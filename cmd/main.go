package main

import (
	"dip-agent/cmd/subcmd"
	"dip-agent/pkg/agent"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/signals"
	"dip-agent/pkg/core/sysconfig"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/exec"
	_ "dip-agent/pkg/include"
	"dip-agent/pkg/monitor"
	"dip-agent/pkg/persistence"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/task"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/yaml"
	"flag"
	"fmt"
	"net"
	"net/http"
	_ "net/http/pprof"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/automaxprocs/maxprocs"
)

var (
	globalConfigFile string
	configType       string
	nodeName         string

	buildTime string // 编译时间
	gitCommit string // git commit id
)

func init() {
	hostName, _ := os.Hostname()

	flag.StringVar(&globalConfigFile, "config.system", "agent.yml", "global config file")
	flag.StringVar(&configType, "config.from", "file", "config from file or env")
	flag.StringVar(&nodeName, "meta.nodeName", hostName, "override nodeName")
}

func main() {
	flag.Parse()
	// init log configuration
	log.InitDefaultLogger()

	if err := subcmd.SwitchSubCommand(); err != nil {
		return
	}

	log.Info("agent version: %s", api.VERSION)
	log.Info("agent build time: %s", buildTime)
	log.Info("agent git commit: %s", gitCommit)

	api.BuildTime = buildTime
	api.CommitId = gitCommit

	// 获取当前进程的PID
	pid := os.Getpid()
	api.Pid = pid
	log.Info("agent pid: %d", pid)

	// 获取当前进程的node name
	api.NodeName = nodeName
	log.Info("node name: %s", nodeName)

	// 获取本机ip
	ip, err := util.GetLocalIP()
	if err != nil {
		log.Warn("get local ip failed: %s", err)
	} else {
		api.NodeIp = ip
		log.Info("node ip: %s", ip)
	}

	// 初始化环境变量
	initEnv()
	log.Info("current env: %s", api.CurrentEnv)
	// 初始化engine目录
	initEngineDir()

	// set up signals, so we handle the first shutdown signal gracefully
	stopCh := signals.SetupSignalHandler()

	// Automatically set GOMAXPROCS to match Linux container CPU quota
	if _, err := maxprocs.Set(maxprocs.Logger(log.Debug)); err != nil {
		log.Fatal("set maxprocs error: %v", err)
	}
	log.Info("real GOMAXPROCS %d", runtime.GOMAXPROCS(-1))

	// system config file
	syscfg := sysconfig.Config{}
	cfg.UnpackTypeDefaultsAndValidate(strings.ToLower(configType), globalConfigFile, &syscfg)

	if syscfg.Agent.JSONEngine != "" {
		out, yamlErr := yaml.Marshal(syscfg)
		if yamlErr != nil {
			log.Fatal("marshal syscfg failed: %v, config:\n%s", yamlErr, out)
		}
		log.Info("system config: \n%s", out)
	}
	// init persistence
	persistence.Init(syscfg.Persistence)
	// init exec manage
	exec.Init()
	// register jsonEngine
	json.SetDefaultEngine(syscfg.Agent.JSONEngine)
	// init engine base info
	engine.InitFormEnv(syscfg.EngineEnvKey)
	log.Info("engine domain: %s", engine.Domain())
	// 初始化引擎二进制版本
	engineBinaryVersions, err := engine.InitEngineBinaryVersions()
	if err != nil {
		log.Warn("init engine binary versions failed: %s", err)
	} else {
		log.Info("engine binary versions: %v", engineBinaryVersions)
		api.EngineBinaryVersion = engineBinaryVersions
	}
	// start eventBus listeners
	//eventbus.StartAndRun(syscfg.Agent.MonitorEventBus)
	// init log after error func
	//log.AfterError = eventbus.AfterErrorFuc

	task.Start(&task.TasksConfig{Tasks: syscfg.Tasks})

	// init web handlers
	web.Init()

	// start http server
	if syscfg.Agent.Http.Enabled {
		go func() {
			listener, err := net.Listen("tcp", fmt.Sprintf("%s:%d", syscfg.Agent.Http.Host, syscfg.Agent.Http.Port))
			if err != nil {
				log.Fatal("http listen err: %v", err)
			}

			log.Info("http listen addr %s", listener.Addr().String())
			if err = http.Serve(listener, nil); err != nil {
				log.Fatal("http serve err: %v", err)
			}
		}()
	}

	// agent 上报自身信息
	agent.Report()

	// 监控日志文件
	monitorLogfile()

	// 拉起agent-monitor
	monitor.StartMonitor()

	log.Info("started agent")
	<-stopCh
	log.Info("shutting down agent")
}

func initEnv() {
	// 环境变量优先获取ENV, ENV_GROUP无法区分国内环境和新加坡环境
	// 对应关系:
	// 	csprd|prd -> 国内生产环境
	// 	pre -> 国内预发环境
	// 	test|d1 -> 海外测试环境
	//  pre-singapore-global -> 新加坡预发环境(http://pre-dip-brain-sgp.poizon.com)
	//  prd-singapore-global -> 新加坡生产环境(http://dip-brain-sgp.poizon.com)
	// 新增环境变量映射
	envMap := map[string]struct {
		backendDomain       string
		brainDomain         string
		ossPoint            string
		ossBucket           string
		agentMonitorOssPath string
	}{
		// ############################## 国内生产环境 ##############################
		"csprd": {
			backendDomain:       "http://dip-backend.shizhuang-inc.com",
			brainDomain:         "http://dip-brain.shizhuang-inc.com",
			ossPoint:            "http://oss-cn-hangzhou-internal.aliyuncs.com",
			ossBucket:           "algo-recommend",
			agentMonitorOssPath: "dip_agent_monitor/prd/dip-agent-monitor.sh",
		},
		"prd": {
			backendDomain:       "http://dip-backend.shizhuang-inc.com",
			brainDomain:         "http://dip-brain.shizhuang-inc.com",
			ossPoint:            "http://oss-cn-hangzhou-internal.aliyuncs.com",
			ossBucket:           "algo-recommend",
			agentMonitorOssPath: "dip_agent_monitor/prd/dip-agent-monitor.sh",
		},
		// ############################## 国内预发环境 ##############################
		"pre": {
			backendDomain:       "http://pre-dip-backend.shizhuang-inc.com",
			brainDomain:         "http://pre-dip-brain.shizhuang-inc.com",
			ossPoint:            "http://oss-cn-hangzhou-internal.aliyuncs.com",
			ossBucket:           "algo-recommend",
			agentMonitorOssPath: "dip_agent_monitor/pre/dip-agent-monitor.sh",
		},
		// ############################## 国内开发环境 ##############################
		"test": {
			backendDomain:       "http://d1-dip-backend.shizhuang-inc.net",
			brainDomain:         "http://d1-dip-brain.shizhuang-inc.net",
			ossPoint:            "http://oss-cn-hangzhou-internal.aliyuncs.com",
			ossBucket:           "algo-recommend",
			agentMonitorOssPath: "dip_agent_monitor/test/dip-agent-monitor.sh",
		},
		"d1": {
			backendDomain:       "http://d1-dip-backend.shizhuang-inc.net",
			brainDomain:         "http://d1-dip-brain.shizhuang-inc.net",
			ossPoint:            "http://oss-cn-hangzhou-internal.aliyuncs.com",
			ossBucket:           "algo-recommend",
			agentMonitorOssPath: "dip_agent_monitor/test/dip-agent-monitor.sh",
		},
		// ############################## 新加坡预发环境 ##############################
		"pre-singapore-global": {
			backendDomain:       "http://pre-dip-backend-sgp.poizon.com",
			brainDomain:         "http://pre-dip-brain-sgp.poizon.com",
			ossPoint:            "http://oss-ap-southeast-1-internal.aliyuncs.com",
			ossBucket:           "algo-recommend-new-sg",
			agentMonitorOssPath: "dip_agent_monitor/sgp-pre/dip-agent-monitor.sh",
		},
		// ############################## 新加坡生产环境 ##############################
		"prd-singapore-global": {
			backendDomain:       "http://dip-backend-sgp.poizon.com",
			brainDomain:         "http://dip-brain-sgp.poizon.com",
			ossPoint:            "http://oss-ap-southeast-1-internal.aliyuncs.com",
			ossBucket:           "algo-recommend-new-sg",
			agentMonitorOssPath: "dip_agent_monitor/sgp-prd/dip-agent-monitor.sh",
		},
	}

	// 获取环境变量ENV, 以此区分是否新加坡环境. 目前索引平台只有国内和新加坡
	env := os.Getenv("ENV")
	if env == "" {
		log.Info("ENV is empty, try to get ENV_GROUP")
		env = os.Getenv("ENV_GROUP")
	}
	if env == "" {
		log.Panic("ENV and ENV_GROUP is empty")
	}
	log.Info("init env: %s", env)

	// 非新加坡环境, env直接使用ENV_GROUP
	if !strings.Contains(env, "singapore") {
		env = os.Getenv("ENV_GROUP")
		log.Info("env is not singapore env, use ENV_GROUP: %s", env)
	}
	log.Info("final env: %s", env)

	// 设置环境变量
	ossAccessPoint := os.Getenv("OSS_ACCESS_POINT")
	if envInfo, ok := envMap[env]; ok {
		api.BackendDomain = envInfo.backendDomain
		api.BrainDomain = envInfo.brainDomain
		api.OssEndPoint = envInfo.ossPoint
		api.OssBucket = envInfo.ossBucket
		api.AgentMonitorOssPath = envInfo.agentMonitorOssPath

		if ossAccessPoint == "" {
			ossAccessPoint = envInfo.ossPoint
		}
	} else {
		log.Panic("ENV is invalid: %s", env)
	}
	log.Info("backend domain: %s", api.BackendDomain)
	log.Info("brain domain: %s", api.BrainDomain)
	log.Info("oss access point: %s", ossAccessPoint)

	api.CurrentEnv = api.Env(env)

	// 设置环境变量
	if api.BackendDomain != "" {
		os.Setenv("CONTROL_SERVER", api.BackendDomain)
	}
	if ossAccessPoint != "" {
		os.Setenv("OSS_ACCESS_POINT", ossAccessPoint)
		api.OssEndPoint = ossAccessPoint
	}

	// clusterName & clusterGroup
	clusterCode := os.Getenv("APP_NAME")
	if clusterCode == "" {
		log.Warn("env APP_NAME is empty")
	}
	api.ClusterCode = clusterCode
	clusterGroup := os.Getenv("GROUP_NAME")
	if clusterGroup == "" {
		log.Warn("env GROUP_NAME is empty")
	}
	api.ClusterGroup = clusterGroup
}

func initEngineDir() {
	// 获取环境变量: SEARCH_REL
	searchRel := os.Getenv("SEARCH_REL")
	if searchRel == "" {
		log.Warn("SEARCH_REL is empty")
		// 设置默认值
		searchRel = "/app/rel"
		err := os.Setenv("SEARCH_REL", searchRel)
		if err != nil {
			log.Info("set SEARCH_REL failed: %s", err)
			return
		}
	}
	api.EngineBaseDir = searchRel
	log.Info("SEARCH_REL: %s", searchRel)
	// 创建几个原有engine需要的目录
	dirs := []string{
		filepath.Join(searchRel, "data", "gz"),
		filepath.Join(searchRel, "data", "table"),
		filepath.Join(searchRel, "data", "model"),
		filepath.Join(searchRel, "data", "cdb"),
		filepath.Join(searchRel, "data", "tdata"),
	}
	engineAppName := os.Getenv("APP_NAME")
	if engineAppName == "" {
		log.Warn("APP_NAME is empty")
	} else {
		dirs = append(dirs, filepath.Join("/logs", engineAppName))
	}
	err := util.CreateDirs(dirs, false)
	if err != nil {
		log.Warn("create engine dirs failed: %s", err)
	}
}

// 线上发现日志文件被删除, 增加日志文件监控
func monitorLogfile() {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Error("new os notify failed: %s", err)
		return
	}
	//err = watcher.Add("/Users/<USER>/soft/test")
	err = watcher.Add("/logs/agent")
	if err != nil {
		log.Error("watcher add failed: %s", err)
		return
	}

	defer func() {
		log.Info("monitor log file start")
	}()

	go func() {
		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}
				if event.Op == fsnotify.Remove {
					//log.Info("log file deleted: %s", event.Name)
					fileName, err := filepath.Abs(event.Name)
					if err != nil {
						log.Error("get abs path failed: %s", err)
						continue
					}
					if fileName == "/logs/agent/dip-agent.log" || fileName == "/logs/agent/std.log" {
						errMsg := fmt.Sprintf("log file deleted: %s", fileName)
						log.Warn(errMsg)
						// 发送报警
						wr := platform.NewWarnRequestWithMsg("log-monitor", errMsg)
						_, err := wr.Send()
						if err != nil {
							log.Error("send warn failed: %s", err)
						}
					}
				}
			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				log.Error("watcher error: %s", err)
			}
		}
	}()
}
