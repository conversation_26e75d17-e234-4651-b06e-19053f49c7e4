#!/usr/bin/python
# coding=utf-8

import getopt
import os
import subprocess
import sys
import time

# 打印日志到日志文件: /logs/agent/dip-agent.log
log_dir = "/logs/agent"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = log_dir + "/dip-agent.log"


# 日志函数
def log(msg):
    # 追加写入日志
    with open(log_file, "a") as f:
        # 添加时间前缀, 文件名称
        msg = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) + " restart.py > " + msg
        f.write(msg + "\n")

# 使用独立进程执行命令
def run_cmd(cmd):
    # 启动进程
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT)
    # 不等待进程结束
    log("Process started, PID:{}".format(process.pid))


# 重新启动后agent
def restart_agent(pid, local_dir="/app/rel/agent/", remote_agent_name="dip-agent"):
    start_time = time.time()

    # agent文件路径
    local_agent_path = local_dir + remote_agent_name

    # 检查agent文件是否存在
    if not os.path.exists(local_agent_path):
        raise Exception("agent file not exist: %s" % local_agent_path)

    # 赋予agent文件执行权限
    s = os.system("chmod +x " + local_agent_path)
    if s != 0:
        raise Exception("chmod agent error: %s" % s)

    # 如果dip-agent进程存在，kill掉
    s = os.system("kill -9 " + pid)
    if s != 0:
        log("kill agent error: %s" % s)

    # 等待agent进程退出
    sleep_time = 3
    time.sleep(sleep_time)

    # 确认agent日志目录存在
    log_dir = "/logs/agent"
    # if not os.path.exists(log_dir):
    #     os.makedirs(log_dir)
    # 拼接agent启动命令
    cmd = ("nohup " + local_agent_path + " -log.enableFile=true -log.enableStdout=false -log.noColor=true -log.dir=" + log_dir)
    # cmd += " &"
    # 增加标准输出重定向到std.log
    cmd += " >> /logs/agent/std.log 2>&1 &"
    log("start agent cmd: %s" % cmd)
    # 启动agent
    run_cmd(cmd)
    # r = os.system(cmd)
    # if r != 0:
    #     log("start agent error: %s" % r)
    #     raise Exception("start agent error: %s" % r)
    log("start agent success. cost: %s" % (time.time() - start_time))


if __name__ == "__main__":
    opts, args = getopt.getopt(sys.argv[1:], "p:")
    pid = None
    for op, value in opts:
        if op == "-p":
            pid = value
    restart_agent(pid)
