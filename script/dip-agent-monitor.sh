#!/bin/bash
###
# @Author: luota<PERSON>
# @Date: 2025-06-05 20:28:18
# @LastEditors: luotao
# @LastEditTime: 2025-06-05 20:28:18
# @Description: dip-agent进程监控与自动重启脚本
# @File: /app/rel/agent/script/dip-agent-monitor.sh
# @Start: nohup /usr/bin/bash /app/rel/script/dip-agent-monitor.sh &
###

AGENT_NAME="dip-agent"
AGENT_PATH="/app/rel/agent/$AGENT_NAME"
DIP_AGENT_WATCHDOG_LOG_FILE="/logs/agent/monitor.log"
RESET_INTERVAL=$((12*2*60)) # 每次sleep 30秒，大约12小时重置一次
POD_IP=$(hostname -I | awk '{print $1}')
DIP_AGENT_WATCHDOG_NAME="dip-agent-monitor.sh"
DIP_AGENT_READINESS_CHECK_URL="http://localhost:9188/api/v1/ops/health/readiness"
DOE_SERVER_BINARY_PATH="/app/rel/bin/doe-server"
DOE_SERVER_COREDUMP_DIR="/app/rel"
DOE_SERVER_ALERT_COOLDOWN=$((30 * 60)) # 静默期30分钟
DOE_SERVER_LAST_ALERT_TIMESTAMP=0

function echo_log() {
    local cur_dateTime=$(date +"%Y-%m-%d-%H:%M:%S-%s")
    echo "${cur_dateTime}:${BASH_LINENO[0]}::$0: $1" | tee -a ${DIP_AGENT_WATCHDOG_LOG_FILE}
}

function send_feishu_alarm() {
    local msg=$1
    local FEISHU_URL="https://lark-center-o.shizhuang-inc.com/open-apis/bot/v2/hook/ae90a319-69d9-48cf-9107-81ff7e378a30"
    [ "$ENV_GROUP" != "prd" ] && FEISHU_URL="https://lark-center-o.shizhuang-inc.com/open-apis/bot/v2/hook/9309b4d5-6ed9-4fd3-91e9-551a75b9962c"

    echo_log "发送飞书告警请求内容: $msg"
    local response=$(curl -s -w "HTTP_STATUS:%{http_code}" -X POST -H "Content-Type: application/json" -d "$msg" "$FEISHU_URL")
    local http_body=${response%HTTP_STATUS:*}
    local http_status=${response##*HTTP_STATUS:}
    echo_log "飞书告警响应状态: $http_status, 响应内容: $http_body"
}

function send_platform_alarm() {
    local platform_message=$1
    local platform_alarm_info='{
        "clusterCode": "'$APP_NAME'",
        "clusterGroup": "'$GROUP_NAME'",
        "podIp": "'$POD_IP'",
        "eventMsg": "'$platform_message'"
    }'
    local platform_url="$CONTROL_SERVER/tool/sendWarnMsg"
    curl -X POST -H "Content-Type: application/json" -d "$platform_alarm_info" "$platform_url"
}

function get_doe_server_core_dump_stack() {
    local latest_core=$(ls -tr "$DOE_SERVER_COREDUMP_DIR" | grep "^core" | tail -n1)
    if [[ -f "$DOE_SERVER_COREDUMP_DIR/$latest_core" ]]; then
        local core_file="$DOE_SERVER_COREDUMP_DIR/$latest_core"
        local core_info=()
        core_info+=("$latest_core")

        # 使用gdb提取单线程堆栈信息，只保留以#开头的调用栈行
        while IFS= read -r line; do
            core_info+=("$line")
        done < <(gdb -q -ex "set confirm off" -ex "bt" -ex "quit" --core="$core_file" "$DOE_SERVER_BINARY_PATH" | grep '^#')

        # 将堆栈数组合并为单个字符串
        local core_info_str=$(printf "%s\n" "${core_info[@]}")

        # 转义堆栈字符串，确保安全插入JSON格式消息
        local escaped_core_info=$(echo "$core_info_str" | sed ':a;N;$!ba;s/\n/\\n/g; s/"/\\"/g; s/'\''\.\([^'\''\\]\{1,\}\)/\1/g')

        local cooldown_minutes=$((DOE_SERVER_ALERT_COOLDOWN / 60))
        local doe_alarm='{
            "msg_type": "interactive",
            "card": {
                "schema": "2.0",
                "body": {"elements":[{"tag":"markdown","content":"```'$escaped_core_info'```"}]},
                "header": {
                    "title":{"tag":"plain_text","content":"【doe-server异常退出堆栈信息】接下来'$cooldown_minutes'分钟内不再发送此告警"},
                    "subtitle":{"tag":"plain_text","content":"app_name:'$APP_NAME' groupName:'$GROUP_NAME' ip:'$POD_IP'"},
                    "template":"red"
                }
            }
        }'
        send_feishu_alarm "$doe_alarm"
    else
        echo_log "未找到doe-server的coredump文件"
    fi
}

function check_dip_agent() {
    PROCESS_FLAG=$(pgrep -f "$AGENT_PATH")
    if [ -z "$PROCESS_FLAG" ]; then
        sleep 10
        RETRY_FLAG=$(pgrep -f "$AGENT_PATH")
        if [ ! -z "$RETRY_FLAG" ]; then
            echo_log "dip-agent未彻底关闭，继续监控"
            return
        fi

        LOG_PATH="/logs/agent/std.log"
        MAX_CHAR=2000

        if [ -f "$LOG_PATH" ]; then
            LOG_TAIL=$(tail -c $MAX_CHAR "$LOG_PATH" | sed '1s/^[^\n]*\n//; s/\([\\"]\)/\\\1/g; s/$/\\n/' | tr -d '\n')
        else
            LOG_TAIL="日志文件不存在或无法读取"
        fi

        local alarm_info='{
            "msg_type": "interactive",
            "card": {
                "schema": "2.0",
                "body": {"elements":[{"tag":"markdown","content":"```'$LOG_TAIL'```"}]},
                "header": {
                    "title":{"tag":"plain_text","content":"【dip-agent进程异常退出】自动拉起中"},
                    "subtitle":{"tag":"plain_text","content":"app_name:'$APP_NAME' groupName:'$GROUP_NAME' ip:'$POD_IP'"},
                    "template":"red"
                }
            }
        }'

        send_feishu_alarm "$alarm_info"
        send_platform_alarm "[dip-agent 进程异常退出] 自动拉起中，appName:$APP_NAME groupName:$GROUP_NAME ip:$POD_IP"

        setsid nohup $AGENT_PATH -log.enableFile=true -log.enableStdout=false -log.noColor=true -log.dir=/logs/agent >> /logs/agent/std.log 2>&1 &
        sleep 20

        NEW_PROCESS_FLAG=$(pgrep -f "$AGENT_PATH")
        local recovery_status
        local template_color
        if [ ! -z "$NEW_PROCESS_FLAG" ]; then
            echo_log "dip-agent进程已拉起成功"
            recovery_status="dip-agent进程已拉起成功"
            template_color="green"
        else
            echo_log "dip-agent重启失败！"
            recovery_status="dip-agent进程重启失败，请立即检查！"
            template_color="red"
        fi

        local recovery_alarm_info='{
            "msg_type": "interactive",
            "card": {
                "schema": "2.0",
                "body": {"elements":[{"tag":"markdown","content":"'$recovery_status' appName: '$APP_NAME', groupName: '$GROUP_NAME', ip: '$POD_IP'"}]},
                "header": {
                    "title":{"tag":"plain_text","content":"【'$recovery_status'】"},
                    "template":"'$template_color'"
                }
            }
        }'
        send_feishu_alarm "$recovery_alarm_info"
    fi
}

function check_doe_server() {
    curl -s --connect-timeout 5 "$DIP_AGENT_READINESS_CHECK_URL" >/dev/null
    if [ $? -ne 0 ]; then
        echo_log "Readiness接口不可达，跳过doe-server检查"
        return
    fi

    local http_status=$(curl -s -o /dev/null -w "%{http_code}" "$DIP_AGENT_READINESS_CHECK_URL")
    local CURRENT_TIMESTAMP=$(date +%s)
    local TIME_SINCE_LAST_ALERT=$((CURRENT_TIMESTAMP - DOE_SERVER_LAST_ALERT_TIMESTAMP))

    if [ "$http_status" != "200" ]; then
        echo_log "Readiness检查返回非200状态: $http_status"
        local PID=$(pgrep -f "$DOE_SERVER_BINARY_PATH")
        if [ -z "$PID" ]; then
            echo_log "doe-server进程不存在"
            if [ $TIME_SINCE_LAST_ALERT -ge $DOE_SERVER_ALERT_COOLDOWN ]; then
                echo_log "doe-server进程未运行且告警静默期已过，开始获取coredump信息"
                get_doe_server_core_dump_stack
                DOE_SERVER_LAST_ALERT_TIMESTAMP=$CURRENT_TIMESTAMP
            else
                echo_log "doe-server告警静默中，跳过本次告警，距离上次告警时间间隔: $TIME_SINCE_LAST_ALERT 秒"
            fi
        else
            echo_log "doe-server进程存在 (PID: $PID)，但服务状态异常"
        fi
    fi
}

# 防止重复运行脚本
if [ $(pgrep -fc "$DIP_AGENT_WATCHDOG_NAME") -gt 1 ]; then
    echo_log "脚本已运行，退出本次启动。"
    exit 1
fi

echo_log "dip-agent-monitor start work.."

count=1
while true; do
    count=$((count + 1))
    if [ $count -ge $RESET_INTERVAL ]; then
        count=1
        echo_log "周期重置"
    fi

    check_doe_server
    check_dip_agent

    sleep 30
done