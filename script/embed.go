package script

import "embed"

//go:embed test.py
var testScript embed.FS

//go:embed restart.py
var restartScript embed.FS

//go:embed dip-agent-monitor.sh
var agentMonitorScript embed.FS

// GetTestScript 获取测试脚本
func GetTestScript() ([]byte, error) {
	return testScript.ReadFile("test.py")
}

// GetRestartScript 获取重启脚本
func GetRestartScript() ([]byte, error) {
	return restartScript.ReadFile("restart.py")
}

// GetAgentMonitorScript 获取监控脚本
func GetAgentMonitorScript() ([]byte, error) {
	return agentMonitorScript.ReadFile("dip-agent-monitor.sh")
}
