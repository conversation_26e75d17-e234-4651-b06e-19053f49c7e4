# dip-agent

dw index platform agent

## protobuf 生成代码

```shell
# 生成schema
protoc --go_out=. --go_opt=paths=source_relative schema.proto
# 生成grpc service
protoc --go-grpc_out=. --go-grpc_opt=paths=source_relative engine/service.proto
```

## 编译

进入到项目主目录, 执行如下命令:

```shell
#GOOS=linux GOARCH=amd64 go build -tags timetzdata -o dip-agent cmd/main.go
# linux
#GOOS=linux GOARCH=amd64 go build -tags timetzdata -ldflags "-X 'main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)' -X main.gitCommit=$(git rev-parse HEAD)" -o dip-agent cmd/main.go
GOOS=linux GOARCH=amd64 go build -tags timetzdata -ldflags "-X 'main.buildTime=$(TZ='Asia/Shanghai' date '+%Y-%m-%d %H:%M:%S')' -X main.gitCommit=$(git rev-parse HEAD)" -o dip-agent cmd/main.go
# mac m芯片
GOOS=darwin GOARCH=arm64 go build -tags timetzdata -ldflags "-X 'main.buildTime=$(TZ='Asia/Shanghai' date '+%Y-%m-%d %H:%M:%S')' -X main.gitCommit=$(git rev-parse HEAD)" -o dip-agent cmd/main.go
# mac intel芯片
GOOS=darwin GOARCH=amd64 go build -tags timetzdata -ldflags "-X 'main.buildTime=$(TZ='Asia/Shanghai' date '+%Y-%m-%d %H:%M:%S')' -X main.gitCommit=$(git rev-parse HEAD)" -o dip-agent.exe cmd/main.go
# windows
GOOS=windows GOARCH=amd64 go build -tags timetzdata -ldflags "-X 'main.buildTime=$(TZ='Asia/Shanghai' date '+%Y-%m-%d %H:%M:%S')' -X main.gitCommit=$(git rev-parse HEAD)" -o dip-agent.exe cmd/main.go
```

## 运行

```shell
chmod +x dip-agent

# 使用默认配置
nohup /app/rel/agent/dip-agent -log.enableFile=true -log.enableStdout=false -log.noColor=true -log.dir=/logs/agent >> /logs/agent/std.log 2>&1 &

# 使用配置文件
nohup /app/rel/agent/dip-agent -log.enableFile=true -log.enableStdout=false -log.noColor=true -log.dir=/logs/agent -config.system=/app/rel/agent/agent.yml >> /logs/agent/std.log 2>&1 &
```

`-config.system=/app/rel/agent/agent.yml`为可选, agent中已经包含所有默认的配置

或者使用csearch-script中的脚本:

```shell
python /app/rel/script/init_agent.py
```

### 本地运行

1. 设置环境变量ENV_GROUP=test
2. ide中直接运行main.go