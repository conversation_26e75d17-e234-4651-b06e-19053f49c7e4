
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Kubernetes Generated files - skip generated files, except for vendored files

!vendor/**/zz_generated.*

# editor and IDE paraphernalia
.idea
*.swp
*.swo
*~
.env
.vscode
pkg/task/data
data
node_modules/
package-lock.json
package.json
dip-agent
cpu.prof
*.zip
ai-commit
vendor
.go_report.json
venv
plugin_exec_result.json

