package pipeline

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/context"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/sort"
	"strings"

	"github.com/pkg/errors"
)

type Pipeline struct {
	done         chan struct{}
	name         string
	epoch        *Epoch
	config       Config
	r            *RegisterCenter
	info         Info
	processChain InvokerFunc
	Running      bool
}

func NewPipeline(pipelineConfig *Config) *Pipeline {
	registerCenter := NewRegisterCenter()
	name := pipelineConfig.Name
	epoch := NewEpoch(name)
	return &Pipeline{
		done:   make(chan struct{}),
		name:   name,
		config: *pipelineConfig,
		epoch:  epoch,
		r:      registerCenter,
		info: Info{
			Stop:         false,
			PipelineName: name,
			Epoch:        epoch,
			R:            registerCenter,
		},
	}
}

func (p *Pipeline) Name() string {
	return p.name
}

func (p *Pipeline) Start() error {
	p.init()

	// start interceptor
	interceptors, err := p.startInterceptor()
	if err != nil {
		return err
	}

	// start processor
	processors, err := p.startProcessor()
	if err != nil {
		return err
	}

	// build interceptor chain
	processorInterceptorChainFunc := p.buildInterceptorChainFunc(interceptors)
	// build process chain
	processInvokerFunc := p.buildProcessChain(processorInterceptorChainFunc, processors)
	p.processChain = processInvokerFunc
	return nil
}

// Process pipeline, call after Start
func (p *Pipeline) Process(ctx *api.ProcessContext) api.Result {
	if ctx.Carrier == nil {
		ctx.Carrier = make(cfg.CommonCfg)
	}
	return p.processChain(api.Invocation{
		ProcessContext: ctx,
	})
}

func (p *Pipeline) Stop() {
	p.info.Stop = true
	close(p.done)
}

func (p *Pipeline) init() {
	if p.epoch.IsEmpty() {
		p.epoch = NewEpoch(p.name)
	}
	p.epoch.Increase()
}

func (p *Pipeline) startInterceptor() ([]*CompositeInterceptor, error) {
	l := len(p.config.Interceptors)
	if l == 0 {
		return nil, nil
	}
	ps := make([]*CompositeInterceptor, 0, l)
	for _, iConfig := range p.config.Interceptors {
		ctx := context.NewContext(iConfig.Name, api.Type(iConfig.Type), api.INTERCEPTOR, iConfig.Properties)
		err := p.startComponent(ctx)
		if err != nil {
			return nil, errors.WithMessage(err, "start interceptor failed")
		}
		interceptor := p.r.LoadInterceptor(ctx.Type(), ctx.Name())
		if interceptor == nil {
			return nil, errors.Errorf("interceptor %s/%s not found", ctx.Category(), ctx.Type())
		}
		ps = append(ps, NewCompositeInterceptor(interceptor, iConfig))
	}
	return ps, nil
}

func (p *Pipeline) buildInterceptorChainFunc(interceptors []*CompositeInterceptor) ProcessorInterceptorChainFunc {
	l := len(interceptors)
	if l == 0 {
		return simpleProcessorInterceptorChainFunc
	}

	// sort interceptor
	sortableInterceptorWrapper := sort.Sortable[*CompositeInterceptor](interceptors)
	sortableInterceptorWrapper.Sort()

	// log interceptor chain
	var interceptorChainName strings.Builder
	interceptorChainName.WriteString("start->")
	for i := 0; i < l; i++ {
		interceptor := sortableInterceptorWrapper[i]
		if !interceptor.Enable() {
			log.Info("[%s]pipeline interceptor[%s] is disabled", p.name, interceptor.String())
			continue
		}
		interceptorChainName.WriteString(interceptor.String())
		interceptorChainName.WriteString("->")
	}
	interceptorChainName.WriteString("end")
	log.Info("[%s]pipeline interceptor chain: %s", p.name, interceptorChainName.String())

	// process chain func
	return func(processor api.Processor) api.Invoker {
		var last api.Invoker = NewProcessInvoker(processor)

		// build interceptor chain
		for i := 0; i < l; i++ {
			tempInterceptor := sortableInterceptorWrapper[l-1-i]
			if !tempInterceptor.Enable() {
				continue
			}
			next := last
			last = &AbstractInvoker{
				Name: tempInterceptor.String(),
				DoInvoke: func(invocation api.Invocation) api.Result {
					return tempInterceptor.Intercept(next, invocation)
				},
			}
		}

		return last
	}
}

func (p *Pipeline) startProcessor() ([]*CompositeProcessor, error) {
	l := len(p.config.Processors)
	ps := make([]*CompositeProcessor, 0, l)
	for _, pConfig := range p.config.Processors {
		ctx := context.NewContext(pConfig.Name, api.Type(pConfig.Type), api.PROCESSOR, pConfig.Properties)
		err := p.startComponent(ctx)
		if err != nil {
			return nil, errors.WithMessage(err, "start processor failed")
		}
		processor := p.r.LoadProcessor(ctx.Type(), ctx.Name())
		if processor == nil {
			return nil, errors.Errorf("processor %s/%s not found", ctx.Category(), ctx.Type())
		}
		ps = append(ps, NewCompositeProcessor(processor, pConfig))
	}
	return ps, nil
}

func (p *Pipeline) buildProcessChain(picFunc ProcessorInterceptorChainFunc, processors []*CompositeProcessor) InvokerFunc {
	l := len(processors)
	if l == 0 {
		log.Warn("task[%s] has no processor", p.name)
		return noopInvokerFunc
	}

	// sort processor
	sortableProcessorWrapper := sort.Sortable[*CompositeProcessor](processors)
	sortableProcessorWrapper.Sort()

	// filter in
	activeProcessor := make([]*CompositeProcessor, 0)

	// log process chain
	var processChainName strings.Builder
	processChainName.WriteString("start->")

	for i := 0; i < l; i++ {
		processor := sortableProcessorWrapper[i]

		if !processor.Enable() {
			log.Info("processor[%s] is disabled", processor.String())
			continue
		}

		activeProcessor = append(activeProcessor, processor)

		processChainName.WriteString(processor.String())
		processChainName.WriteString("->")
	}

	processChainName.WriteString("end")
	log.Info("[%s]pipeline process chain: %s", p.name, processChainName.String())

	apl := len(activeProcessor)
	if apl == 0 {
		log.Error("task[%s] has no active processor", p.name)
		return noopInvokerFunc
	}

	// build process chain
	return func(invocation api.Invocation) api.Result {
		var r api.Result

		// process chain
		for i := 0; i < apl; i++ {
			index := i
			processor := activeProcessor[index]
			// 填充processor元信息
			invocation.ProcessorName = processor.Name()
			invocation.ProcessorIndex = index
			invocation.ProcessorStepProgress = processor.Config.StepProgress
			invocation.CanStop = processor.Config.CanStop
			// 是否可以跳过，默认true
			invocation.CanSkip = processor.Config.CanSkip == nil || *processor.Config.CanSkip
			// 织入processor拦截器链
			invoker := picFunc(processor.Processor)
			r = invoker.Invoke(invocation)
			if r.Status().ShouldBreak() {
				if r.Status().IsFail() {
					log.Error("[%s]processor[%s] process fail: %s. error detail: %s", invocation.RequestId, processor.String(), r.Message(), r.Error().String())
				}
				break
			}
		}
		return r
	}
}

func (p *Pipeline) startComponent(ctx api.Context) error {
	component, err := GetWithType(ctx.Category(), ctx.Type(), p.info)
	if err != nil {
		return err
	}
	if err = p.startWithComponent(component, ctx); err != nil {
		return err
	}
	return nil
}

func (p *Pipeline) startWithComponent(component api.Component, ctx api.Context) error {
	// unpack config from properties
	err := cfg.UnpackFromCommonCfgWithYaml(ctx.Properties(), component.Config()).DefaultAndValidate()
	if err != nil {
		return errors.WithMessagef(err, "unpack component %s/%s", component.Category(), component.Type())
	}

	err = component.Init(ctx)
	if err != nil {
		return errors.WithMessagef(err, "init component %s/%s", component.Category(), component.Type())
	}

	err = component.Start()
	if err != nil {
		return errors.WithMessagef(err, "start component %s/%s", component.Category(), component.Type())
	}

	err = p.r.Register(ctx.Name(), component)
	if err != nil {
		return err
	}
	return nil
}
