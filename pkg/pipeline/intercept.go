package pipeline

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/intercept"
)

// CompositeInterceptor 组合Interceptor和基础配置, 例如Order\Enable等
type CompositeInterceptor struct {
	Interceptor api.Interceptor
	Config      *intercept.Config
}

func NewCompositeInterceptor(interceptor api.Interceptor, config *intercept.Config) *CompositeInterceptor {
	return &CompositeInterceptor{
		Interceptor: interceptor,
		Config:      config,
	}
}

func (c *CompositeInterceptor) Enable() bool {
	return c.Config.Enable
}

func (c *CompositeInterceptor) Order() int {
	return c.Config.Order
}

// String 返回Interceptor的字符串表示
func (c *CompositeInterceptor) String() string {
	return c.Interceptor.String()
}

// Intercept 拦截处理
func (c *CompositeInterceptor) Intercept(invoker api.Invoker, invocation api.Invocation) api.Result {
	return c.Interceptor.Intercept(invoker, invocation)
}

// ProcessorInterceptorChainFunc Processor拦截器链构造函数
type ProcessorInterceptorChainFunc func(processor api.Processor) api.Invoker

var simpleProcessorInterceptorChainFunc = func(processor api.Processor) api.Invoker {
	return NewProcessInvoker(processor)
}
