package monitor

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/oss"
	"dip-agent/script"
	"fmt"
	"github.com/pkg/errors"
	"path/filepath"
)

const (
	monitorScriptName = "dip-agent-monitor.sh"
)

func StopMonitor() {
	// 获取agent-monitor的pid
	pid, err := util.GetPID(monitorScriptName)
	if err != nil {
		log.Error("get agent-monitor pid fail: %s", err)
		return
	}
	// 执行脚本: kill pid
	clue := "stop-agent-monitor"
	cmdArgs := []string{"-9", fmt.Sprintf("%d", pid)}
	_, err = util.ExecuteScript(clue, "kill", cmdArgs)
	if err != nil {
		log.Error("stop agent-monitor fail: %s", err)
		return
	}
	log.Info("agent-monitor stopped")
	api.AgentMonitorPid = 0
}

func StartMonitor() {
	// 当前是否存在agent-monitor, 使用ps命令判断
	pid, err := util.GetPID(monitorScriptName)
	if err != nil {
		log.Warn("get agent-monitor pid fail: %s", err)
		log.Info("agent-monitor not running, start agent-monitor...")
	} else {
		log.Info("agent-monitor already running. pid: %d", pid)
		api.AgentMonitorPid = pid
		return
	}

	// 本地agent-monitor脚本路径
	localAgentMonitorFile := filepath.Join(api.EngineBaseDir, "/agent/script/", monitorScriptName)

	// 初始化agent-monitor脚本
	err = initAgentMonitorScript(localAgentMonitorFile)
	if err != nil {
		log.Error("init agent-monitor script fail: %s", err)
		return
	}

	// 执行脚本: nohup /usr/bin/bash /app/rel/agent/script/dip-agent-monitor.sh > /dev/null 2>&1 &
	clue := "start-agent-monitor"
	cmdArgs := fmt.Sprintf("nohup /usr/bin/bash %s > /dev/null 2>&1 &", localAgentMonitorFile)
	_, err = util.ExecuteScript(clue, "sh", []string{"-c", cmdArgs})
	if err != nil {
		log.Error("start agent-monitor fail: %s", err)
		return
	}

	// 获取agent-monitor的pid
	pid, err = util.GetPID(monitorScriptName)
	if err != nil {
		log.Error("get agent-monitor pid fail: %s", err)
		return
	}
	api.AgentMonitorPid = pid
	log.Info("agent-monitor started. pid: %d", pid)
}

func initAgentMonitorScript(localAgentMonitorFile string) error {
	// 尝试从oss拉取agent-monitor脚本
	err := downloadForOss(localAgentMonitorFile)
	if err != nil {
		log.Error("download agent-monitor script fail: %s", err)
		// 拉取失败则使用本地./script/dip-agent-monitor.sh
		log.Info("use local agent-monitor script")
		err = copyToLocal(localAgentMonitorFile)
		if err != nil {
			log.Error("init agent-monitor script fail: %s", err)
			return err
		}
	}
	// 设置可执行权限
	err = util.MakeFileExecutable(localAgentMonitorFile)
	if err != nil {
		log.Error("make agent-monitor script executable fail: %s", err)
		return err
	}
	return nil
}

func downloadForOss(localFile string) error {
	// 尝试从oss拉取agent-monitor脚本
	ossConfig, err := platform.GetOssConfig()
	if err != nil {
		return err
	}
	ossClient, err := oss.NewClient(api.OssEndPoint, api.OssBucket, ossConfig.OssKey, ossConfig.KeySecret)
	if err != nil {
		return err
	}
	err = ossClient.DownloadFile(api.AgentMonitorOssPath, localFile)
	if err != nil {
		log.Error("download agent-monitor script fail: %s", err)
		return err
	}
	return nil
}

func copyToLocal(localFile string) error {
	// 创建本地文件
	agentMonitorFile, err := util.CreateFileAndReturn(localFile)
	if err != nil {
		return errors.WithMessagef(err, "create agent-monitor file fail: %s", localFile)
	}
	// 写入本地文件
	agentMonitorScriptContent, err := script.GetAgentMonitorScript()
	if err != nil {
		err = errors.WithMessage(err, "get agent-monitor script fail")
		return err
	}
	_, err = agentMonitorFile.Write(agentMonitorScriptContent)
	if err != nil {
		err = errors.WithMessage(err, "write agent-monitor script to local file fail")
		return err
	}
	agentMonitorFile.Close()
	return nil
}
