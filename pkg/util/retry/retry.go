package retry

import (
	"dip-agent/pkg/core/log"
	"time"
)

const defaultMaxRetries = 8

type RetryableFunc func() error

// Strategy defines the interface for retry strategies
type Strategy interface {
	ShouldRetry(error) bool
	NextDelay() time.Duration
}

type BasicStrategy struct {
	Clue               string
	MaxRetries         int
	BaseDelay          time.Duration
	MaxDelay           time.Duration
	Multiplier         float64 // 指数退避的倍数
	NonRetryableErrors map[error]bool
	currentRetryCount  int
}

func (s *BasicStrategy) ShouldRetry(err error) bool {
	if len(s.NonRetryableErrors) >= 0 {
		if _, exists := s.NonRetryableErrors[err]; exists {
			return false
		}
	}

	if s.currentRetryCount >= s.MaxRetries {
		return false
	}
	s.currentRetryCount++
	log.Info("[%s]retry times: %d/%d, error: %v", s.Clue, s.currentRetryCount, s.MaxRetries, err)
	return true
}

func (s *BasicStrategy) NextDelay() time.Duration {
	if s.currentRetryCount == 1 {
		return s.BaseDelay
	}
	delay := time.Duration(float64(s.BaseDelay) * s.Multiplier * float64(s.currentRetryCount-1))
	if s.MaxDelay > 0 && delay > s.MaxDelay {
		delay = s.MaxDelay
	}
	return delay
}

func DoRetry(retryFunc RetryableFunc) error {
	return DoRetryWithClue(retryFunc, "")
}

func DoRetryWithClue(retryFunc RetryableFunc, clue string) error {
	return DoRetryWithStrategy(retryFunc, &BasicStrategy{
		Clue:               clue,
		MaxRetries:         3,
		BaseDelay:          2 * time.Second,
		MaxDelay:           1 * time.Minute,
		Multiplier:         2,
		NonRetryableErrors: make(map[error]bool),
	})
}

func DoRetryWithMaxRetries(retryFunc RetryableFunc, maxRetries int, delay time.Duration, clue string) error {
	return DoRetryWithStrategy(retryFunc, &BasicStrategy{
		Clue:               clue,
		MaxRetries:         maxRetries,
		BaseDelay:          delay,
		MaxDelay:           10 * time.Minute,
		Multiplier:         2,
		NonRetryableErrors: make(map[error]bool),
	})
}

func DoRetryWithStrategy(retryFunc RetryableFunc, strategy Strategy) error {
	var err error
	var i = 0
	for {
		err = retryFunc()
		if err == nil {
			return nil
		}
		if !strategy.ShouldRetry(err) {
			log.Error("All retries failed. Last error: %s", err)
			return err
		}
		i++
		if i >= defaultMaxRetries {
			log.Info("retry %d times over default max retries(%d), error: %v", i, defaultMaxRetries, err)
			return err
		}
		time.Sleep(strategy.NextDelay())
	}
}
