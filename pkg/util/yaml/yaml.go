package yaml

import (
	goccyyaml "github.com/goccy/go-yaml"
	"github.com/pkg/errors"
	"gopkg.in/yaml.v3"
)

func Unmarshal(in []byte, out interface{}) error {
	err := yaml.Unmarshal(in, out)
	if err != nil {
		return errors.Wrap(err, "failed to unmarshal yaml")
	}
	return nil
}

func UnmarshalWithPrettyError(in []byte, out interface{}) error {
	err := Unmarshal(in, out)
	if err != nil {
		prettyErr := goccyyaml.Unmarshal(in, out)
		if prettyErr != nil {
			err = errors.Wrapf(prettyErr, "failed to unmarshal yaml: %s", err)
		}
	}
	return nil
}

func Marshal(in interface{}) (out []byte, err error) {
	content, err := yaml.Marshal(in)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal yaml")
	}
	return content, err
}
