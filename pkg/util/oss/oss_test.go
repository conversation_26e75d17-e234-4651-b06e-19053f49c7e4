package oss

import (
	"dip-agent/pkg/controller/platform"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/util/crypto/base64"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"dip-agent/pkg/util/json/sonic"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"testing"
)

func TestClient_List(t *testing.T) {
	type fields struct {
		endpoint   string
		bucketName string
		accessKey  string
		secretKey  string
		provider   *oss.EnvironmentVariableCredentialsProvider
	}
	type args struct {
		sourcePath string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "case1",
			fields: fields{
				//endpoint:   "http://oss-cn-hangzhou-internal.aliyuncs.com",
				endpoint:   "http://oss-cn-hangzhou.aliyuncs.com",
				bucketName: "algo-recommend",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				sourcePath: "dip_new/dip_all_type_field_gqw2/2025-05-14/cengine/pre/1/default/1/0/",
			},
			want:    make([]string, 0),
			wantErr: false,
		},
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 先解密
			if tt.fields.secretKey != "" {
				secretKey, err := base64.Decrypt(tt.fields.secretKey)
				if err != nil {
					t.Errorf("base64.Decode error: %v", err)
					return
				}
				tt.fields.secretKey = secretKey
			}
			if tt.fields.accessKey != "" {
				accessKey, err := base64.Decrypt(tt.fields.accessKey)
				if err != nil {
					t.Errorf("base64.Decode error: %v", err)
					return
				}
				tt.fields.accessKey = accessKey
			}

			c, err := NewClient(tt.fields.endpoint, tt.fields.bucketName, tt.fields.accessKey, tt.fields.secretKey)
			if err != nil {
				t.Errorf("NewClient() error = %v", err)
				return
			}
			got, err := c.List(tt.args.sourcePath)
			if (err != nil) != tt.wantErr {
				t.Errorf("List() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) == 0 {
				t.Errorf("List() got = %v, want %v", got, tt.want)
				return
			}
			for _, path := range got {
				log.Info("path: %s", path)
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("List() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

func TestClient_DownloadDir(t *testing.T) {
	type fields struct {
		endpoint   string
		bucketName string
		accessKey  string
		secretKey  string
		oc         *oss.Client
		ob         *oss.Bucket
	}
	type args struct {
		ossDir              string
		localDir            string
		concurrentDownloads int
		cleanDir            bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testDownloadDir",
			fields: fields{
				endpoint:   "http://oss-cn-hangzhou.aliyuncs.com",
				bucketName: "algo-recommend",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				ossDir:              "dip_new/deal_qianchuan_brand_basic_recall/202409111400/cengine/test/2/default/1/0",
				localDir:            "/tmp/dump/deal_qianchuan_brand_basic_recall/202409111400",
				concurrentDownloads: 8,
				cleanDir:            true,
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 先解密
			if tt.fields.secretKey != "" {
				secretKey, err := base64.Decrypt(tt.fields.secretKey)
				if err != nil {
					t.Errorf("base64.Decode error: %v", err)
					return
				}
				tt.fields.secretKey = secretKey
			}
			if tt.fields.accessKey != "" {
				accessKey, err := base64.Decrypt(tt.fields.accessKey)
				if err != nil {
					t.Errorf("base64.Decode error: %v", err)
					return
				}
				tt.fields.accessKey = accessKey
			}

			c, err := NewClient(tt.fields.endpoint, tt.fields.bucketName, tt.fields.accessKey, tt.fields.secretKey)
			if err != nil {
				t.Errorf("NewClient() error = %v", err)
				return
			}
			if err = c.DownloadDir(tt.args.ossDir, tt.args.localDir, tt.args.concurrentDownloads, tt.args.cleanDir); (err != nil) != tt.wantErr {
				t.Errorf("DownloadDir() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_CreateDir(t *testing.T) {
	type fields struct {
		endpoint   string
		bucketName string
		accessKey  string
		secretKey  string
	}
	type args struct {
		ossDir string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "testCreateDir",
			fields: fields{
				endpoint: "http://oss-cn-hangzhou.aliyuncs.com",
				//endpoint:   "http://oss-cn-hangzhou-internal.aliyuncs.com",
				bucketName: "algo-recommend",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				//ossDir: "dip_agent/v7/default",
				ossDir: "dip_config/search_item_multi_vector/20250204/cengine/pre/1/default/1/0",
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 先解密
			if tt.fields.secretKey != "" {
				secretKey, err := base64.Decrypt(tt.fields.secretKey)
				if err != nil {
					t.Errorf("base64.Decode error: %v", err)
					return
				}
				tt.fields.secretKey = secretKey
			}
			if tt.fields.accessKey != "" {
				accessKey, err := base64.Decrypt(tt.fields.accessKey)
				if err != nil {
					t.Errorf("base64.Decode error: %v", err)
					return
				}
				tt.fields.accessKey = accessKey
			}

			c, err := NewClient(tt.fields.endpoint, tt.fields.bucketName, tt.fields.accessKey, tt.fields.secretKey)
			if err != nil {
				t.Errorf("NewClient() error = %v", err)
				return
			}
			if err := c.CreateDir(tt.args.ossDir); (err != nil) != tt.wantErr {
				t.Errorf("CreateDir() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test1(t *testing.T) {
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	jsonStr := "{\"taskId\":\"8538491\",\"reportUrl\":\"http://d1-dip-brain.shizhuang-inc.net/engine/schedule/engine\",\"callbackUrl\":\"http://d1-dip-brain.shizhuang-inc.net/engine/schedule/engine\",\"templateName\":\"index-build\",\"logPath\":\"/logs/engine\",\"dataPath\":\"/app/rel/data/tdata\",\"dbPath\":\"/app/rel/data/cdb\",\"compress\":true,\"shardId\":0,\"shardNum\":1,\"indexDef\":{\"indexName\":\"deal_qianchuan_brand_basic_recall\",\"indexVersion\":\"202408231600\",\"indexType\":\"kvv\",\"keyFieldName\":\"key\",\"keyFieldType\":\"string\",\"isDoubleVersion\":false,\"isInc\":false,\"fileConfig\":{\"invertedCount\":1000000,\"count\":8000000,\"lineFlag\":\"0x0a\"},\"maxRowNum\":10000,\"fields\":[{\"name\":\"bi\",\"indexAliasName\":\"\",\"type\":\"int64\",\"isWs\":false,\"isArray\":false,\"isDefaultNeed\":false,\"isIndex\":false,\"arrayLen\":0,\"isDefaultValueAvailable\":false,\"defaultValue\":\"\",\"indexType\":\"\",\"arrayMaxLen\":0,\"slop\":0,\"isTf\":false,\"isPrefix\":false,\"isPostfix\":false,\"isPosition\":false,\"isOffset\":false,\"isDisk\":false,\"remark\":\"\",\"config\":\"\"},{\"name\":\"ii_sc\",\"indexAliasName\":\"\",\"type\":\"double\",\"isWs\":false,\"isArray\":false,\"isDefaultNeed\":false,\"isIndex\":false,\"arrayLen\":0,\"isDefaultValueAvailable\":false,\"defaultValue\":\"\",\"indexType\":\"\",\"arrayMaxLen\":0,\"slop\":0,\"isTf\":false,\"isPrefix\":false,\"isPostfix\":false,\"isPosition\":false,\"isOffset\":false,\"isDisk\":false,\"remark\":\"\",\"config\":\"\"},{\"name\":\"bi_sc\",\"indexAliasName\":\"\",\"type\":\"double\",\"isWs\":false,\"isArray\":false,\"isDefaultNeed\":false,\"isIndex\":false,\"arrayLen\":0,\"isDefaultValueAvailable\":false,\"defaultValue\":\"\",\"indexType\":\"\",\"arrayMaxLen\":0,\"slop\":0,\"isTf\":false,\"isPrefix\":false,\"isPostfix\":false,\"isPosition\":false,\"isOffset\":false,\"isDisk\":false,\"remark\":\"\",\"config\":\"\"},{\"name\":\"ii\",\"indexAliasName\":\"\",\"type\":\"string\",\"isWs\":false,\"isArray\":false,\"isDefaultNeed\":false,\"isIndex\":false,\"arrayLen\":0,\"isDefaultValueAvailable\":false,\"defaultValue\":\"\",\"indexType\":\"\",\"arrayMaxLen\":0,\"slop\":0,\"isTf\":false,\"isPrefix\":false,\"isPostfix\":false,\"isPosition\":false,\"isOffset\":false,\"isDisk\":false,\"remark\":\"\",\"config\":\"\"}],\"extraInfo\":{},\"extraConfig\":{\"attribute\":{\"inc_topic\":\"#s{inc_topic}\",\"doclist_expand_ratio\":1.3,\"key_id\":\"#i{key_id}\",\"enable_unique_value_shared\":false,\"doclist_initial_capacity\":2,\"key_dict_doc_inplace_max_size\":8,\"value_data_load_strategy_touch\":true,\"mmap_chunk_page_size\":262144,\"multiple_value\":true,\"value_data_load_strategy_mmap\":true,\"value_data_file_max_size\":1024,\"key_dict_load_strategy_touch\":true,\"key_raw_load_strategy_lock\":false,\"key_raw_load_strategy_touch\":true,\"key_dict_load_strategy_mmap\":true,\"inc_timestamp\":\"#s{inc_timestamp}\",\"value_data_doc_inplace_max_size\":32,\"inc_index_capacity\":2000000,\"key_dict_load_strategy_lock\":false,\"doclist_max_capacity\":10000,\"key_raw_load_strategy_mmap\":true,\"enable_inc\":\"#b{enable_inc}\",\"value_data_load_strategy_lock\":false}}},\"datasource\":{\"type\":1,\"info\":\"{\\\"accessKey\\\":\\\"LTAI5tH73GQaNa3xwUNg9Lro\\\",\\\"secretKey\\\":\\\"******************************\\\",\\\"endpoint\\\":\\\"http://service.odps.aliyun.com/api\\\",\\\"project\\\":\\\"du_algo_1\\\",\\\"hints\\\":{\\\"odps.task.wlm.quota\\\":\\\"algo_dip\\\"},\\\"sql\\\":\\\"SELECT key, docs FROM du_algo_1.deal_qianchuan_brand_basic_recall WHERE ds = 202408231600 AND DSearchHash(key, 1) = 0 LIMIT 1000;\\\"}\"},\"dataTarget\":{\"type\":2,\"info\":\"{\\\"accessKey\\\":\\\"LTAI5tH73GQaNa3xwUNg9Lro\\\",\\\"secretKey\\\":\\\"******************************\\\",\\\"endpoint\\\":\\\"http://oss-cn-hangzhou.aliyuncs.com\\\",\\\"bucket\\\":\\\"algo-recommend\\\",\\\"dir\\\":\\\"algo-recommend/dip_new/deal_qianchuan_brand_basic_recall/202408231600/cengine/test/2/1/0\\\",\\\"ossConcurrentDownloads\\\":8,\\\"ossDownloadLimitSpeed\\\":false,\\\"ossDownloadSpeed\\\":52428800}\"},\"engineBuildCmdPath\":\"/bin/build-engine\"}"
	jsonMap := make(map[string]interface{})
	err := json.Unmarshal([]byte(jsonStr), &jsonMap)
	if err != nil {
		log.Error("json.Unmarshal error:%+v", err)
		return
	}
	jsonMap["dumpProcessorName"] = "forward"
	//jsonMap["specialDeal"] = ""
	jsonMap["clusterCode"] = "cengine"
	jsonMap["clusterGroup"] = "test"
	jsonMap["checksum"] = true
	jsonMap["sync"] = true
	delete(jsonMap, "hints")
	//log.Info("hints: %v", jsonMap["hints"])
	tr, err := json.Marshal(jsonMap)
	if err != nil {
		log.Error("json.MarshalIndent error:%+v", err)
		return
	}
	log.Info("json: %s", tr)
	jsonMapWrapper := make(map[string]interface{})
	jsonMapWrapper["type"] = 1
	jsonMapWrapper["taskInfo"] = string(tr)
	ttr, err := json.MarshalIndent(jsonMapWrapper, "", "  ")
	if err != nil {
		log.Error("json.MarshalIndent error:%+v", err)
		return
	}
	log.Info("json2: %s", ttr)
}

func Test2(t *testing.T) {
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	jsonStr := "{\"type\":1,\"taskInfo\":\"{\\\"templateName\\\":\\\"index-build\\\",\\\"logPath\\\":\\\"/logs/engine\\\",\\\"dataPath\\\":\\\"/app/rel/data/tdata\\\",\\\"dbPath\\\":\\\"/app/rel/data/cdb\\\",\\\"compress\\\":true,\\\"shardId\\\":0,\\\"shardNum\\\":1,\\\"indexDef\\\":{\\\"indexName\\\":\\\"deal_qianchuan_brand_basic_recall\\\",\\\"indexVersion\\\":\\\"202408300900\\\",\\\"indexType\\\":\\\"kvv\\\",\\\"keyFieldName\\\":\\\"key\\\",\\\"keyFieldType\\\":\\\"string\\\",\\\"isDoubleVersion\\\":false,\\\"isInc\\\":false,\\\"fileConfig\\\":{\\\"invertedCount\\\":1000000,\\\"count\\\":8000000,\\\"lineFlag\\\":\\\"0x0a\\\"},\\\"maxRowNum\\\":10000,\\\"fields\\\":[{\\\"name\\\":\\\"bi\\\",\\\"indexAliasName\\\":\\\"\\\",\\\"type\\\":\\\"int64\\\",\\\"isWs\\\":false,\\\"isArray\\\":false,\\\"isDefaultNeed\\\":false,\\\"isIndex\\\":false,\\\"arrayLen\\\":0,\\\"isDefaultValueAvailable\\\":false,\\\"defaultValue\\\":\\\"\\\",\\\"indexType\\\":\\\"\\\",\\\"arrayMaxLen\\\":0,\\\"slop\\\":0,\\\"isTf\\\":false,\\\"isPrefix\\\":false,\\\"isPostfix\\\":false,\\\"isPosition\\\":false,\\\"isOffset\\\":false,\\\"isDisk\\\":false,\\\"remark\\\":\\\"\\\",\\\"config\\\":\\\"\\\"},{\\\"name\\\":\\\"ii_sc\\\",\\\"indexAliasName\\\":\\\"\\\",\\\"type\\\":\\\"double\\\",\\\"isWs\\\":false,\\\"isArray\\\":false,\\\"isDefaultNeed\\\":false,\\\"isIndex\\\":false,\\\"arrayLen\\\":0,\\\"isDefaultValueAvailable\\\":false,\\\"defaultValue\\\":\\\"\\\",\\\"indexType\\\":\\\"\\\",\\\"arrayMaxLen\\\":0,\\\"slop\\\":0,\\\"isTf\\\":false,\\\"isPrefix\\\":false,\\\"isPostfix\\\":false,\\\"isPosition\\\":false,\\\"isOffset\\\":false,\\\"isDisk\\\":false,\\\"remark\\\":\\\"\\\",\\\"config\\\":\\\"\\\"},{\\\"name\\\":\\\"bi_sc\\\",\\\"indexAliasName\\\":\\\"\\\",\\\"type\\\":\\\"double\\\",\\\"isWs\\\":false,\\\"isArray\\\":false,\\\"isDefaultNeed\\\":false,\\\"isIndex\\\":false,\\\"arrayLen\\\":0,\\\"isDefaultValueAvailable\\\":false,\\\"defaultValue\\\":\\\"\\\",\\\"indexType\\\":\\\"\\\",\\\"arrayMaxLen\\\":0,\\\"slop\\\":0,\\\"isTf\\\":false,\\\"isPrefix\\\":false,\\\"isPostfix\\\":false,\\\"isPosition\\\":false,\\\"isOffset\\\":false,\\\"isDisk\\\":false,\\\"remark\\\":\\\"\\\",\\\"config\\\":\\\"\\\"},{\\\"name\\\":\\\"ii\\\",\\\"indexAliasName\\\":\\\"\\\",\\\"type\\\":\\\"string\\\",\\\"isWs\\\":false,\\\"isArray\\\":false,\\\"isDefaultNeed\\\":false,\\\"isIndex\\\":false,\\\"arrayLen\\\":0,\\\"isDefaultValueAvailable\\\":false,\\\"defaultValue\\\":\\\"\\\",\\\"indexType\\\":\\\"\\\",\\\"arrayMaxLen\\\":0,\\\"slop\\\":0,\\\"isTf\\\":false,\\\"isPrefix\\\":false,\\\"isPostfix\\\":false,\\\"isPosition\\\":false,\\\"isOffset\\\":false,\\\"isDisk\\\":false,\\\"remark\\\":\\\"\\\",\\\"config\\\":\\\"\\\"}],\\\"extraInfo\\\":{},\\\"extraConfig\\\":{\\\"attribute\\\":{\\\"inc_topic\\\":\\\"#s{inc_topic}\\\",\\\"doclist_expand_ratio\\\":1.3,\\\"key_id\\\":\\\"#i{key_id}\\\",\\\"enable_unique_value_shared\\\":false,\\\"doclist_initial_capacity\\\":2,\\\"key_dict_doc_inplace_max_size\\\":8,\\\"value_data_load_strategy_touch\\\":true,\\\"mmap_chunk_page_size\\\":262144,\\\"multiple_value\\\":true,\\\"value_data_load_strategy_mmap\\\":true,\\\"value_data_file_max_size\\\":1024,\\\"key_dict_load_strategy_touch\\\":true,\\\"key_raw_load_strategy_lock\\\":false,\\\"key_raw_load_strategy_touch\\\":true,\\\"key_dict_load_strategy_mmap\\\":true,\\\"inc_timestamp\\\":\\\"#s{inc_timestamp}\\\",\\\"value_data_doc_inplace_max_size\\\":32,\\\"inc_index_capacity\\\":2000000,\\\"key_dict_load_strategy_lock\\\":false,\\\"doclist_max_capacity\\\":10000,\\\"key_raw_load_strategy_mmap\\\":true,\\\"enable_inc\\\":\\\"#b{enable_inc}\\\",\\\"value_data_load_strategy_lock\\\":false}}},\\\"datasource\\\":{\\\"type\\\":1,\\\"info\\\":\\\"{\\\\\\\"accessKey\\\\\\\":\\\\\\\"LTAI5tH73GQaNa3xwUNg9Lro\\\\\\\",\\\\\\\"secretKey\\\\\\\":\\\\\\\"******************************\\\\\\\",\\\\\\\"endpoint\\\\\\\":\\\\\\\"http://service.odps.aliyun.com/api\\\\\\\",\\\\\\\"project\\\\\\\":\\\\\\\"du_algo_1\\\\\\\",\\\\\\\"hints\\\\\\\":{},\\\\\\\"sql\\\\\\\":\\\\\\\"select key, docs from du_algo_1.deal_qianchuan_brand_basic_recall where ds=202408300900 limit 1000;\\\\\\\"}\\\"},\\\"dataTarget\\\":{\\\"type\\\":2,\\\"info\\\":\\\"{\\\\\\\"accessKey\\\\\\\":\\\\\\\"LTAI5tH73GQaNa3xwUNg9Lro\\\\\\\",\\\\\\\"secretKey\\\\\\\":\\\\\\\"******************************\\\\\\\",\\\\\\\"endpoint\\\\\\\":\\\\\\\"http://oss-cn-hangzhou.aliyuncs.com\\\\\\\",\\\\\\\"bucket\\\\\\\":\\\\\\\"algo-recommend\\\\\\\",\\\\\\\"dir\\\\\\\":\\\\\\\"dip_new/deal_qianchuan_brand_basic_recall/202408300900/cengine/test/2/default/1/0\\\\\\\",\\\\\\\"ossConcurrentDownloads\\\\\\\":8,\\\\\\\"ossDownloadLimitSpeed\\\\\\\":false,\\\\\\\"ossDownloadSpeed\\\\\\\":52428800}\\\"},\\\"engineBuildCmdPath\\\":\\\"/app/rel/bin/build-engine\\\",\\\"dumpProcessorName\\\":\\\"ivt\\\",\\\"clusterCode\\\":\\\"doe-new-dip\\\",\\\"clusterGroup\\\":\\\"group1\\\",\\\"specialDeal\\\":\\\"[]\\\",\\\"checksum\\\":true}\"}"
	jsonMap := make(map[string]interface{})
	err := json.Unmarshal([]byte(jsonStr), &jsonMap)
	if err != nil {
		log.Error("json.Unmarshal error:%+v", err)
		return
	}
	//jsonMap["dumpProcessorName"] = "forward"
	//jsonMap["specialDeal"] = ""
	//jsonMap["clusterCode"] = "cengine"
	//jsonMap["clusterGroup"] = "test"
	//jsonMap["checksum"] = true
	//jsonMap["sync"] = true
	//delete(jsonMap, "hints")
	//log.Info("hints: %v", jsonMap["hints"])
	tr, err := json.MarshalIndent(jsonMap, "", "  ")
	if err != nil {
		log.Error("json.MarshalIndent error:%+v", err)
		return
	}
	log.Info("json: %s", tr)
	taskInfo := jsonMap["taskInfo"]
	r := &platform.IndexBuildRequest{}
	err = json.Unmarshal([]byte(taskInfo.(string)), r)
	if err != nil {
		log.Error("json.Unmarshal error:%+v", err)
		return
	}
	r.TaskId = "8663450"
	doSync := true
	r.Sync = &doSync
	r.CallbackUrl = "http://d1-dip-brain.shizhuang-inc.net/engine/schedule/engine"
	r.ReportUrl = "http://d1-dip-brain.shizhuang-inc.net/engine/task/status/report"

	// carry oss
	oss, err := r.DataTarget.GetOss()
	if err != nil {
		log.Error("get oss error:%+v", err)
		return
	}
	oss.Concurrency = 1
	ossContent, err := json.Marshal(oss)
	if err != nil {
		log.Error("json.MarshalIndent error:%+v", err)
		return
	}
	log.Info("oss: %s", ossContent)
	r.DataTarget.Info = string(ossContent)

	// 序列化request
	taskInfoContent, err := json.Marshal(r)
	if err != nil {
		log.Error("json.Marshal error:%+v", err)
		return
	}
	jsonMap["taskInfo"] = string(taskInfoContent)

	// 序列化jsonmap
	rc, err := json.Marshal(jsonMap)
	if err != nil {
		log.Error("json.Marshal error:%+v", err)
		return
	}
	log.Info("json2: %s", rc)

}
func TestClient_IsDirExist(t *testing.T) {
	type fields struct {
		endpoint   string
		bucketName string
		accessKey  string
		secretKey  string
	}
	type args struct {
		ossDir string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "testIsDirExist",
			fields: fields{
				endpoint:   "http://oss-cn-hangzhou.aliyuncs.com",
				bucketName: "algo-recommend",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				ossDir: "dip_config/search_item_multi_vector/20250204/cengine/pre/1/default/1/0",
			},
			want:    true,
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	for _, tt := range tests {
		// 先解密
		if tt.fields.secretKey != "" {
			secretKey, err := base64.Decrypt(tt.fields.secretKey)
			if err != nil {
				t.Errorf("base64.Decode error: %v", err)
				return
			}
			tt.fields.secretKey = secretKey
		}
		if tt.fields.accessKey != "" {
			accessKey, err := base64.Decrypt(tt.fields.accessKey)
			if err != nil {
				t.Errorf("base64.Decode error: %v", err)
				return
			}
			tt.fields.accessKey = accessKey
		}

		t.Run(tt.name, func(t *testing.T) {
			c, err := NewClient(tt.fields.endpoint, tt.fields.bucketName, tt.fields.accessKey, tt.fields.secretKey)
			if err != nil {
				t.Errorf("NewClient() error = %v", err)
				return
			}
			got, err := c.IsDirExist(tt.args.ossDir)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsDirExist() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsDirExist() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_DeleteDir(t *testing.T) {
	type fields struct {
		endpoint   string
		bucketName string
		accessKey  string
		secretKey  string
	}
	type args struct {
		ossDir string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "testDeleteDir",
			fields: fields{
				endpoint:   "http://oss-cn-hangzhou.aliyuncs.com",
				bucketName: "algo-recommend",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				//ossDir: "dip_agent/default/tt",
				ossDir: "dip_config/search_item_multi_vector/20250204/cengine/pre/1/default/1/0",
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, err := NewClient(tt.fields.endpoint, tt.fields.bucketName, tt.fields.accessKey, tt.fields.secretKey)
			if err != nil {
				t.Errorf("NewClient() error = %v", err)
				return
			}
			if err := c.DeleteDir(tt.args.ossDir); (err != nil) != tt.wantErr {
				t.Errorf("DeleteDir() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_CopyScriptIndexDir(t *testing.T) {
	type fields struct {
		endpoint   string
		bucketName string
		accessKey  string
		secretKey  string
		oc         *oss.Client
		ob         *oss.Bucket
	}
	type args struct {
		srcDir string
		dstDir string
	}
	tests := []struct {
		name    string
		enable  bool
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "testCopyScriptIndexDir",
			enable: false,
			fields: fields{
				endpoint:   "http://oss-cn-hangzhou.aliyuncs.com",
				bucketName: "algo-recommend",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				srcDir: "dip/ad_community_swingi2c/20250112/csearch/csprd/1/1/0/",
				dstDir: "dip_new/ad_community_swingi2c/20250112/cengine/prd/1/default/1/0/",
			},
			wantErr: false,
		},
		{
			name:   "testCopySgpIndexDir",
			enable: false,
			fields: fields{
				endpoint:   "http://oss-ap-southeast-1.aliyuncs.com",
				bucketName: "algo-recommend-new-sg",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				srcDir: "dip/oversea_ec_list_homepage_i2i_recall_v1_new_ord_sc/20250208/doe/prd/1/",
				dstDir: "dip/oversea_ec_list_homepage_i2i_recall_v1_new_ord_sc/20250208/doe/prd/2/",
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	for _, tt := range tests {
		if !tt.enable {
			t.Logf("skip test %s", tt.name)
			continue
		}
		// 先解密
		if tt.fields.secretKey != "" {
			secretKey, err := base64.Decrypt(tt.fields.secretKey)
			if err != nil {
				t.Errorf("base64.Decode error: %v", err)
				return
			}
			tt.fields.secretKey = secretKey
		}
		if tt.fields.accessKey != "" {
			accessKey, err := base64.Decrypt(tt.fields.accessKey)
			if err != nil {
				t.Errorf("base64.Decode error: %v", err)
				return
			}
			tt.fields.accessKey = accessKey
		}

		t.Run(tt.name, func(t *testing.T) {
			c, err := NewClient(tt.fields.endpoint, tt.fields.bucketName, tt.fields.accessKey, tt.fields.secretKey)
			if err != nil {
				t.Errorf("NewClient() error = %v", err)
				return
			}
			if err := c.CopyScriptIndexDir(tt.args.srcDir, tt.args.dstDir); (err != nil) != tt.wantErr {
				t.Errorf("CopyScriptIndexDir() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_ListObjects(t *testing.T) {
	type fields struct {
		endpoint   string
		bucketName string
		accessKey  string
		secretKey  string
	}
	type args struct {
		ossDir string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "testListObjects",
			fields: fields{
				endpoint:   "http://oss-cn-hangzhou.aliyuncs.com",
				bucketName: "algo-recommend",
				accessKey:  "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
				secretKey:  "****************************************",
			},
			args: args{
				//ossDir: "dip_agent/default/tt",
				ossDir: "dip_config/search_item_multi_vector/20250204/cengine/pre/1/default/1/0",
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		if tt.fields.secretKey != "" {
			secretKey, err := base64.Decrypt(tt.fields.secretKey)
			if err != nil {
				t.Errorf("base64.Decode error: %v", err)
				return
			}
			tt.fields.secretKey = secretKey
		}
		if tt.fields.accessKey != "" {
			accessKey, err := base64.Decrypt(tt.fields.accessKey)
			if err != nil {
				t.Errorf("base64.Decode error: %v", err)
				return
			}
			tt.fields.accessKey = accessKey
		}

		t.Run(tt.name, func(t *testing.T) {
			c, err := NewClient(tt.fields.endpoint, tt.fields.bucketName, tt.fields.accessKey, tt.fields.secretKey)
			if err != nil {
				t.Errorf("NewClient() error = %v", err)
				return
			}
			if objs, err := c.ListObjects(tt.args.ossDir); (err != nil) != tt.wantErr {
				t.Errorf("DeleteDir() error = %v, wantErr %v", err, tt.wantErr)
			} else {
				for _, obj := range objs {
					log.Info("object: %s", obj.Key)
				}
			}
		})
	}
}

func Test3(t *testing.T) {
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	mi := &dto.ModelInfo{
		ModelName: "test",
		VersionOssPaths: map[string]*dto.Oss{
			"20250204": {
				Endpoint:  "http://oss-cn-hangzhou.aliyuncs.com",
				Bucket:    "algo-recommend",
				AccessKey: "LTAI5t7onSuRStgsTy9hVP888",
				SecretKey: "aaa",
				Dir:       "aaa",
			},
		},
	}
	bmis := make([]*dto.ModelInfo, 0)
	bmis = append(bmis, mi)
	jsonBytes, err := json.Marshal(bmis)

	mis := make([]*dto.ModelInfo, 0)
	err = json.Unmarshal(jsonBytes, &mis) // 传递指针
	if err != nil {
		t.Errorf("json.Unmarshal error:%+v", err)
		return
	}
	content, err := json.MarshalIndentDefault(mis)
	if err != nil {
		t.Errorf("json.Marshal error:%+v", err)
		return
	}
	log.Info("result: %s", string(content))
	if len(mis) > 0 {
		for _, info := range mis {
			err = cfg.SetDefaultAndValidate(info)
			if err != nil {
				t.Errorf("cfg.SetDefaultAndValidate error:%+v", err)
				return
			}
		}
		content, err := json.MarshalIndentDefault(mis)
		if err != nil {
			t.Errorf("json.Marshal error:%+v", err)
			return
		}
		log.Info("result1: %s", string(content))
	}
}

func TestClient_DirSize(t *testing.T) {
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	var (
		endpoint   = "http://oss-cn-hangzhou.aliyuncs.com"
		bucketName = "algo-recommend"
		accessKey = "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp"
		secretKey = "****************************************"
	)
	// 先解密
	if secretKey != "" {
		secretKey, err := base64.Decrypt(secretKey)
		if err != nil {
			t.Errorf("base64.Decode error: %v", err)
			return
		}
		secretKey = secretKey
	}
	if accessKey != "" {
		accessKey, err := base64.Decrypt(accessKey)
		if err != nil {
			t.Errorf("base64.Decode error: %v", err)
			return
		}
		accessKey = accessKey
	}
	c, err := NewClient(endpoint, bucketName, accessKey, secretKey)
	if err != nil {
		t.Errorf("NewClient() error = %v", err)
		return
	}
	type args struct {
		dir string
	}
	tests := []struct {
		name    string
		args    args
		want    int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testDirSize",
			args: args{
				dir: "dip_new/dip_all_type_field_gqw2/2025-05-14/cengine/pre/1/default/1/0/",
			},
			want:    119610,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.DirSize(tt.args.dir)
			if (err != nil) != tt.wantErr {
				t.Errorf("DirSize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("DirSize() got = %v, want %v", got, tt.want)
			}
		})
	}
}
