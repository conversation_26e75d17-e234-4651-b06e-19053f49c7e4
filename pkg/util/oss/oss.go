package oss

import (
	"bytes"
	"context"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/retry"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/pkg/errors"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

type DownloadFileTask struct {
	ObjectKey  string
	ObjectSize int64
	LocalPath  string
}

type Client struct {
	endpoint   string
	bucketName string
	accessKey  string
	secretKey  string

	oc *oss.Client
	ob *oss.Bucket
}

func NewClient(endpoint, bucketName, accessKey, secretKey string) (*Client, error) {
	start := time.Now()
	defer func() {
		log.Info("init oss client cost %s", util.DurationToHumanReadable(time.Since(start)))
	}()
	// 创建阿里云OSS客户端
	ossClient, err := oss.New(endpoint, accessKey, secretKey, oss.Timeout(30, 60))
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create oss client")
	}

	// 获取bucket
	bucket, err := ossClient.Bucket(bucketName)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get bucket: %s", bucketName)
	}

	return &Client{
		endpoint:   endpoint,
		bucketName: bucketName,
		accessKey:  accessKey,
		secretKey:  secretKey,
		oc:         ossClient,
		ob:         bucket,
	}, nil
}

// DownloadDirs 从指定的多个oss路径下载文件到本地
func (c *Client) DownloadDirs(ossDir2LocalDir map[string]string,
	concurrentDownloads int,
	cleanDirBeforeDownload bool,
	cleanDirIfErr bool) error {

	log.Info("start download dirs: %v", ossDir2LocalDir)
	start := time.Now()
	defer func() {
		log.Info("download dirs success! cost %s", util.DurationToHumanReadable(time.Since(start)))
	}()

	// 设置并发下载数
	if concurrentDownloads < 1 {
		concurrentDownloads = runtime.GOMAXPROCS(-1)
	}

	// 初始化本地目录
	for ossDir, localDir := range ossDir2LocalDir {
		// 检查oss目录是否存在
		exist, err := c.IsDirExist(ossDir)
		if err != nil {
			return errors.WithMessagef(err, "check oss dir exists fail")
		}
		if !exist {
			return errors.New("oss path not exists: " + ossDir)
		}

		// 清空本地目录
		if cleanDirBeforeDownload {
			err := os.RemoveAll(localDir)
			if err != nil && !os.IsNotExist(err) {
				return errors.Wrapf(err, "error clear local dir: %s", localDir)
			}
			log.Info("local dir %s cleared", localDir)
		}

		// 确保本地目录存在
		if err := os.MkdirAll(localDir, 0755); err != nil && !os.IsExist(err) {
			return errors.Wrapf(err, "error creating local dir: %s", localDir)
		}
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() // 确保在函数退出时取消上下文

	var (
		wg       sync.WaitGroup
		firstErr atomic.Value
		errChan  = make(chan error, 1)
		tasks    = make(chan DownloadFileTask, concurrentDownloads)
	)

	// 错误监听
	go func() {
		err, ok := <-errChan
		if !ok {
			return
		}
		if firstErr.Load() == nil {
			log.Info("cancel download due to error: %v", err)
			firstErr.Store(err)
			cancel()
		}
	}()

	// 启动消费者
	for i := 0; i < concurrentDownloads; i++ {
		index := i
		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
				log.Info("downloadFileTaskConsumer %d stopped", index)
			}()
			c.downloadFileTaskConsumer(index, ctx, tasks, errChan)
		}()
	}

	// 启动生产者
	go func() {
		// 确保退出时关闭任务队列
		defer close(tasks)
		// 生产任务
		err := c.mutilDownloadFileTaskProducer(ossDir2LocalDir, tasks, ctx)
		if err != nil {
			select {
			case errChan <- err:
			default:
			}
			return
		}
	}()

	// 等待消费者完成
	wg.Wait()
	// 关闭错误通道
	close(errChan)

	// 返回第一个错误
	if firstErr.Load() != nil {
		if cleanDirIfErr {
			for _, localDir := range ossDir2LocalDir {
				err := os.RemoveAll(localDir)
				if err != nil && !os.IsNotExist(err) {
					return errors.Wrapf(err, "error clear local dir: %s", localDir)
				}
			}
		}
		return firstErr.Load().(error)
	}

	return nil
}

func (c *Client) mutilDownloadFileTaskProducer(ossDir2LocalDir map[string]string, taskChan chan<- DownloadFileTask, ctx context.Context) error {
	for ossDir, localDir := range ossDir2LocalDir {
		// 获取目录中的文件信息
		objs, err := c.ListObjects(ossDir)
		if err != nil {
			return err
		}

		for _, object := range objs {
			// 跳过目录
			if strings.HasSuffix(object.Key, "/") {
				continue
			}

			// 创建下载任务
			task := DownloadFileTask{
				ObjectKey:  object.Key,
				ObjectSize: object.Size,
				LocalPath:  filepath.Join(localDir, strings.TrimPrefix(object.Key, ossDir)),
			}

			select {
			case <-ctx.Done():
				return nil
			case taskChan <- task:
			}
		}
	}
	return nil
}

// DownloadDir 从指定路径下载目录
// ossDir: oss上的目录路径, 不包含bucketName
// localDir: 下载到本地的目录路径
// concurrentDownloads: 并发下载数
// cleanDir: 是否清空目标目录
func (c *Client) DownloadDir(ossDir, localDir string, concurrentDownloads int, cleanDir bool) error {
	log.Info("Downloading from oss://%s/%s to %s", c.bucketName, ossDir, localDir)
	start := time.Now()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() // 确保在函数退出时取消上下文

	if concurrentDownloads < 1 {
		concurrentDownloads = runtime.GOMAXPROCS(-1)
	}
	log.Info("concurrent downloads: %d", concurrentDownloads)

	// 检查oss目录是否存在
	exist, err := c.IsDirExist(ossDir)
	if err != nil {
		return errors.WithMessagef(err, "check oss dir exists fail")
	}
	if !exist {
		return errors.New("oss path not exists: " + ossDir)
	}

	// 清空本地目录
	if cleanDir {
		err := os.RemoveAll(localDir)
		if err != nil && !os.IsNotExist(err) {
			return errors.Wrapf(err, "error clear local dir: %s", localDir)
		}
		log.Info("local dir %s cleared", localDir)
	}

	// 确保本地目录存在
	if err := os.MkdirAll(localDir, 0755); err != nil && !os.IsExist(err) {
		return errors.Wrapf(err, "error creating local dir: %s", localDir)
	}

	// 任务队列
	tasks := make(chan DownloadFileTask, concurrentDownloads)
	// 等待组
	var wg sync.WaitGroup
	// 错误队列
	errChan := make(chan error, 1)
	// 原子的存储err
	var firstErr atomic.Value

	// 错误监听
	go func() {
		err, ok := <-errChan
		if !ok {
			return
		}
		if firstErr.Load() == nil {
			log.Info("Cancelling download due to error: %v", err)
			firstErr.Store(err)
			cancel()
		}
	}()

	// 启动消费者
	for i := 0; i < concurrentDownloads; i++ {
		index := i
		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
				log.Info("downloadFileTaskConsumer %d stopped", index)
			}()
			c.downloadFileTaskConsumer(index, ctx, tasks, errChan)
		}()
	}

	// 启动生产者
	go func() {
		// 确保退出时关闭任务队列
		defer close(tasks)
		// 生产任务
		err := c.downloadFileTaskProducer(ctx, ossDir, localDir, tasks)
		if err != nil {
			select {
			case errChan <- err:
			default:
			}
			return
		}
	}()

	// 等待消费者完成
	wg.Wait()
	// 关闭错误通道
	close(errChan)

	// 耗时
	log.Info("Download %s to %s success! cost %s", ossDir, localDir, util.DurationToHumanReadable(time.Since(start)))

	// 返回第一个错误
	if firstErr.Load() != nil {
		return firstErr.Load().(error)
	}
	return nil
}

func (c *Client) downloadFileTaskConsumer(index int, ctx context.Context, tasks <-chan DownloadFileTask, errChan chan<- error) {
	log.Info("downloadFileTaskConsumer %d started", index)
	for {
		select {
		case <-ctx.Done():
			return
		case task, ok := <-tasks:
			if !ok {
				return
			}

			err := c.DownloadFileAuto(task)

			// fail fast
			if err != nil {
				select {
				case errChan <- err:
				default:
				}
				return
			}
		}
	}
}

// DownloadFileAuto 下载文件, 根据文件大小自动选择下载方式
func (c *Client) DownloadFileAuto(task DownloadFileTask) error {
	var (
		err   error
		start = time.Now()
	)
	defer func() {
		resultStr := "success"
		if err != nil {
			resultStr = fmt.Sprintf("failed: %s", err)
		}
		log.Info("download [%s] to [%s] %s. cost: %s", task.ObjectKey, task.LocalPath, resultStr, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 确保本地目录存在
	localPath := task.LocalPath
	localFileDir := filepath.Dir(localPath)
	err = os.MkdirAll(localFileDir, 0755)
	if err != nil {
		return errors.Wrapf(err, "failed to create local dir: %s", localFileDir)
	}

	// 重试下载文件
	err = retry.DoRetry(func() error {
		var downloadErr error
		if task.ObjectSize > 20*1024*1024 {
			// 超过20M的文件, 使用分片下载
			downloadErr = c.DownloadBigFile(task.ObjectKey, localPath)
		} else {
			downloadErr = c.ob.GetObjectToFile(task.ObjectKey, localPath)
			if downloadErr != nil {
				downloadErr = errors.Wrapf(downloadErr, "failed to download [%s] to [%s]", task.ObjectKey, localPath)
			}
		}
		return downloadErr
	})
	return err
}

func (c *Client) downloadFileTaskProducer(ctx context.Context, ossDir, localDir string, tasks chan<- DownloadFileTask) error {
	var continuationToken string
	for {
		if ctx.Err() != nil {
			return nil
		}

		lsRes, err := c.ob.ListObjectsV2(oss.Prefix(ossDir), oss.ContinuationToken(continuationToken))
		if err != nil {
			return errors.Wrapf(err, "error listing objects: %s", ossDir)
		}

		for _, object := range lsRes.Objects {
			if strings.HasSuffix(object.Key, "/") {
				continue
			}

			localFilePath := filepath.Join(localDir, strings.TrimPrefix(object.Key, ossDir))
			select {
			case tasks <- DownloadFileTask{ObjectKey: object.Key, ObjectSize: object.Size, LocalPath: localFilePath}:
			case <-ctx.Done():
				return nil
			}
		}

		if !lsRes.IsTruncated {
			break
		}
		continuationToken = lsRes.NextContinuationToken
	}
	return nil
}

// List 列出指定路径下的文件
func (c *Client) List(ossDir string) ([]string, error) {
	log.Info("List objects in %s", ossDir)
	objs, err := c.ListObjects(ossDir)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to list objects in dir: %s", ossDir)
	}

	var objects []string
	for _, object := range objs {
		objects = append(objects, object.Key)
	}
	return objects, nil
}

// ListObjects 列出指定路径下的文件
func (c *Client) ListObjects(ossDir string) ([]oss.ObjectProperties, error) {
	log.Info("List objects in %s", ossDir)
	res := make([]oss.ObjectProperties, 0)
	err := c.ObjectIterator(ossDir, func(object oss.ObjectProperties) error {
		res = append(res, object)
		return nil
	})
	return res, err
}

func (c *Client) ObjectIterator(ossDir string, consumer func(object oss.ObjectProperties) error) error {
	log.Info("List objects in %s", ossDir)
	var (
		err               error
		continuationToken string
		lsRes             oss.ListObjectsResultV2
		count             = 0
	)

	defer func() {
		log.Info("object iterator file count: %d. err: %s", count, err)
	}()

	for {
		lsRes, err = c.ob.ListObjectsV2(oss.Prefix(ossDir), oss.ContinuationToken(continuationToken))
		if err != nil {
			return errors.Wrapf(err, "failed to list objects: %s", ossDir)
		}

		for _, object := range lsRes.Objects {
			if strings.HasSuffix(object.Key, "/") {
				continue
			}
			count++

			err = consumer(object)
			if err != nil {
				return errors.Wrapf(err, "failed to consume object: %s", object.Key)
			}
		}

		if !lsRes.IsTruncated {
			break
		}
		continuationToken = lsRes.NextContinuationToken
	}

	return nil
}

// IsDirExist 是否存在指的目录
func (c *Client) IsDirExist(ossDir string) (bool, error) {
	var lsRes oss.ListObjectsResultV2
	var exist bool

	// 使用重试机制检查目录是否存在
	err := retry.DoRetry(func() error {
		var err error
		lsRes, err = c.ob.ListObjectsV2(oss.Prefix(ossDir), oss.MaxKeys(1))
		if err != nil {
			return errors.Wrapf(err, "failed to list objects in dir: %s", ossDir)
		}
		exist = len(lsRes.Objects) > 0
		return nil
	})

	if err != nil {
		return false, err
	}
	return exist, nil
}

// IsFileExist 是否存在指的文件
func (c *Client) IsFileExist(ossFile string) (bool, error) {
	exist, err := c.ob.IsObjectExist(ossFile)
	if err != nil {
		return false, errors.Wrapf(err, "failed to check object exists: %s", ossFile)
	}
	return exist, nil
}

// DeleteDir 删除指定目录(包括目录中的文件)
func (c *Client) DeleteDir(ossDir string) error {
	var (
		err   error
		count = 0
	)
	defer func() {
		log.Info("delete oss dir(%s). delete file count: %d. err: %s", ossDir, count, err)
	}()

	return c.ObjectIterator(ossDir, func(object oss.ObjectProperties) error {
		err = c.ob.DeleteObject(object.Key)
		if err != nil {
			return errors.Wrapf(err, "failed to delete object: %s", object.Key)
		}
		count++
		return nil
	})
}

// DownloadBigFile 单个大文件多线程分快下载. 适用于压缩后的索引文件
func (c *Client) DownloadBigFile(ossFile, localFile string) error {
	return c.DownloadBigFileWithOpt(ossFile, localFile, 2*1024*1024, 6)
}

// DownloadBigFileWithOpt 单个大文件多线程分快下载. 适用于压缩后的索引文件
func (c *Client) DownloadBigFileWithOpt(ossFile, localFile string, partSize int64, concurrentDownloads int) error {
	log.Info("start downloading big file %s to %s", ossFile, localFile)

	// Ensure the local directory exists
	localFileDir := filepath.Dir(localFile)
	if err := os.MkdirAll(localFileDir, 0755); err != nil {
		return errors.Wrapf(err, "error creating local dir: %s", localFileDir)
	}

	// Download the file
	err := c.ob.DownloadFile(ossFile, localFile, partSize, oss.Routines(concurrentDownloads))
	if err != nil {
		return errors.Wrapf(err, "error download file: %s", ossFile)
	}
	return nil
}

// DownloadFile 下载文件
func (c *Client) DownloadFile(ossFile, localFile string) error {
	log.Info("start downloading file from %s to %s", ossFile, localFile)
	// Ensure the local directory exists
	localFileDir := filepath.Dir(localFile)
	if err := os.MkdirAll(localFileDir, 0755); err != nil {
		return errors.Wrapf(err, "error creating local dir: %s", localFileDir)
	}

	// Download the file
	err := c.ob.GetObjectToFile(ossFile, localFile)
	if err != nil {
		return errors.Wrapf(err, "error download file: %s", ossFile)
	}
	return nil
}

// ReadFileContent 读取文件内容
func (c *Client) ReadFileContent(ossFile string) ([]byte, error) {
	buf := new(bytes.Buffer)
	body, err := c.ob.GetObject(ossFile)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get file content: %s", ossFile)
	}
	defer body.Close()
	_, err = io.Copy(buf, body)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to copy file content: %s", ossFile)
	}
	return buf.Bytes(), nil
}

// UploadDir 并发上传目录到OSS
func (c *Client) UploadDir(localDir, remoteDir string) error {
	var (
		wg  sync.WaitGroup
		err error
	)
	// 设置goroutines的最大并发数，可以需求和资源限制调整
	threadNum := runtime.GOMAXPROCS(-1) * 2
	maxGoroutines := threadNum
	log.Info("upload dir %s to %s, max goroutines: %d", localDir, remoteDir, maxGoroutines)
	goroutineSemaphore := make(chan struct{}, maxGoroutines)
	// 保存第一个错误
	ve := atomic.Value{}

	// 遍历目录中的所有文件
	err = filepath.Walk(localDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil // 跳过目录
		}

		// 计算文件在OSS中的路径
		relativePath, err := filepath.Rel(localDir, path)
		if err != nil {
			return errors.Wrapf(err, "failed to get relative path of %s", path)
		}
		ossPath := filepath.Join(remoteDir, relativePath)

		wg.Add(1)
		goroutineSemaphore <- struct{}{} // 获取令牌
		go func(path, ossPath string) {
			defer wg.Done()
			defer func() { <-goroutineSemaphore }() // 释放令牌

			// 执行上传
			err = c.UploadFile(path, ossPath)
			if err != nil {
				ve.Store(err)
				log.Error("failed to upload %s to %s: %+v\n", path, ossPath, err)
			}
		}(path, ossPath)

		return nil
	})

	wg.Wait() // 等待所有goroutine完成

	if err == nil {
		val := ve.Load()
		if val != nil {
			err = val.(error)
		}
	}
	return err
}

// UploadFile 上传单个文件到OSS
func (c *Client) UploadFile(localFilePath, ossFilePath string) error {
	// 打开本地文件
	file, err := os.Open(localFilePath)
	if err != nil {
		return errors.Wrapf(err, "failed to open file: %s", localFilePath)
	}
	defer file.Close()

	// 获取文件大小
	fileInfo, err := file.Stat()
	if err != nil {
		return errors.Wrapf(err, "failed to get file info: %s", localFilePath)
	}
	fileSize := fileInfo.Size()

	// 如果文件大小小于200M, 直接上传
	if fileSize < 200*1024*1024 {
		err = c.ob.PutObject(ossFilePath, file)
		if err != nil {
			return errors.Wrapf(err, "failed to upload file %s to %s", localFilePath, ossFilePath)
		}
		return nil
	}

	// 大文件分片上传
	var (
		partSize    = int64(100 * 1024 * 1024) // 默认分片大小100M
		concurrency = 5                        // 默认并发数5
	)
	// 计算分片上传的并行数
	partCount := int(fileSize / partSize)
	if partCount < concurrency {
		concurrency = partCount
	}
	log.Info("start uploading big file %s to %s, partSize: %d, concurrency: %d", localFilePath, ossFilePath, partSize, concurrency)
	err = c.ob.UploadFile(ossFilePath, localFilePath, partSize, oss.Routines(concurrency))
	if err != nil {
		return errors.Wrapf(err, "failed to upload big file %s to %s", localFilePath, ossFilePath)
	}
	return nil
}

// CreateDir 创建oss目录
func (c *Client) CreateDir(ossDir string) error {
	if !strings.HasSuffix(ossDir, "/") {
		ossDir += "/"
	}
	err := c.ob.PutObject(ossDir, strings.NewReader(""))
	if err != nil {
		return errors.Wrapf(err, "failed to create oss dir: %s", ossDir)
	}
	return nil
}

// CopyDir 复制指定oss目录到指定目录
func (c *Client) CopyDir(srcDir, dstDir string) error {
	return c.CopyDirWithFunc(srcDir, dstDir, nil)
}

// CopyDirWithFunc 复制指定oss目录到指定目录
func (c *Client) CopyDirWithFunc(srcDir, dstDir string, filter func(srcPath, dstPath string) (bool, error)) error {
	if !strings.HasSuffix(srcDir, "/") {
		srcDir += "/"
	}
	if !strings.HasSuffix(dstDir, "/") {
		dstDir += "/"
	}

	return c.ObjectIterator(srcDir, func(object oss.ObjectProperties) error {
		src := object.Key
		dst := strings.Replace(src, srcDir, dstDir, 1)
		if filter != nil {
			b, err := filter(src, dst)
			if err != nil {
				return errors.Wrapf(err, "failed to filter object: %s->%s", src, dst)
			}
			if !b {
				return nil
			}
		}
		_, err := c.ob.CopyObject(src, dst)
		if err != nil {
			return errors.Wrapf(err, "failed to copy object: %s", src)
		}
		return nil
	})
}

// CopyScriptIndexDir 复制脚本构建的索引目录到agent目录
func (c *Client) CopyScriptIndexDir(srcDir, dstDir string) error {
	filter := func(srcPath, dstPath string) (bool, error) {
		// 如果srcPath不是check.json, 则按照原有方式复制
		if !strings.HasSuffix(srcPath, "check.json") {
			return true, nil
		}
		// 将原有check.json转为符合agent格式的check.json的内容
		// 读取原有check.json的内容
		content, err := c.ReadFileContent(srcPath)
		if err != nil {
			return false, nil
		}
		// check.json内容转为map[string]interface{}
		var originCheckJson map[string]interface{}
		err = json.Unmarshal(content, &originCheckJson)
		if err != nil {
			return false, err
		}
		// 构建新的check.json内容
		originCheckJson["index_total_count"] = originCheckJson["docs_total_count"]
		originCheckJson["fail_doc_count"] = originCheckJson["parse_error_count"]
		if buildTotalSecs, ok := originCheckJson["build_total_secs"].(float64); ok {
			originCheckJson["build_cost_ms"] = int64(buildTotalSecs) * 1000
		}
		newCheckJson := make(map[string]interface{})
		newCheckJson["build_result"] = originCheckJson
		header := make(map[string]interface{})
		newCheckJson["header"] = header
		header["code"] = 200
		// 将新的check.json内容写入到dstPath
		newContent, err := json.Marshal(newCheckJson)
		if err != nil {
			return false, err
		}
		err = c.ob.PutObject(dstPath, bytes.NewReader(newContent))
		if err != nil {
			return false, err
		}

		// 写入新的兼容的checksum.json文件
		checksumPath := strings.Replace(dstPath, "check.json", "checksum.json", 1)
		err = c.ob.PutObject(checksumPath, strings.NewReader("{\"isCompatible\":true}"))

		return false, nil
	}
	return c.CopyDirWithFunc(srcDir, dstDir, filter)
}

// DirSize 计算指定目录的大小
func (c *Client) DirSize(dir string) (int64, error) {
	var size int64
	err := c.ObjectIterator(dir, func(object oss.ObjectProperties) error {
		size += object.Size
		return nil
	})
	if err != nil {
		return 0, err
	}
	return size, nil
}
