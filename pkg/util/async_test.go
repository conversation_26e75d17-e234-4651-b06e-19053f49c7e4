package util

import (
	"context"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/sonic"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"reflect"
	"testing"
	"time"
)

func TestAsyncProcess(t *testing.T) {
	type args[T any] struct {
		clue          string
		produce       ProduceFunc[T]
		consume       ConsumeFunc[T]
		consumerCount int
		failFast      bool
	}
	type testCase[T any] struct {
		name string
		args args[T]
		want []error
	}
	tests := []testCase[int]{
		// TODO: Add test cases.
		{
			name: "testAsyncProcess",
			args: args[int]{
				clue: "test1",
				produce: func(ctx context.Context, dataCh chan<- int) error {
					for i := 0; i < 10; i++ {
						select {
						case <-ctx.Done():
							return nil
						case dataCh <- i:
						}
					}
					return nil
				},
				consume: func(item int) error {
					if item%2 == 0 {
						return errors.Errorf("consume error: %d", item)
					}
					log.Info("consume item: %d", item)
					return nil
				},
				consumerCount: 2,
				failFast:      false,
			},
			want: []error{errors.Errorf("consume error: 0"), errors.Errorf("consume error: 2"), errors.Errorf("consume error: 4"), errors.Errorf("consume error: 6"), errors.Errorf("consume error: 8")},
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := AsyncProcess(tt.args.clue, tt.args.produce, tt.args.consume, tt.args.consumerCount, tt.args.failFast)
			for i, err := range got {
				assert.Equal(t, tt.want[i].Error(), err.Error())
			}
		})
	}
	time.Sleep(3 * time.Second)
}

func TestExecuteTasks(t *testing.T) {
	type args[T any] struct {
		tasks []Task[T]
	}
	type testCase[T any] struct {
		name    string
		args    args[T]
		want    []T
		wantErr bool
	}
	tests := []testCase[string]{
		{
			name: "testExecuteTasks",
			args: args[string]{
				tasks: []Task[string]{
					func() (string, error) {
						return "task1 result", nil
					},
					func() (string, error) {
						return "task2 result", nil
					},
					func() (string, error) {
						return "task3 result", nil
					},
				},
			},
			want:    []string{"task1 result", "task2 result", "task3 result"},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExecuteTasks("test", tt.args.tasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExecuteTasks() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExecuteTasks() got = %v, want %v", got, tt.want)
			}
		})
	}
}
