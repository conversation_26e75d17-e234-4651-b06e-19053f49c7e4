package checksum

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/sonic"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestGenerateAndWriteToFile(t *testing.T) {
	type args struct {
		dir              string
		checksumFileName string
		filter           func(path string) bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		setup   func() error
		cleanup func()
		verify  func(t *testing.T, dir, checksumFileName string)
	}{
		{
			name: "正常生成校验和文件",
			args: args{
				dir:              "/tmp/checksum_test",
				checksumFileName: "checksum.json",
				filter: func(path string) bool {
					return !strings.HasSuffix(path, "checksum.json")
				},
			},
			wantErr: false,
			setup: func() error {
				// 创建测试目录
				if err := os.MkdirAll("/tmp/checksum_test", 0755); err != nil {
					return err
				}
				// 创建测试文件
				if err := os.WriteFile("/tmp/checksum_test/test1.txt", []byte("test1 content"), 0644); err != nil {
					return err
				}
				if err := os.WriteFile("/tmp/checksum_test/test2.txt", []byte("test2 content"), 0644); err != nil {
					return err
				}
				return nil
			},
			cleanup: func() {
				os.RemoveAll("/tmp/checksum_test")
			},
			verify: func(t *testing.T, dir, checksumFileName string) {
				// 验证校验和文件是否存在
				checksumFile := filepath.Join(dir, checksumFileName)
				if !util.IsPathExist(checksumFile) {
					t.Errorf("校验和文件不存在: %s", checksumFile)
					return
				}

				// 读取校验和文件
				var info Info
				err := util.ReadFileTo(checksumFile, &info)
				if err != nil {
					t.Errorf("读取校验和文件失败: %v", err)
					return
				}

				// 验证校验和内容
				if info.CheckSum == nil {
					t.Errorf("校验和内容为空")
					return
				}

				// 验证文件数量
				if len(info.CheckSum) != 2 {
					t.Errorf("校验和文件数量不正确，期望2，实际%d", len(info.CheckSum))
					return
				}

				// 验证是否包含测试文件
				if _, ok := info.CheckSum["test1.txt"]; !ok {
					t.Errorf("校验和不包含test1.txt")
				}
				if _, ok := info.CheckSum["test2.txt"]; !ok {
					t.Errorf("校验和不包含test2.txt")
				}
			},
		},
		{
			name: "目录不存在",
			args: args{
				dir:              "/tmp/not_exist_dir",
				checksumFileName: "checksum.json",
				filter: func(path string) bool {
					return true
				},
			},
			wantErr: true,
			setup:   func() error { return nil },
			cleanup: func() {},
		},
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试环境
			if err := tt.setup(); err != nil {
				t.Fatalf("测试环境准备失败: %v", err)
			}
			defer tt.cleanup()

			// 执行测试
			err := GenerateAndWriteToFile(tt.args.dir, tt.args.checksumFileName, tt.args.filter)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateAndWriteToFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 额外验证
			if err == nil && tt.verify != nil {
				tt.verify(t, tt.args.dir, tt.args.checksumFileName)
			}
		})
	}
}

func TestCheckEqualLocal(t *testing.T) {
	type args struct {
		localDir         string
		checksumFileName string
	}
	tests := []struct {
		name    string
		args    args
		setup   func(t *testing.T) error
		cleanup func()
		want    bool
		wantErr bool
	}{
		{
			name: "文件校验一致",
			args: args{
				localDir:         "/tmp/checksum_test_equal",
				checksumFileName: "checksum.json",
			},
			setup: func(t *testing.T) error {
				// 创建测试目录
				if err := os.MkdirAll("/tmp/checksum_test_equal", 0755); err != nil {
					return err
				}

				// 创建测试文件
				if err := os.WriteFile("/tmp/checksum_test_equal/test1.txt", []byte("test1 content"), 0644); err != nil {
					return err
				}
				if err := os.WriteFile("/tmp/checksum_test_equal/test2.txt", []byte("test2 content"), 0644); err != nil {
					return err
				}

				// 生成校验和文件
				return GenerateAndWriteToFile("/tmp/checksum_test_equal", "checksum.json", func(path string) bool {
					return !strings.HasSuffix(path, "checksum.json")
				})
			},
			cleanup: func() {
				os.RemoveAll("/tmp/checksum_test_equal")
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "文件校验不一致",
			args: args{
				localDir:         "/tmp/checksum_test_not_equal",
				checksumFileName: "checksum.json",
			},
			setup: func(t *testing.T) error {
				// 创建测试目录
				if err := os.MkdirAll("/tmp/checksum_test_not_equal", 0755); err != nil {
					return err
				}

				// 创建测试文件
				if err := os.WriteFile("/tmp/checksum_test_not_equal/test1.txt", []byte("test1 content"), 0644); err != nil {
					return err
				}
				if err := os.WriteFile("/tmp/checksum_test_not_equal/test2.txt", []byte("test2 content"), 0644); err != nil {
					return err
				}

				// 生成校验和文件
				if err := GenerateAndWriteToFile("/tmp/checksum_test_not_equal", "checksum.json", func(path string) bool {
					return !strings.HasSuffix(path, "checksum.json")
				}); err != nil {
					return err
				}

				// 修改文件内容，使校验和不一致
				time.Sleep(100 * time.Millisecond) // 确保文件修改时间不同
				return os.WriteFile("/tmp/checksum_test_not_equal/test1.txt", []byte("modified content"), 0644)
			},
			cleanup: func() {
				os.RemoveAll("/tmp/checksum_test_not_equal")
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "校验和文件不存在",
			args: args{
				localDir:         "/tmp/checksum_test_no_file",
				checksumFileName: "checksum.json",
			},
			setup: func(t *testing.T) error {
				// 创建测试目录
				return os.MkdirAll("/tmp/checksum_test_no_file", 0755)
			},
			cleanup: func() {
				os.RemoveAll("/tmp/checksum_test_no_file")
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "兼容模式校验和文件",
			args: args{
				localDir:         "/tmp/checksum_test_compatible",
				checksumFileName: "checksum.json",
			},
			setup: func(t *testing.T) error {
				// 创建测试目录
				if err := os.MkdirAll("/tmp/checksum_test_compatible", 0755); err != nil {
					return err
				}

				// 创建兼容模式的校验和文件
				compatible := true
				info := &Info{
					IsCompatible: &compatible,
					CheckSum:     map[string]int{"test1.txt": 12345},
				}

				checksumFile := filepath.Join("/tmp/checksum_test_compatible", "checksum.json")
				return util.WriteJsonToFile(checksumFile, info, false)
			},
			cleanup: func() {
				os.RemoveAll("/tmp/checksum_test_compatible")
			},
			want:    true,
			wantErr: false,
		},
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试环境
			if err := tt.setup(t); err != nil {
				t.Fatalf("测试环境准备失败: %v", err)
			}
			defer tt.cleanup()

			// 执行测试
			got, err := CheckEqualLocal(tt.args.localDir, tt.args.checksumFileName)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckEqualLocal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckEqualLocal() = %v, want %v", got, tt.want)
			}
		})
	}
}
