package checksum

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/sonic"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestGenerateDirectoryChecksums(t *testing.T) {
	type args struct {
		dirPath string
		filter  func(path string) bool
	}
	tests := []struct {
		name    string
		args    args
		setup   func(t *testing.T) (string, error)
		cleanup func(string)
		want    func(t *testing.T, info *Info)
		wantErr bool
	}{
		{
			name: "正常生成目录校验和",
			args: args{
				filter: func(path string) bool {
					return !strings.HasSuffix(path, "exclude.txt")
				},
			},
			setup: func(t *testing.T) (string, error) {
				// 创建临时测试目录
				tmpDir, err := os.MkdirTemp("", "checksum_test_*")
				if err != nil {
					return "", err
				}
				
				// 创建测试文件
				if err := os.WriteFile(filepath.Join(tmpDir, "file1.txt"), []byte("file1 content"), 0644); err != nil {
					return tmpDir, err
				}
				if err := os.WriteFile(filepath.Join(tmpDir, "file2.txt"), []byte("file2 content"), 0644); err != nil {
					return tmpDir, err
				}
				if err := os.WriteFile(filepath.Join(tmpDir, "exclude.txt"), []byte("excluded content"), 0644); err != nil {
					return tmpDir, err
				}
				
				// 创建子目录
				subDir := filepath.Join(tmpDir, "subdir")
				if err := os.MkdirAll(subDir, 0755); err != nil {
					return tmpDir, err
				}
				if err := os.WriteFile(filepath.Join(subDir, "file3.txt"), []byte("file3 content"), 0644); err != nil {
					return tmpDir, err
				}
				
				return tmpDir, nil
			},
			cleanup: func(tmpDir string) {
				os.RemoveAll(tmpDir)
			},
			want: func(t *testing.T, info *Info) {
				// 验证校验和结果
				if info == nil {
					t.Errorf("校验和信息为空")
					return
				}
				
				// 验证排除的文件
				if len(info.Excludes) != 1 {
					t.Errorf("排除文件数量不正确，期望1，实际%d", len(info.Excludes))
				} else if info.Excludes[0] != "exclude.txt" {
					t.Errorf("排除文件不正确，期望exclude.txt，实际%s", info.Excludes[0])
				}
				
				// 验证校验和文件数量
				expectedFiles := []string{"file1.txt", "file2.txt", "subdir/file3.txt"}
				if len(info.CheckSum) != len(expectedFiles) {
					t.Errorf("校验和文件数量不正确，期望%d，实际%d", len(expectedFiles), len(info.CheckSum))
				}
				
				// 验证所有期望的文件都有校验和
				for _, file := range expectedFiles {
					if _, ok := info.CheckSum[file]; !ok {
						t.Errorf("校验和中缺少文件: %s", file)
					}
				}
				
				// 验证校验和值不为0
				for file, checksum := range info.CheckSum {
					if checksum == 0 {
						t.Errorf("文件 %s 的校验和为0", file)
					}
				}
			},
			wantErr: false,
		},
		{
			name: "目录不存在",
			args: args{
				dirPath: "/non/existent/directory",
				filter:  func(path string) bool { return true },
			},
			setup: func(t *testing.T) (string, error) {
				return "/non/existent/directory", nil
			},
			cleanup: func(string) {},
			want:    nil,
			wantErr: true,
		},
		{
			name: "空目录",
			args: args{
				filter: func(path string) bool { return true },
			},
			setup: func(t *testing.T) (string, error) {
				// 创建空的临时目录
				return os.MkdirTemp("", "empty_checksum_test_*")
			},
			cleanup: func(tmpDir string) {
				os.RemoveAll(tmpDir)
			},
			want: func(t *testing.T, info *Info) {
				if info == nil {
					t.Errorf("校验和信息为空")
					return
				}
				
				// 验证空目录没有文件校验和
				if len(info.CheckSum) != 0 {
					t.Errorf("空目录应该没有文件校验和，实际有%d个", len(info.CheckSum))
				}
				
				// 验证没有排除的文件
				if len(info.Excludes) != 0 {
					t.Errorf("空目录不应该有排除的文件，实际有%d个", len(info.Excludes))
				}
			},
			wantErr: false,
		},
		{
			name: "全部排除",
			args: args{
				filter: func(path string) bool { return false },
			},
			setup: func(t *testing.T) (string, error) {
				// 创建临时测试目录
				tmpDir, err := os.MkdirTemp("", "exclude_all_checksum_test_*")
				if err != nil {
					return "", err
				}
				
				// 创建测试文件
				if err := os.WriteFile(filepath.Join(tmpDir, "file1.txt"), []byte("file1 content"), 0644); err != nil {
					return tmpDir, err
				}
				
				return tmpDir, nil
			},
			cleanup: func(tmpDir string) {
				os.RemoveAll(tmpDir)
			},
			want: func(t *testing.T, info *Info) {
				if info == nil {
					t.Errorf("校验和信息为空")
					return
				}
				
				// 验证没有文件校验和
				if len(info.CheckSum) != 0 {
					t.Errorf("应该没有文件校验和，实际有%d个", len(info.CheckSum))
				}
				
				// 验证所有文件都被排除
				if len(info.Excludes) == 0 {
					t.Errorf("应该有排除的文件，实际没有")
				}
			},
			wantErr: false,
		},
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试环境
			tmpDir, err := tt.setup(t)
			if err != nil {
				t.Fatalf("测试环境准备失败: %v", err)
			}
			defer tt.cleanup(tmpDir)
			
			// 设置目录路径
			dirPath := tmpDir
			if tt.args.dirPath != "" {
				dirPath = tt.args.dirPath
			}
			
			// 执行测试
			got, err := GenerateDirectoryChecksums(dirPath, tt.args.filter)
			
			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateDirectoryChecksums() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			
			// 额外验证
			if err == nil && tt.want != nil {
				tt.want(t, got)
			}
		})
	}
}

