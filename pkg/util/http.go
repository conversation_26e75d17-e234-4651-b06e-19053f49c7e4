package util

import (
	"bytes"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"time"
)

const (
	successCode = "200" // 成功的代码为200
)

var (
	defaultResponseCodeKey  = []string{"code", "status"}
	defaultResponseBodyKey  = []string{"data", "result"}
	defaultResponseMsgKey   = []string{"message", "msg"}
	defaultInnerSuccessCode = []string{"200"} // 可以根据需要扩展更多内部成功代码
)

var defaultHttpClient = NewHttpClient(30 * time.Second)

func GetDefaultHttpClient() *HttpClient {
	return defaultHttpClient
}

func GetHttpClient(clue string) *HttpClient {
	client := NewHttpClient(30 * time.Second)
	client.SetClue(clue)
	return client
}

type HttpClient struct {
	client           *http.Client
	innerSuccessCode []string
	clue             string
}

func NewHttpClient(timeout time.Duration) *HttpClient {
	return &HttpClient{
		client: &http.Client{Timeout: timeout},
	}
}

func NewHttpClientWithSuccessCode(timeout time.Duration, innerSuccessCode []string) *HttpClient {
	return &HttpClient{
		client:           &http.Client{Timeout: timeout},
		innerSuccessCode: innerSuccessCode,
	}
}

func NewHttpClientWithSuccessCodeAndClue(timeout time.Duration, innerSuccessCode []string, clue string) *HttpClient {
	return &HttpClient{
		client:           &http.Client{Timeout: timeout},
		innerSuccessCode: innerSuccessCode,
		clue:             clue,
	}
}

func (c *HttpClient) SetClue(clue string) {
	c.clue = clue
}

// PostWithResponseAndIsSuccess 发送POST请求并判断是否成功
func (c *HttpClient) PostWithResponseAndIsSuccess(url string, body interface{}, headers map[string]string) (bool, string, error) {
	response, err := c.sendRequest(http.MethodPost, url, body, headers)
	if err != nil {
		return false, response, err
	}
	success, err := IsResultSuccess(response, c.innerSuccessCode)
	return success, response, err
}

// PostString 发送POST请求并获取响应体为字符串
func (c *HttpClient) PostString(url string, body interface{}, headers map[string]string) (string, error) {
	return c.sendRequest(http.MethodPost, url, body, headers)
}

// PostStringWithResponse 发送POST请求并获取结构化的响应体
func (c *HttpClient) PostStringWithResponse(url string, body interface{}, headers map[string]string) (string, error) {
	response, err := c.sendRequest(http.MethodPost, url, body, headers)
	if err != nil {
		return "", err
	}
	return c.getResultString(true, response)
}

// Post 发送POST请求并反序列化到指定的对象
func (c *HttpClient) Post(url string, body interface{}, headers map[string]string, result interface{}) error {
	response, err := c.sendRequest(http.MethodPost, url, body, headers)
	if err != nil {
		return err
	}
	err = json.Unmarshal([]byte(response), result)
	if err != nil {
		err = errors.Wrapf(err, "response unmarshal failed, raw response body: %s", response)
	}
	return err
}

// PostWithResponse 发送POST请求并反序列化到指定的对象
func (c *HttpClient) PostWithResponse(url string, body interface{}, headers map[string]string, result interface{}) error {
	response, err := c.sendRequest(http.MethodPost, url, body, headers)
	if err != nil {
		return err
	}
	response, err = c.getResultString(true, response)
	if err != nil {
		return err
	}
	err = json.Unmarshal([]byte(response), result)
	if err != nil {
		err = errors.Wrapf(err, "response unmarshal failed, raw response body: %s", response)
	}
	return err
}

// GetString 发送GET请求并获取响应体为字符串
func (c *HttpClient) GetString(url string, headers map[string]string) (string, error) {
	return c.sendRequest(http.MethodGet, url, nil, headers)
}

// GetStringWithResponse 发送GET请求并获取结构化的响应体
func (c *HttpClient) GetStringWithResponse(url string, headers map[string]string) (string, error) {
	response, err := c.sendRequest(http.MethodGet, url, nil, headers)
	if err != nil {
		return "", err
	}
	return c.getResultString(true, response)
}

// Get 发送GET请求并反序列化到指定的对象
func (c *HttpClient) Get(url string, headers map[string]string, result interface{}) error {
	response, err := c.sendRequest(http.MethodGet, url, nil, headers)
	if err != nil {
		return err
	}
	err = json.Unmarshal([]byte(response), result)
	if err != nil {
		err = errors.Wrapf(err, "response unmarshal failed, raw response body: %s", response)
	}
	return err
}

// GetWithResponse 发送GET请求并反序列化到指定的对象
func (c *HttpClient) GetWithResponse(url string, headers map[string]string, result interface{}) error {
	response, err := c.sendRequest(http.MethodGet, url, nil, headers)
	if err != nil {
		return err
	}
	response, err = c.getResultString(true, response)
	if err != nil {
		return err
	}
	err = json.Unmarshal([]byte(response), result)
	if err != nil {
		err = errors.Wrapf(err, "response unmarshal failed, raw response body: %s", response)
	}
	return err
}

// 公共请求函数
func (c *HttpClient) sendRequest(method string, url string, body interface{}, headers map[string]string) (string, error) {
	var buf io.Reader
	if body != nil {
		jsonBytes, err := json.Marshal(body)
		if err != nil {
			return "", errors.Wrapf(err, "request body marshal failed, body: %v", body)
		}
		buf = bytes.NewBuffer(jsonBytes)
	}

	log.Info("[%s]request url: %s, method: %s, body: %s", c.clue, url, method, buf)

	req, err := http.NewRequest(method, url, buf)
	if err != nil {
		return "", errors.WithStack(err)
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}
	req.Header.Add("Content-Type", "application/json; charset=utf-8")

	resp, err := c.client.Do(req)
	if err != nil {
		return "", errors.WithStack(err)
	}
	defer resp.Body.Close()

	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", errors.WithStack(err)
	}
	rd := string(responseData)

	log.Info("[%s]request url: %s, response data: %s", c.clue, url, rd)

	return rd, nil
}

// getResultString parses the actual result from the response JSON based on whether it is wrapped as a response
func (c *HttpClient) getResultString(withResponse bool, responseJson string) (string, error) {
	if responseJson == "" || !withResponse {
		return responseJson, nil
	}

	// Parse the JSON response
	var bodyObj map[string]interface{}
	if err := json.Unmarshal([]byte(responseJson), &bodyObj); err != nil {
		return "", errors.Wrapf(err, "responseJson unmarshal failed, raw response body: %s", responseJson)
	}

	// Check if the request was successful
	var innerCode string
	for _, key := range defaultResponseCodeKey {
		cv, ok := bodyObj[key]
		if !ok {
			continue
		}
		code := fmt.Sprintf("%v", cv)
		if code != "" {
			innerCode = code
			break
		}
		// 可能有些响应code为int
		//if code, ok := bodyObj[key].(string); ok && code != "" {
		//	innerCode = code
		//	break
		//}
	}

	isSuccess := innerCode == successCode
	var isc []string
	if len(c.innerSuccessCode) > 0 {
		isc = c.innerSuccessCode
	} else {
		isc = defaultInnerSuccessCode
	}
	for _, code := range isc {
		if innerCode == code {
			isSuccess = true
			break
		}
	}

	if isSuccess {
		for _, key := range defaultResponseBodyKey {
			if bodyContent, ok := bodyObj[key]; ok {
				if data, ok := bodyContent.(string); ok && data != "" {
					return data, nil
				}
				bc, err := json.Marshal(bodyContent)
				if err != nil {
					return "", errors.Wrapf(err, "bodyContent marshal failed, raw bodyContent: %s", bodyContent)
				}
				return string(bc), nil
			}
		}
	} else {
		var message string
		for _, key := range defaultResponseMsgKey {
			if msg, ok := bodyObj[key].(string); ok && msg != "" {
				message = msg
				break
			}
		}
		if message == "" {
			message = "unknown error"
		}
		return "", errors.New(fmt.Sprintf("HTTP request failed, reason: %s. raw response body: %s", message, responseJson))
	}
	return responseJson, nil
}

func IsResultSuccess(responseJson string, innerSuccessCode []string) (bool, error) {
	// Parse the JSON response
	var bodyObj map[string]interface{}
	if err := json.Unmarshal([]byte(responseJson), &bodyObj); err != nil {
		return false, errors.Wrapf(err, "responseJson unmarshal failed, raw response body: %s", responseJson)
	}

	// Check if the request was successful
	var innerCode string
	for _, key := range defaultResponseCodeKey {
		code := fmt.Sprintf("%v", bodyObj[key])
		if code != "" {
			innerCode = code
			break
		}
	}

	isSuccess := innerCode == successCode
	var isc []string
	if innerSuccessCode != nil {
		isc = innerSuccessCode
	} else {
		isc = defaultInnerSuccessCode
	}
	for _, code := range isc {
		if innerCode == code {
			isSuccess = true
			break
		}
	}

	return isSuccess, nil
}
