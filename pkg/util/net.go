package util

import (
	"context"
	"dip-agent/pkg/core/api"
	"github.com/pkg/errors"
	"net"
	"net/http"
	"strings"
)

// GetIPAddress extracts the client's IP address from the http.Request.
func GetIPAddress(r *http.Request) string {
	headers := []string{"X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}
	for _, header := range headers {
		ip := r.Header.Get(header)
		if ip != "" && !strings.EqualFold(ip, "unknown") {
			if strings.Contains(ip, ",") {
				return strings.TrimSpace(strings.Split(ip, ",")[0])
			}
			return ip
		}
	}
	return r.RemoteAddr
}

func GetRequestId(r *http.Request) (string, *http.Request) {
	return GetRequestIdOr(r, "")
}

func GetRequestIdOr(r *http.Request, newRequestId string) (string, *http.Request) {
	var (
		ok        bool
		requestId string
	)
	value := r.Context().Value(api.RequestId)
	if value != nil {
		requestId, ok = value.(string)
	} else {
		ok = false
	}
	if !ok {
		// 尝试从请求头中获取requestId
		requestId = r.Header.Get(api.RequestId)
		if requestId == "" {
			if newRequestId == "" {
				requestId = GenerateUUID()
			} else {
				requestId = newRequestId
			}
		}
		ctx := context.WithValue(r.Context(), api.RequestId, requestId)
		r = r.WithContext(ctx)
	}
	return requestId, r
}

// GetLocalIP finds the first non-loopback IPv4 address of the machine
func GetLocalIP() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 遍历所有的网络接口
	for _, iface := range interfaces {
		if iface.Flags&net.FlagUp == 0 {
			continue // 接口未激活
		}
		if iface.Flags&net.FlagLoopback != 0 {
			continue // 忽略环回接口
		}

		addrs, err := iface.Addrs()
		if err != nil {
			return "", errors.WithStack(err)
		}

		// 检查接口上的所有地址
		for _, addr := range addrs {
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}

			// 我们查找IPv4地址，非环回地址
			if ip == nil || ip.IsLoopback() {
				continue
			}
			ip = ip.To4()
			if ip == nil {
				continue // 不是IPv4地址
			}

			return ip.String(), nil // 返回找到的第一个合法IPv4地址
		}
	}

	return "", errors.New("no active network interfaces found")
}
