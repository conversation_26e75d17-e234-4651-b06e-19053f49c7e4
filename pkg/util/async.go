package util

import (
	"context"
	"dip-agent/pkg/core/log"
	"sync"
	"sync/atomic"
)

// ProduceFunc 生产者函数
type ProduceFunc[T any] func(ctx context.Context, dataCh chan<- T) error

// ConsumeFunc 消费者函数
type ConsumeFunc[T any] func(item T) error

// AsyncProcess 开始生产者消费者模型的处理流程. 单生产者, 多消费者模式
func AsyncProcess[T any](clue string, produce ProduceFunc[T], consume ConsumeFunc[T], consumerCount int, failFast bool) []error {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建一个通道用于生产者和消费者之间的数据传递
	dataCh := make(chan T, consumerCount)
	// 创建一个等待组用于等待所有生产者和消费者完成
	var wg sync.WaitGroup

	// 错误处理
	errCh := make(chan error, 1)
	var errs atomic.Value
	errs.Store([]error{})

	// 启动错误监听
	go func() {
		defer log.Info("[%s]error listener done", clue)

		for {
			select {
			case <-ctx.Done():
				return
			case err, ok := <-errCh:
				if !ok {
					return
				}

				currentErrs := errs.Load().([]error)
				if failFast {
					if len(currentErrs) == 0 {
						currentErrs = append(currentErrs, err)
						errs.Store(currentErrs)
					}
					cancel() // 发生错误，通知中断所有进程
				} else {
					currentErrs = append(currentErrs, err)
					errs.Store(currentErrs)
				}
			}
		}
	}()

	// 启动生产者
	wg.Add(1)
	go func() {
		defer func() {
			wg.Done()
			close(dataCh) // 生产者完成后关闭数据通道, 用以通知消费者安全退出
			log.Info("[%s]producer done", clue)
		}()

		if err := produce(ctx, dataCh); err != nil {
			select {
			case <-ctx.Done():
				return
			case errCh <- err:
			}
		}
	}()

	// 启动消费者
	wg.Add(consumerCount)
	for i := 0; i < consumerCount; i++ {
		index := i
		go func() {
			defer func() {
				wg.Done()
				log.Info("[%s]consumer[%d/%d] done", clue, index, consumerCount)
			}()
			log.Info("[%s]consumer[%d/%d] start", clue, index, consumerCount)

			for {
				select {
				case <-ctx.Done():
					return
				case item, ok := <-dataCh:
					if !ok {
						return
					}
					if err := consume(item); err != nil {
						select {
						case <-ctx.Done():
							return
						case errCh <- err:
						}
					}
				}
			}
		}()
	}

	// 等待所有生产者完成后关闭数据通道
	wg.Wait()
	close(errCh)

	return errs.Load().([]error)
}

type Task[T any] func() (T, error)

// ExecuteTasks takes a slice of tasks, executes them concurrently, and returns a slice of results.
// If any task returns an error, the function will return that error (the first one encountered).
// Parameters:
//   - clue: A string to identify the task execution in logs.
//   - tasks: A slice of Task functions, each returning a value of type T and an error.
//
// Returns:
//   - A slice of results of type T, or an error if any task fails.
func ExecuteTasks[T any](clue string, tasks []Task[T]) ([]T, error) {
	taskSize := len(tasks)
	log.Info("[%s]execute tasks size: %d", clue, taskSize)

	var (
		wg      sync.WaitGroup
		results = make([]T, taskSize)
		err     atomic.Value
		mu      sync.Mutex // 添加互斥锁保护results切片的写入
	)

	wg.Add(taskSize)
	for i, task := range tasks {
		go func(index int, t Task[T]) {
			defer wg.Done()
			log.Info("[%s]execute task[%d]", clue, index)

			result, taskErr := t()
			if taskErr != nil {
				if err.Load() == nil {
					err.Store(taskErr)
				}
				return
			}

			// 使用互斥锁保护对results切片的写入操作
			mu.Lock()
			results[index] = result
			mu.Unlock()
		}(i, task)
	}

	wg.Wait()

	if loadedErr := err.Load(); loadedErr != nil {
		return nil, loadedErr.(error)
	}
	return results, nil
}
