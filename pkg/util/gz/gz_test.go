package gz

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/sonic"
	"github.com/klauspost/compress/gzip"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func TestDecompressOrMove(t *testing.T) {
	tests := []struct {
		name           string
		setupSrc       func(t *testing.T) (string, error)
		setupDest      func(t *testing.T) (string, error)
		removeOriginal bool
		wantErr        bool
		verify         func(t *testing.T, srcDir, destDir string)
	}{
		{
			name: "解压和移动混合文件",
			setupSrc: func(t *testing.T) (string, error) {
				// 创建源目录
				srcDir, err := ioutil.TempDir("", "decompress_move_src_*")
				if err != nil {
					return "", errors.Wrap(err, "创建源目录失败")
				}

				// 创建普通文件
				if err := os.WriteFile(filepath.Join(srcDir, "normal.txt"), []byte("normal file content"), 0644); err != nil {
					return srcDir, errors.Wrap(err, "创建普通文件失败")
				}

				// 创建子目录
				subDir := filepath.Join(srcDir, "subdir")
				if err := os.MkdirAll(subDir, 0755); err != nil {
					return srcDir, errors.Wrap(err, "创建子目录失败")
				}

				// 在子目录中创建普通文件
				if err := os.WriteFile(filepath.Join(subDir, "sub_normal.txt"), []byte("sub normal file content"), 0644); err != nil {
					return srcDir, errors.Wrap(err, "创建子目录普通文件失败")
				}

				// 创建gz文件
				gzFile := filepath.Join(srcDir, "compressed.txt.gz")
				if err := compressFile(filepath.Join(srcDir, "normal.txt"), gzFile, false); err != nil {
					return srcDir, errors.Wrap(err, "创建gz文件失败")
				}

				// 在子目录中创建gz文件
				subGzFile := filepath.Join(subDir, "sub_compressed.txt.gz")
				if err := compressFile(filepath.Join(subDir, "sub_normal.txt"), subGzFile, false); err != nil {
					return srcDir, errors.Wrap(err, "创建子目录gz文件失败")
				}

				return srcDir, nil
			},
			setupDest: func(t *testing.T) (string, error) {
				// 创建目标目录
				return ioutil.TempDir("", "decompress_move_dest_*")
			},
			removeOriginal: true,
			wantErr:        false,
			verify: func(t *testing.T, srcDir, destDir string) {
				// 验证普通文件被移动
				normalDestPath := filepath.Join(destDir, "normal.txt")
				assert.True(t, fileExists(normalDestPath), "普通文件应该被移动到目标目录")
				content, err := os.ReadFile(normalDestPath)
				assert.NoError(t, err, "读取移动后的普通文件失败")
				assert.Equal(t, "normal file content", string(content), "移动后的普通文件内容不正确")

				// 验证gz文件被解压
				compressedDestPath := filepath.Join(destDir, "compressed.txt")
				assert.True(t, fileExists(compressedDestPath), "压缩文件应该被解压到目标目录")
				content, err = os.ReadFile(compressedDestPath)
				assert.NoError(t, err, "读取解压后的文件失败")
				assert.Equal(t, "normal file content", string(content), "解压后的文件内容不正确")

				// 验证子目录结构被保留
				subDirDestPath := filepath.Join(destDir, "subdir")
				assert.True(t, dirExists(subDirDestPath), "子目录结构应该被保留")

				// 验证子目录中的普通文件被移动
				subNormalDestPath := filepath.Join(subDirDestPath, "sub_normal.txt")
				assert.True(t, fileExists(subNormalDestPath), "子目录中的普通文件应该被移动")
				content, err = os.ReadFile(subNormalDestPath)
				assert.NoError(t, err, "读取移动后的子目录普通文件失败")
				assert.Equal(t, "sub normal file content", string(content), "移动后的子目录普通文件内容不正确")

				// 验证子目录中的gz文件被解压
				subCompressedDestPath := filepath.Join(subDirDestPath, "sub_compressed.txt")
				assert.True(t, fileExists(subCompressedDestPath), "子目录中的压缩文件应该被解压")
				content, err = os.ReadFile(subCompressedDestPath)
				assert.NoError(t, err, "读取解压后的子目录文件失败")
				assert.Equal(t, "sub normal file content", string(content), "解压后的子目录文件内容不正确")

				// 如果设置了removeOriginal，验证源文件被删除
				if t.Name() == "TestDecompressOrMove/解压和移动混合文件" {
					assert.False(t, fileExists(filepath.Join(srcDir, "normal.txt")), "源目录中的普通文件应该被删除")
					assert.False(t, fileExists(filepath.Join(srcDir, "compressed.txt.gz")), "源目录中的压缩文件应该被删除")
					assert.False(t, fileExists(filepath.Join(srcDir, "subdir", "sub_normal.txt")), "源目录子目录中的普通文件应该被删除")
					assert.False(t, fileExists(filepath.Join(srcDir, "subdir", "sub_compressed.txt.gz")), "源目录子目录中的压缩文件应该被删除")
				}
			},
		},
		{
			name: "不删除原始文件",
			setupSrc: func(t *testing.T) (string, error) {
				// 创建源目录
				srcDir, err := ioutil.TempDir("", "decompress_move_src_keep_*")
				if err != nil {
					return "", errors.Wrap(err, "创建源目录失败")
				}

				// 创建普通文件
				if err := os.WriteFile(filepath.Join(srcDir, "keep.txt"), []byte("keep content"), 0644); err != nil {
					return srcDir, errors.Wrap(err, "创建普通文件失败")
				}

				// 创建gz文件
				gzFile := filepath.Join(srcDir, "keep_compressed.txt.gz")
				if err := compressFile(filepath.Join(srcDir, "keep.txt"), gzFile, false); err != nil {
					return srcDir, errors.Wrap(err, "创建gz文件失败")
				}

				return srcDir, nil
			},
			setupDest: func(t *testing.T) (string, error) {
				return ioutil.TempDir("", "decompress_move_dest_keep_*")
			},
			removeOriginal: false,
			wantErr:        false,
			verify: func(t *testing.T, srcDir, destDir string) {
				// 验证目标目录中的文件
				assert.True(t, fileExists(filepath.Join(destDir, "keep.txt")), "普通文件应该被移动到目标目录")
				assert.True(t, fileExists(filepath.Join(destDir, "keep_compressed.txt")), "压缩文件应该被解压到目标目录")

				// 验证源目录中的文件仍然存在
				assert.True(t, fileExists(filepath.Join(srcDir, "keep.txt")), "源目录中的普通文件应该保留")
				assert.True(t, fileExists(filepath.Join(srcDir, "keep_compressed.txt.gz")), "源目录中的压缩文件应该保留")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备源目录
			srcDir, err := tt.setupSrc(t)
			if err != nil {
				t.Fatalf("准备源目录失败: %v", err)
			}
			if !strings.HasPrefix(srcDir, "/non") {
				defer os.RemoveAll(srcDir)
			}

			// 准备目标目录
			destDir, err := tt.setupDest(t)
			if err != nil {
				t.Fatalf("准备目标目录失败: %v", err)
			}
			defer os.RemoveAll(filepath.Dir(destDir))

			// 执行测试
			err = DecompressOrMove(srcDir, destDir, tt.removeOriginal)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("DecompressOrMove() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 额外验证
			if err == nil && tt.verify != nil {
				tt.verify(t, srcDir, destDir)
			}
		})
	}
}

// 辅助函数：检查文件是否存在
func fileExists(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		return false
	}
	return !info.IsDir()
}

// 辅助函数：检查目录是否存在
func dirExists(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		return false
	}
	return info.IsDir()
}

func TestCompressGzipFiles(t *testing.T) {
	type args struct {
		dirPath        string
		removeOriginal bool
		filter         func(fileName string) bool
	}
	tests := []struct {
		name    string
		args    args
		setup   func(t *testing.T) (string, error)
		cleanup func(string)
		wantErr bool
		verify  func(t *testing.T, dirPath string)
	}{
		{
			name: "正常压缩文件",
			args: args{
				removeOriginal: true,
				filter: func(fileName string) bool {
					return !strings.HasSuffix(fileName, "exclude.txt")
				},
			},
			setup: func(t *testing.T) (string, error) {
				// 创建临时测试目录
				tmpDir, err := ioutil.TempDir("", "compress_test_*")
				if err != nil {
					return "", errors.Wrap(err, "创建临时目录失败")
				}

				// 创建测试文件
				files := []struct {
					name    string
					content string
				}{
					{"file1.txt", "file1 content"},
					{"file2.txt", "file2 content"},
					{"exclude.txt", "excluded content"},
				}

				for _, f := range files {
					if err := os.WriteFile(filepath.Join(tmpDir, f.name), []byte(f.content), 0644); err != nil {
						return tmpDir, errors.Wrapf(err, "创建文件 %s 失败", f.name)
					}
				}

				// 创建子目录
				subDir := filepath.Join(tmpDir, "subdir")
				if err := os.MkdirAll(subDir, 0755); err != nil {
					return tmpDir, errors.Wrap(err, "创建子目录失败")
				}

				// 在子目录中创建文件
				if err := os.WriteFile(filepath.Join(subDir, "subfile.txt"), []byte("subfile content"), 0644); err != nil {
					return tmpDir, errors.Wrap(err, "创建子目录文件失败")
				}

				return tmpDir, nil
			},
			cleanup: func(dirPath string) {
				os.RemoveAll(dirPath)
			},
			wantErr: false,
			verify: func(t *testing.T, dirPath string) {
				// 验证文件被压缩
				assert.True(t, fileExists(filepath.Join(dirPath, "file1.txt.gz")), "file1.txt 应该被压缩")
				assert.True(t, fileExists(filepath.Join(dirPath, "file2.txt.gz")), "file2.txt 应该被压缩")
				assert.True(t, fileExists(filepath.Join(dirPath, "subdir/subfile.txt.gz")), "subdir/subfile.txt 应该被压缩")

				// 验证排除的文件没有被压缩
				assert.True(t, fileExists(filepath.Join(dirPath, "exclude.txt")), "exclude.txt 不应该被压缩")
				assert.False(t, fileExists(filepath.Join(dirPath, "exclude.txt.gz")), "exclude.txt 不应该有对应的 gz 文件")

				// 验证原始文件被删除
				assert.False(t, fileExists(filepath.Join(dirPath, "file1.txt")), "file1.txt 应该被删除")
				assert.False(t, fileExists(filepath.Join(dirPath, "file2.txt")), "file2.txt 应该被删除")
				assert.False(t, fileExists(filepath.Join(dirPath, "subdir/subfile.txt")), "subdir/subfile.txt 应该被删除")

				// 验证压缩文件内容正确
				// 解压第一个文件并验证内容
				gzFile, err := os.Open(filepath.Join(dirPath, "file1.txt.gz"))
				assert.NoError(t, err, "打开压缩文件失败")
				defer gzFile.Close()

				gzReader, err := gzip.NewReader(gzFile)
				assert.NoError(t, err, "创建gzip reader失败")
				defer gzReader.Close()

				content, err := io.ReadAll(gzReader)
				assert.NoError(t, err, "读取解压内容失败")
				assert.Equal(t, "file1 content", string(content), "解压内容不匹配")
			},
		},
		{
			name: "不删除原始文件",
			args: args{
				removeOriginal: false,
				filter: func(fileName string) bool {
					return true
				},
			},
			setup: func(t *testing.T) (string, error) {
				// 创建临时测试目录
				tmpDir, err := ioutil.TempDir("", "compress_keep_test_*")
				if err != nil {
					return "", errors.Wrap(err, "创建临时目录失败")
				}

				// 创建测试文件
				if err := os.WriteFile(filepath.Join(tmpDir, "keep.txt"), []byte("keep content"), 0644); err != nil {
					return tmpDir, errors.Wrap(err, "创建文件失败")
				}

				return tmpDir, nil
			},
			cleanup: func(dirPath string) {
				os.RemoveAll(dirPath)
			},
			wantErr: false,
			verify: func(t *testing.T, dirPath string) {
				// 验证文件被压缩
				assert.True(t, fileExists(filepath.Join(dirPath, "keep.txt.gz")), "keep.txt 应该被压缩")

				// 验证原始文件被保留
				assert.True(t, fileExists(filepath.Join(dirPath, "keep.txt")), "keep.txt 应该被保留")

				// 验证压缩文件内容正确
				gzFile, err := os.Open(filepath.Join(dirPath, "keep.txt.gz"))
				assert.NoError(t, err, "打开压缩文件失败")
				defer gzFile.Close()

				gzReader, err := gzip.NewReader(gzFile)
				assert.NoError(t, err, "创建gzip reader失败")
				defer gzReader.Close()

				content, err := io.ReadAll(gzReader)
				assert.NoError(t, err, "读取解压内容失败")
				assert.Equal(t, "keep content", string(content), "解压内容不匹配")
			},
		},
		{
			name: "目录不存在",
			args: args{
				dirPath:        "/non/existent/directory",
				removeOriginal: true,
				filter: func(fileName string) bool {
					return true
				},
			},
			setup: func(t *testing.T) (string, error) {
				return "/non/existent/directory", nil
			},
			cleanup: func(string) {},
			wantErr: true,
			verify:  nil,
		},
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试环境
			dirPath, err := tt.setup(t)
			if err != nil {
				t.Fatalf("准备测试环境失败: %v", err)
			}
			if !strings.HasPrefix(dirPath, "/non") {
				defer tt.cleanup(dirPath)
			}

			// 设置目录路径
			if tt.args.dirPath == "" {
				tt.args.dirPath = dirPath
			}

			// 执行测试
			err = CompressGzipFiles(tt.args.dirPath, tt.args.removeOriginal, tt.args.filter)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("CompressGzipFiles() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 额外验证
			if err == nil && tt.verify != nil {
				tt.verify(t, tt.args.dirPath)
			}
		})
	}
}

func TestDecompressGzipFiles(t *testing.T) {
	type args struct {
		dirPath        string
		removeOriginal bool
		filter         func(fileName string) bool
		concurrency    int
	}
	tests := []struct {
		name    string
		args    args
		setup   func(t *testing.T) (string, error)
		cleanup func(string)
		wantErr bool
		verify  func(t *testing.T, dirPath string)
	}{
		{
			name: "正常解压文件",
			args: args{
				removeOriginal: true,
				concurrency:    4,
				filter: func(fileName string) bool {
					return !strings.HasSuffix(fileName, "exclude.txt.gz")
				},
			},
			setup: func(t *testing.T) (string, error) {
				// 创建临时测试目录
				tmpDir, err := ioutil.TempDir("", "decompress_test_*")
				if err != nil {
					return "", errors.Wrap(err, "创建临时目录失败")
				}

				// 创建测试文件并压缩
				files := []struct {
					name    string
					content string
				}{
					{"file1.txt", "file1 content"},
					{"file2.txt", "file2 content"},
					{"exclude.txt", "excluded content"},
				}

				for _, f := range files {
					filePath := filepath.Join(tmpDir, f.name)
					if err := os.WriteFile(filePath, []byte(f.content), 0644); err != nil {
						return tmpDir, errors.Wrapf(err, "创建文件 %s 失败", f.name)
					}

					// 压缩文件
					gzPath := filePath + ".gz"
					if err := compressFile(filePath, gzPath, true); err != nil {
						return tmpDir, errors.Wrapf(err, "压缩文件 %s 失败", f.name)
					}
				}

				// 创建子目录
				subDir := filepath.Join(tmpDir, "subdir")
				if err := os.MkdirAll(subDir, 0755); err != nil {
					return tmpDir, errors.Wrap(err, "创建子目录失败")
				}

				// 在子目录中创建并压缩文件
				subFilePath := filepath.Join(subDir, "subfile.txt")
				if err := os.WriteFile(subFilePath, []byte("subfile content"), 0644); err != nil {
					return tmpDir, errors.Wrap(err, "创建子目录文件失败")
				}
				if err := compressFile(subFilePath, subFilePath+".gz", true); err != nil {
					return tmpDir, errors.Wrap(err, "压缩子目录文件失败")
				}

				return tmpDir, nil
			},
			cleanup: func(dirPath string) {
				os.RemoveAll(dirPath)
			},
			wantErr: false,
			verify: func(t *testing.T, dirPath string) {
				// 验证文件被解压
				assert.True(t, fileExists(filepath.Join(dirPath, "file1.txt")), "file1.txt 应该被解压")
				assert.True(t, fileExists(filepath.Join(dirPath, "file2.txt")), "file2.txt 应该被解压")
				assert.True(t, fileExists(filepath.Join(dirPath, "subdir/subfile.txt")), "subdir/subfile.txt 应该被解压")

				// 验证排除的文件没有被解压
				assert.True(t, fileExists(filepath.Join(dirPath, "exclude.txt.gz")), "exclude.txt.gz 不应该被解压")
				assert.False(t, fileExists(filepath.Join(dirPath, "exclude.txt")), "exclude.txt 不应该存在")

				// 验证原始压缩文件被删除
				assert.False(t, fileExists(filepath.Join(dirPath, "file1.txt.gz")), "file1.txt.gz 应该被删除")
				assert.False(t, fileExists(filepath.Join(dirPath, "file2.txt.gz")), "file2.txt.gz 应该被删除")
				assert.False(t, fileExists(filepath.Join(dirPath, "subdir/subfile.txt.gz")), "subdir/subfile.txt.gz 应该被删除")

				// 验证解压文件内容正确
				content, err := os.ReadFile(filepath.Join(dirPath, "file1.txt"))
				assert.NoError(t, err, "读取解压文件失败")
				assert.Equal(t, "file1 content", string(content), "解压内容不匹配")

				content, err = os.ReadFile(filepath.Join(dirPath, "subdir/subfile.txt"))
				assert.NoError(t, err, "读取解压子目录文件失败")
				assert.Equal(t, "subfile content", string(content), "解压子目录文件内容不匹配")
			},
		},
		{
			name: "不删除原始压缩文件",
			args: args{
				removeOriginal: false,
				concurrency:    2,
				filter: func(fileName string) bool {
					return true
				},
			},
			setup: func(t *testing.T) (string, error) {
				// 创建临时测试目录
				tmpDir, err := ioutil.TempDir("", "decompress_keep_test_*")
				if err != nil {
					return "", errors.Wrap(err, "创建临时目录失败")
				}

				// 创建并压缩测试文件
				filePath := filepath.Join(tmpDir, "keep.txt")
				if err := os.WriteFile(filePath, []byte("keep content"), 0644); err != nil {
					return tmpDir, errors.Wrap(err, "创建文件失败")
				}
				if err := compressFile(filePath, filePath+".gz", true); err != nil {
					return tmpDir, errors.Wrap(err, "压缩文件失败")
				}

				return tmpDir, nil
			},
			cleanup: func(dirPath string) {
				os.RemoveAll(dirPath)
			},
			wantErr: false,
			verify: func(t *testing.T, dirPath string) {
				// 验证文件被解压
				assert.True(t, fileExists(filepath.Join(dirPath, "keep.txt")), "keep.txt 应该被解压")

				// 验证原始压缩文件被保留
				assert.True(t, fileExists(filepath.Join(dirPath, "keep.txt.gz")), "keep.txt.gz 应该被保留")

				// 验证解压文件内容正确
				content, err := os.ReadFile(filepath.Join(dirPath, "keep.txt"))
				assert.NoError(t, err, "读取解压文件失败")
				assert.Equal(t, "keep content", string(content), "解压内容不匹配")
			},
		},
		{
			name: "目录不存在",
			args: args{
				dirPath:        "/non/existent/directory",
				removeOriginal: true,
				concurrency:    1,
				filter: func(fileName string) bool {
					return true
				},
			},
			setup: func(t *testing.T) (string, error) {
				return "/non/existent/directory", nil
			},
			cleanup: func(string) {},
			wantErr: true,
			verify:  nil,
		},
		{
			name: "自动设置并发数",
			args: args{
				removeOriginal: true,
				concurrency:    0, // 应该自动设置为 runtime.GOMAXPROCS(-1) + 1
				filter: func(fileName string) bool {
					return true
				},
			},
			setup: func(t *testing.T) (string, error) {
				// 创建临时测试目录
				tmpDir, err := ioutil.TempDir("", "decompress_auto_concurrency_*")
				if err != nil {
					return "", errors.Wrap(err, "创建临时目录失败")
				}

				// 创建并压缩测试文件
				filePath := filepath.Join(tmpDir, "auto.txt")
				if err := os.WriteFile(filePath, []byte("auto content"), 0644); err != nil {
					return tmpDir, errors.Wrap(err, "创建文件失败")
				}
				if err := compressFile(filePath, filePath+".gz", true); err != nil {
					return tmpDir, errors.Wrap(err, "压缩文件失败")
				}

				return tmpDir, nil
			},
			cleanup: func(dirPath string) {
				os.RemoveAll(dirPath)
			},
			wantErr: false,
			verify: func(t *testing.T, dirPath string) {
				// 验证文件被解压
				assert.True(t, fileExists(filepath.Join(dirPath, "auto.txt")), "auto.txt 应该被解压")
				
				// 验证原始压缩文件被删除
				assert.False(t, fileExists(filepath.Join(dirPath, "auto.txt.gz")), "auto.txt.gz 应该被删除")
				
				// 验证解压文件内容正确
				content, err := os.ReadFile(filepath.Join(dirPath, "auto.txt"))
				assert.NoError(t, err, "读取解压文件失败")
				assert.Equal(t, "auto content", string(content), "解压内容不匹配")
			},
		},
	}

	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试环境
			dirPath, err := tt.setup(t)
			if err != nil {
				t.Fatalf("准备测试环境失败: %v", err)
			}
			if !strings.HasPrefix(dirPath, "/non") {
				defer tt.cleanup(dirPath)
			}

			// 设置目录路径
			if tt.args.dirPath == "" {
				tt.args.dirPath = dirPath
			}

			// 执行测试
			err = DecompressGzipFiles(tt.args.dirPath, tt.args.removeOriginal, tt.args.filter, tt.args.concurrency)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("DecompressGzipFiles() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 额外验证
			if err == nil && tt.verify != nil {
				tt.verify(t, tt.args.dirPath)
			}
		})
	}
}
