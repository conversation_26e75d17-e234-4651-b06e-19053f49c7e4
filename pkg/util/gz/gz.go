package gz

import (
	"compress/gzip"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"github.com/pkg/errors"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/klauspost/pgzip"
)

// CompressGzipFiles 压缩指定目录中的文件为 .gz 格式
// dirPath: 目录路径
// removeOriginal: 是否删除原始文件
// filter: 函数，决定是否压缩特定文件，返回 true 表示压缩, 入参为相对路径
func CompressGzipFiles(dirPath string, removeOriginal bool, filter func(fileName string) bool) error {
	// 设置goroutines的最大并发数，可以需求和资源限制调整
	threadNum := runtime.GOMAXPROCS(-1) + 1
	maxGoroutines := threadNum
	log.Info("compress dir %s, max goroutines: %d", dirPath, maxGoroutines)

	var (
		err error

		wg                 sync.WaitGroup
		asyncErr           atomic.Value
		goroutineSemaphore = make(chan struct{}, maxGoroutines)
	)

	// 遍历目录中的所有文件
	err = filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 跳过root dir
		if path == dirPath {
			return nil
		}

		// 获取相对路径
		relativePath, err := filepath.Rel(dirPath, path)
		if err != nil {
			return errors.Wrapf(err, "failed to get relative path of %s", path)
		}

		if filter != nil && !filter(relativePath) {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		if info.IsDir() {
			return nil
		}

		// 过滤.gz文件
		if filepath.Ext(path) == ".gz" {
			return nil
		}

		// 异步压缩
		wg.Add(1)
		goroutineSemaphore <- struct{}{} // 获取令牌
		go func(path string) {
			defer wg.Done()
			defer func() { <-goroutineSemaphore }() // 释放令牌

			// 压缩文件
			err = compressGzipFile(path)
			if err != nil {
				log.Error("failed to compress %s: %v", path, err)
				asyncErr.Store(err)
			}

			// 如果需要，删除原始文件
			if removeOriginal {
				err = os.Remove(path)
				if err != nil {
					err = errors.Wrapf(err, "failed to remove file %s", path)
					log.Error("failed to remove file %s: %+v", path, err)
					asyncErr.Store(err)
				}
			}
		}(path)

		return nil
	})

	wg.Wait() // 等待所有goroutine完成

	// 检查是否有错误
	if err != nil {
		return err
	}
	if err, ok := asyncErr.Load().(error); ok {
		return err
	}

	return nil
}

// compressGzipFile 压缩单个文件为 .gz 格式
func compressGzipFile(filePath string) error {
	log.Info("compress file: %s", filePath)
	// 打开原始文件
	originalFile, err := os.Open(filePath)
	if err != nil {
		return errors.Wrapf(err, "failed to open file %s", filePath)
	}
	defer originalFile.Close()

	// 创建 gzip 文件
	gzipPath := filePath + ".gz"
	gzipFile, err := os.Create(gzipPath)
	if err != nil {
		return errors.Wrapf(err, "failed to create file %s", gzipPath)
	}
	defer gzipFile.Close()

	// 创建 gzip Writer
	// 使用pgzip代替标准库gzip，提高性能
	pw := pgzip.NewWriter(gzipFile)
	err = pw.SetConcurrency(1<<20, 4)
	if err != nil {
		return errors.Wrapf(err, "failed to set concurrency for gzip writer")
	}
	defer pw.Close()
	//gzipWriter := gzip.NewWriter(gzipFile)
	//defer gzipWriter.Close()

	// 从原始文件复制数据到 gzip Writer
	_, err = io.Copy(pw, originalFile)
	if err != nil {
		return errors.Wrapf(err, "failed to copy data from %s to gzip writer", filePath)
	}

	return nil
}

// DecompressGzipFiles 解压指定目录中的 .gz 文件
// dirPath: 目录路径
// removeOriginal: 是否删除原始 .gz 文件
// filter: 函数，决定是否解压特定文件，返回 true 表示解压
// concurrency: 解压并发数
func DecompressGzipFiles(dirPath string, removeOriginal bool, filter func(fileName string) bool, concurrency int) error {
	// 设置goroutines的最大并发数
	if concurrency < 1 {
		concurrency = runtime.GOMAXPROCS(-1) + 1
	}
	log.Info("decompress dir %s, max concurrency: %d", dirPath, concurrency)

	var (
		err error

		wg                 sync.WaitGroup
		asyncErr           atomic.Value
		goroutineSemaphore = make(chan struct{}, concurrency)
	)

	// 遍历目录中的所有文件
	err = filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 跳过root dir
		if path == dirPath {
			return nil
		}

		// 获取相对路径
		relativePath, err := filepath.Rel(dirPath, path)
		if err != nil {
			return errors.Wrapf(err, "failed to get relative path of %s", path)
		}

		if filter != nil && !filter(relativePath) {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}
		if info.IsDir() || filepath.Ext(path) != ".gz" {
			return nil
		}

		// 异步解压
		wg.Add(1)
		goroutineSemaphore <- struct{}{} // 获取令牌
		go func() {
			defer wg.Done()
			defer func() { <-goroutineSemaphore }() // 释放令牌

			// 解压 .gz 文件
			err = decompressGzipFile(path, removeOriginal)
			if err != nil {
				asyncErr.Store(err)
			}
		}()

		return nil
	})

	wg.Wait() // 等待所有goroutine完成

	if err != nil {
		return err
	}

	if err, ok := asyncErr.Load().(error); ok {
		return err
	}

	return nil
}

// decompressGzipFile 解压单个 .gz 文件
func decompressGzipFile(filePath string, removeOriginal bool) error {
	// 打开 gzip 文件
	gzFile, err := os.Open(filePath)
	if err != nil {
		return errors.Wrapf(err, "failed to open file %s", filePath)
	}
	defer gzFile.Close()

	gzipReader, err := pgzip.NewReader(gzFile)
	if err != nil {
		return errors.Wrapf(err, "failed to create gzip reader for file %s", filePath)
	}
	defer gzipReader.Close()

	// 创建解压后的文件
	outputFilePath := filepath.Join(filepath.Dir(filePath), filepath.Base(filePath[:len(filePath)-len(".gz")]))
	outputFile, err := os.Create(outputFilePath)
	if err != nil {
		return errors.Wrapf(err, "failed to create file %s", outputFilePath)
	}
	defer outputFile.Close()

	// 将内容从 gzip 解压到新文件
	_, err = io.Copy(outputFile, gzipReader)
	if err != nil {
		return errors.Wrapf(err, "failed to copy data from %s to %s", filePath, outputFilePath)
	}

	// 根据需要删除原始 .gz 文件
	if removeOriginal {
		err = os.Remove(filePath)
		if err != nil && !os.IsNotExist(err) {
			return errors.Wrapf(err, "failed to remove file %s", filePath)
		}
	}

	return nil
}

// DecompressOrMove 解压缩或根据需要移动文件，同时保持目录结构。
// removeOriginal: 是否删除原始文件
func DecompressOrMove(srcDir, destDir string, removeOriginal bool) error {
	// 遍历源目录中的所有文件和目录
	return filepath.Walk(srcDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过源目录本身
		if path == srcDir {
			return nil
		}

		// 构建目标文件路径，保持原始子目录结构
		relativePath, err := filepath.Rel(srcDir, path)
		if err != nil {
			return errors.Wrapf(err, "failed to get relative path of %s", path)
		}
		destFilePath := filepath.Join(destDir, relativePath)

		// 检查是文件还是目录
		if info.IsDir() {
			// 创建目标目录
			err := os.MkdirAll(destFilePath, info.Mode())
			if err != nil {
				return errors.Wrapf(err, "failed to create dir %s", destFilePath)
			}
			return nil
		} else {
			// 检查文件是否为.gz压缩文件
			if strings.HasSuffix(path, ".gz") {
				return DecompressGZFile(path, destFilePath, removeOriginal)
			} else {
				if removeOriginal {
					return moveFile(path, destFilePath)
				}
				return copyFile(path, destFilePath)
			}
		}
	})
}

// DecompressGZFile 解压单个.gz文件到指定路径，不包含.gz扩展。
func DecompressGZFile(srcFilePath, destFilePath string, removeOriginal bool) error {
	srcFile, err := os.Open(srcFilePath)
	if err != nil {
		return errors.Wrapf(err, "failed to open file %s", srcFilePath)
	}
	defer srcFile.Close()

	gr, err := gzip.NewReader(srcFile)
	if err != nil {
		return errors.Wrapf(err, "failed to create gzip reader for file %s", srcFilePath)
	}
	defer gr.Close()

	// 保证解压目标路径所在的目录存在
	destDir := filepath.Dir(destFilePath)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return errors.Wrapf(err, "failed to create dir %s", destDir)
	}

	destFile, err := os.Create(strings.TrimSuffix(destFilePath, ".gz"))
	if err != nil {
		return errors.Wrapf(err, "failed to create file %s", destFilePath)
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, gr)
	if err != nil {
		return errors.Wrapf(err, "failed to copy data from %s to %s", srcFilePath, destFilePath)
	}

	// 如果指定删除原文件，解压后删除
	if removeOriginal {
		err = os.Remove(srcFilePath)
		if err != nil && !os.IsNotExist(err) {
			return errors.Wrapf(err, "failed to remove file %s", srcFilePath)
		}
	}

	return nil
}

// moveFile 移动文件到新的位置，确保目标目录存在。
func moveFile(srcFilePath, destFilePath string) error {
	destDir := filepath.Dir(destFilePath)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return errors.Wrapf(err, "failed to create dir %s", destDir)
	}
	err := os.Rename(srcFilePath, destFilePath)
	if err != nil {
		return errors.Wrapf(err, "failed to rename file from %s to %s", srcFilePath, destFilePath)
	}
	return nil
}

// copyFile 复制文件到新的位置，确保目标目录存在。
func copyFile(srcFilePath, destFilePath string) error {
	destDir := filepath.Dir(destFilePath)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return errors.Wrapf(err, "failed to create dir %s", destDir)
	}
	inputFile, err := os.Open(srcFilePath)
	if err != nil {
		return errors.Wrapf(err, "failed to open file %s", srcFilePath)
	}
	defer inputFile.Close()

	outputFile, err := os.Create(destFilePath)
	if err != nil {
		return errors.Wrapf(err, "failed to create file %s", destFilePath)
	}
	defer outputFile.Close()

	_, err = io.Copy(outputFile, inputFile)
	if err != nil {
		return errors.Wrapf(err, "failed to copy file from %s to %s", srcFilePath, destFilePath)
	}

	return nil
}

// CompressFilesInDir 压缩指定目录中的文件，支持排除特定文件或目录
// dirPath: 要压缩的目录路径
// excludes: 排除的文件或目录名称列表(相对路径, 相对dirPath)
// removeOriginal: 是否删除原始文件
func CompressFilesInDir(dirPath string, excludes []string, removeOriginal bool) error {
	return filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过根目录
		if path == dirPath {
			return nil
		}

		// 获取相对路径，用于检查是否排除
		relativePath, err := filepath.Rel(dirPath, path)
		if err != nil {
			return errors.Wrapf(err, "failed to get relative path of %s", path)
		}

		// 检查当前路径是否应该被排除
		if util.ContainsString(excludes, relativePath) {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// 忽略目录本身，只处理文件
		if info.IsDir() {
			return nil
		}

		// 如果已经是.gz文件，不再压缩
		if strings.HasSuffix(path, ".gz") {
			return nil
		}

		// 生成压缩文件的路径
		gzPath := path + ".gz"
		return compressFile(path, gzPath, removeOriginal)
	})
}

// compressFile 将给定的文件压缩为.gz格式
func compressFile(src, dst string, removeOriginal bool) error {
	// 打开源文件
	inputFile, err := os.Open(src)
	if err != nil {
		return errors.Wrapf(err, "failed to open file %s", src)
	}
	defer inputFile.Close()

	// 创建目标gzip文件
	outputFile, err := os.Create(dst)
	if err != nil {
		return errors.Wrapf(err, "failed to create gzip file %s", dst)
	}
	defer outputFile.Close()

	// 创建gzip.Writer
	gzWriter := gzip.NewWriter(outputFile)
	defer gzWriter.Close()

	// 将文件内容复制到gzip.Writer
	_, err = io.Copy(gzWriter, inputFile)
	if err != nil {
		return errors.Wrapf(err, "failed to copy file %s to gzip writer", src)
	}

	// 如果指定删除原文件，压缩后删除
	if removeOriginal {
		err = os.Remove(src)
		if err != nil && !os.IsNotExist(err) {
			return errors.Wrapf(err, "failed to remove file %s", src)
		}
	}

	return gzWriter.Close()
}
