package util

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"dip-agent/pkg/util/json/sonic"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"testing"

	"github.com/pkg/errors"
)

func TestRemoveAllExcept(t *testing.T) {
	type args struct {
		dirPath  string
		excludes []string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				dirPath: "/tmp/dip/data/cdb/invert_deal_rec_ranklist_product2list/202408081600",
				excludes: []string{
					"dump",
					"test",
				},
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := RemoveAllExcept(tt.args.dirPath, tt.args.excludes); (err != nil) != tt.wantErr {
				t.Errorf("RemoveAllExcept() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestWriteJsonToFile(t *testing.T) {
	type args struct {
		filePath string
		data     interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				filePath: "/tmp/dip/data/cdb/invert_deal_rec_ranklist_product2list/202408081600/test.json",
				data: map[string]interface{}{
					"key1": "value1",
					"key2": "value2",
				},
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := WriteJsonToFile(tt.args.filePath, tt.args.data, false); (err != nil) != tt.wantErr {
				t.Errorf("WriteJsonToFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestEnsureDir(t *testing.T) {
	type args struct {
		filePath string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				filePath: "/tmp/dip1/data/tdata/0.done",
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path := tt.args.filePath
			dir := filepath.Dir(path)
			dir += "/"
			log.Info("dir: %s", dir)
			if err := EnsureDir(dir); (err != nil) != tt.wantErr {
				t.Errorf("EnsureDir() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCopyFile(t *testing.T) {
	type args struct {
		src    string
		dstDir string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testCopyFile",
			args: args{
				src:    "/tmp/dump/deal_qianchuan_brand_basic_recall/202408251400/0.done",
				dstDir: "/tmp/dump/deal_qianchuan_brand_basic_recall/202408251400/tmp",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CopyFile(tt.args.src, tt.args.dstDir); (err != nil) != tt.wantErr {
				t.Errorf("CopyFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCopyDir(t *testing.T) {
	type args struct {
		srcDir    string
		dstDir    string
		overwrite bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testCopyDir",
			args: args{
				srcDir:    "/tmp/dump/deal_qianchuan_brand_basic_recall/202408251400",
				dstDir:    "/tmp/dump/deal_qianchuan_brand_basic_recall/tt",
				overwrite: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CopyDir(tt.args.srcDir, tt.args.dstDir, nil, tt.args.overwrite); (err != nil) != tt.wantErr {
				t.Errorf("CopyDir() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMoveDir(t *testing.T) {
	type args struct {
		srcDirPath string
		dstDirPath string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testMoveDir",
			args: args{
				srcDirPath: "/tmp/dump/deal_qianchuan_brand_basic_recall/202408251400",
				dstDirPath: "/tmp/dump/deal_qianchuan_brand_basic_recall/2024082514001",
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := MoveDir(tt.args.srcDirPath, tt.args.dstDirPath); (err != nil) != tt.wantErr {
				t.Errorf("MoveDir() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMergeFiles(t *testing.T) {
	t.Skip("skip merge files")
	type args struct {
		dir string
		n   int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testMergeFiles",
			args: args{
				dir: "/tmp/dump/qc_deal_rec_item_for_landingpage_v2/202409120930/qc_deal_rec_item_for_landingpage_v2/202409120930",
				n:   8,
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := MergeFiles(tt.args.dir, tt.args.n); (err != nil) != tt.wantErr {
				t.Errorf("MergeFiles() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGetDirSize(t *testing.T) {
	type args struct {
		dirPath string
	}
	tests := []struct {
		name    string
		args    args
		want    int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testGetDirSize",
			args: args{
				dirPath: "/tmp/dump/qc_deal_rec_item_for_landingpage_v2/202409120930/qc_deal_rec_item_for_landingpage_v2/202409120930",
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetDirSize(tt.args.dirPath)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDirSize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetDirSize() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMoveDirByCmd(t *testing.T) {
	type args struct {
		src string
		dst string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testMoveDirByCmd",
			args: args{
				src: "/tmp/dump/qc_deal_rec_item_for_landingpage_v2/202409120930",
				dst: "/tmp/dump/qc_deal_rec_item_for_landingpage_v2/2024091209301",
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := MoveDirByCmd(tt.args.src, tt.args.dst); (err != nil) != tt.wantErr {
				t.Errorf("MoveDirByCmd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// 模拟一个会产生错误的函数
func doSomething() error {
	//return errors.WithStack(errors.New("original error"))
	return errors.New("original error")
}

// 测试堆栈信息是否会重复
func TestErrorStackDuplication(t *testing.T) {
	err := doSomething()
	if err != nil {
		//wrappedErr := errors.Wrap(err, "wrapped error")
		wrappedErr := errors.WithMessage(err, "wrapped error")

		// 使用 %+v 来打印错误，查看堆栈信息
		fmt.Printf("final err: %+v\n. errMsg: %s\n", wrappedErr, wrappedErr.Error())
	}
}

func TestInt64(t *testing.T) {
	result := float64(int64(1)+int64(2)-5+1) * 100 / float64(10)
	fmt.Println(result)
}

func Test1(t *testing.T) {
	num, err := strconv.Atoi("123888")
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(num)
}

func Test2(t *testing.T) {
	// 测试nil转string
	var str interface{}
	str = 1
	var v string
	if str == nil {
		v = ""
	} else {
		v = fmt.Sprintf("%v", str)
	}
	fmt.Println(v)
}

func Test3(t *testing.T) {
	tasks := make([]int, 0, 5)
	tasks = append(tasks, 5)
	tasks = append(tasks, 1)
	tasks = append(tasks, 4)
	tasks = append(tasks, 2)
	tasks = append(tasks, 3)
	fmt.Println(tasks)
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i] < tasks[j]
	})
	fmt.Println(tasks)
}

func Test4(t *testing.T) {
	fp := "/app/rel/data/cdb"
	dir := filepath.Dir(fp)
	fmt.Println(dir)
}

func Test5(t *testing.T) {
	versions := []string{
		"202409120930",
		"202409120931",
	}
	fmt.Printf("%s\n", versions)
}

func Test6(t *testing.T) {
	indexDbDir := "/tmp/dump/qianchuan_aup_basic_recall"
	localVersions, err := GetSubdirectories(indexDbDir)
	if err != nil {
		fmt.Println(err)
		return
	}
	var minIndexSize int64
	for _, localVersion := range localVersions {
		indexDir := filepath.Join(indexDbDir, localVersion)
		//indexSize, err := getIndexSize(indexDir, "check.json")
		indexSize, err := GetDirSize(indexDir)
		if err != nil {
			log.Warn("get index size fail: %+v", err)
			indexSize = 0
		}
		if minIndexSize == 0 || indexSize < minIndexSize {
			minIndexSize = indexSize
		}
	}
	fmt.Printf("minIndexSize: %d\n", minIndexSize)
}

func TestMergeFilesByOrder(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(dir string) error
		n       int
		wantErr bool
		verify  func(t *testing.T, dir string) bool
	}{
		{
			name: "成功合并3个文件",
			setup: func(dir string) error {
				files := []struct {
					name    string
					content string
				}{
					{"0.done", "file0\n"},
					{"1.done", "file1\n"},
					{"2.done", "file2\n"},
				}
				for _, f := range files {
					if err := os.WriteFile(filepath.Join(dir, f.name), []byte(f.content), 0644); err != nil {
						return err
					}
				}
				return nil
			},
			n:       3,
			wantErr: false,
			verify: func(t *testing.T, dir string) bool {
				// 验证0.done文件内容
				content, err := os.ReadFile(filepath.Join(dir, "0.done"))
				if err != nil {
					t.Errorf("failed to read merged file: %v", err)
					return false
				}
				expected := "file0\nfile1\nfile2\n"
				if string(content) != expected {
					t.Errorf("merged content = %q, want %q", string(content), expected)
					return false
				}
				// 验证原文件已被删除
				for i := 1; i < 3; i++ {
					if _, err := os.Stat(filepath.Join(dir, fmt.Sprintf("%d.done", i))); !os.IsNotExist(err) {
						t.Errorf("source file %d.done still exists", i)
						return false
					}
				}
				return true
			},
		},
		{
			name: "源文件不存在",
			setup: func(dir string) error {
				return os.WriteFile(filepath.Join(dir, "0.done"), []byte("file0\n"), 0644)
			},
			n:       2,
			wantErr: true,
			verify: func(t *testing.T, dir string) bool {
				// 验证临时文件被清理
				if _, err := os.Stat(filepath.Join(dir, "merged.done.tmp")); !os.IsNotExist(err) {
					t.Error("temp file should not exist")
					return false
				}
				return true
			},
		},
		{
			name: "空文件合并",
			setup: func(dir string) error {
				files := []string{"0.done", "1.done"}
				for _, f := range files {
					if _, err := os.Create(filepath.Join(dir, f)); err != nil {
						return err
					}
				}
				return nil
			},
			n:       2,
			wantErr: false,
			verify: func(t *testing.T, dir string) bool {
				// 验证合并后的文件存在且为空
				content, err := os.ReadFile(filepath.Join(dir, "0.done"))
				if err != nil {
					t.Errorf("failed to read merged file: %v", err)
					return false
				}
				if len(content) != 0 {
					t.Errorf("merged content should be empty, got %d bytes", len(content))
					return false
				}
				return true
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建临时测试目录
			dir, err := os.MkdirTemp("", "merge_test_*")
			if err != nil {
				t.Fatalf("failed to create temp dir: %v", err)
			}
			defer os.RemoveAll(dir)

			// 准备测试文件
			if err := tt.setup(dir); err != nil {
				t.Fatalf("failed to setup test files: %v", err)
			}

			// 执行测试
			err = MergeFilesByOrder(dir, tt.n)

			// 验证错误
			if (err != nil) != tt.wantErr {
				t.Errorf("MergeFilesByOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 验证结果
			if !tt.wantErr {
				if !tt.verify(t, dir) {
					t.Error("verification failed")
				}
			}
		})
	}
}