package zip

import (
	"archive/zip"
	"bytes"
	"io"
	"os"
	"path/filepath"
	"testing"
)

func TestDecompress(t *testing.T) {
	tests := []struct {
		name        string
		setupZip    func(t *testing.T) string
		destSetup   func(t *testing.T) string
		wantErr     bool
		verify      func(t *testing.T, destDir string)
		cleanupDest bool
	}{
		{
			name: "正常解压包含文件和目录的zip",
			setupZip: func(t *testing.T) string {
				// 创建临时zip文件
				tmpZip, err := os.CreateTemp("", "test_*.zip")
				if err != nil {
					t.Fatalf("创建临时zip文件失败: %v", err)
				}
				defer tmpZip.Close()

				// 创建zip写入器
				zipWriter := zip.NewWriter(tmpZip)
				defer zipWriter.Close()

				// 添加文件
				files := map[string]string{
					"file1.txt":           "file1 content",
					"dir1/file2.txt":      "file2 content",
					"dir1/dir2/file3.txt": "file3 content",
				}

				for name, content := range files {
					w, err := zipWriter.Create(name)
					if err != nil {
						t.Fatalf("创建zip内文件失败: %v", err)
					}
					if _, err := io.Copy(w, bytes.NewBufferString(content)); err != nil {
						t.Fatalf("写入zip内文件内容失败: %v", err)
					}
				}

				// 添加空目录
				dirEntry, err := zipWriter.CreateHeader(&zip.FileHeader{
					Name:   "emptydir/",
					Method: zip.Store,
				})
				if err != nil {
					t.Fatalf("创建空目录失败: %v", err)
				}
				if dirEntry == nil {
					t.Fatal("创建目录条目失败")
				}

				return tmpZip.Name()
			},
			destSetup: func(t *testing.T) string {
				// 创建临时目标目录
				destDir, err := os.MkdirTemp("", "test_dest_*")
				if err != nil {
					t.Fatalf("创建临时目标目录失败: %v", err)
				}
				return destDir
			},
			wantErr: false,
			verify: func(t *testing.T, destDir string) {
				// 验证文件内容
				files := map[string]string{
					"file1.txt":           "file1 content",
					"dir1/file2.txt":      "file2 content",
					"dir1/dir2/file3.txt": "file3 content",
				}

				for name, expectedContent := range files {
					path := filepath.Join(destDir, name)
					content, err := os.ReadFile(path)
					if err != nil {
						t.Errorf("读取解压后的文件失败 %s: %v", name, err)
						continue
					}
					if string(content) != expectedContent {
						t.Errorf("文件内容不匹配 %s: got=%s, want=%s", name, string(content), expectedContent)
					}
				}

				// 验证空目录是否创建
				emptyDirPath := filepath.Join(destDir, "emptydir")
				if info, err := os.Stat(emptyDirPath); err != nil || !info.IsDir() {
					t.Errorf("空目录未被正确创建: %v", err)
				}
			},
			cleanupDest: true,
		},
		{
			name: "源zip文件不存在",
			setupZip: func(t *testing.T) string {
				return "/non/existent/file.zip"
			},
			destSetup: func(t *testing.T) string {
				// 创建临时目标目录
				destDir, err := os.MkdirTemp("", "test_dest_*")
				if err != nil {
					t.Fatalf("创建临时目标目录失败: %v", err)
				}
				return destDir
			},
			wantErr:     true,
			cleanupDest: true,
		},
		{
			name: "目标目录无写入权限",
			setupZip: func(t *testing.T) string {
				// 创建临时zip文件
				tmpZip, err := os.CreateTemp("", "test_*.zip")
				if err != nil {
					t.Fatalf("创建临时zip文件失败: %v", err)
				}
				defer tmpZip.Close()

				// 创建zip写入器
				zipWriter := zip.NewWriter(tmpZip)
				defer zipWriter.Close()

				// 添加文件
				w, err := zipWriter.Create("file1.txt")
				if err != nil {
					t.Fatalf("创建zip内文件失败: %v", err)
				}
				if _, err := io.Copy(w, bytes.NewBufferString("content")); err != nil {
					t.Fatalf("写入zip内文件内容失败: %v", err)
				}

				return tmpZip.Name()
			},
			destSetup: func(t *testing.T) string {
				// 在Linux/MacOS上创建一个只读目录
				// 注意：在Windows上可能需要不同的权限设置
				destDir, err := os.MkdirTemp("", "test_dest_*")
				if err != nil {
					t.Fatalf("创建临时目标目录失败: %v", err)
				}

				// 创建一个子目录，然后将其设为只读
				readOnlyDir := filepath.Join(destDir, "readonly")
				if err := os.Mkdir(readOnlyDir, 0500); err != nil {
					t.Fatalf("创建只读目录失败: %v", err)
				}

				return readOnlyDir
			},
			wantErr:     true, // 在某些系统上可能不会报错，因为MkdirAll可能不会因为权限问题失败
			cleanupDest: true,
		},
		{
			name: "解压包含特殊字符的文件名",
			setupZip: func(t *testing.T) string {
				// 创建临时zip文件
				tmpZip, err := os.CreateTemp("", "test_*.zip")
				if err != nil {
					t.Fatalf("创建临时zip文件失败: %v", err)
				}
				defer tmpZip.Close()

				// 创建zip写入器
				zipWriter := zip.NewWriter(tmpZip)
				defer zipWriter.Close()

				// 添加带特殊字符的文件
				specialFiles := map[string]string{
					"特殊文件名.txt":              "special filename content",
					"file with spaces.txt":   "spaces content",
					"dir-with-dash/file.txt": "dash content",
				}

				for name, content := range specialFiles {
					w, err := zipWriter.Create(name)
					if err != nil {
						t.Fatalf("创建zip内文件失败: %v", err)
					}
					if _, err := io.Copy(w, bytes.NewBufferString(content)); err != nil {
						t.Fatalf("写入zip内文件内容失败: %v", err)
					}
				}

				return tmpZip.Name()
			},
			destSetup: func(t *testing.T) string {
				// 创建临时目标目录
				destDir, err := os.MkdirTemp("", "test_dest_*")
				if err != nil {
					t.Fatalf("创建临时目标目录失败: %v", err)
				}
				return destDir
			},
			wantErr: false,
			verify: func(t *testing.T, destDir string) {
				// 验证特殊文件名的文件内容
				specialFiles := map[string]string{
					"特殊文件名.txt":              "special filename content",
					"file with spaces.txt":   "spaces content",
					"dir-with-dash/file.txt": "dash content",
				}

				for name, expectedContent := range specialFiles {
					path := filepath.Join(destDir, name)
					content, err := os.ReadFile(path)
					if err != nil {
						t.Errorf("读取解压后的文件失败 %s: %v", name, err)
						continue
					}
					if string(content) != expectedContent {
						t.Errorf("文件内容不匹配 %s: got=%s, want=%s", name, string(content), expectedContent)
					}
				}
			},
			cleanupDest: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备源zip文件
			srcZip := tt.setupZip(t)
			if filepath.Ext(srcZip) == ".zip" { // 只清理临时创建的文件
				defer os.Remove(srcZip)
			}

			// 准备目标目录
			destDir := tt.destSetup(t)
			if tt.cleanupDest {
				defer os.RemoveAll(destDir)
			}

			// 执行解压
			err := Decompress(srcZip, destDir)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("Decompress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果没有错误且有验证函数，执行验证
			if err == nil && tt.verify != nil {
				tt.verify(t, destDir)
			}
		})
	}
}

func TestCompress(t *testing.T) {
	tests := []struct {
		name      string
		setupSrc  func(t *testing.T) string
		setupDest func(t *testing.T) string
		wantErr   bool
		verify    func(t *testing.T, zipPath string)
	}{
		{
			name: "压缩单个文件",
			setupSrc: func(t *testing.T) string {
				// 创建临时目录
				tmpDir, err := os.MkdirTemp("", "compress_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				// 创建测试文件
				filePath := filepath.Join(tmpDir, "test.txt")
				if err := os.WriteFile(filePath, []byte("test content"), 0644); err != nil {
					t.Fatalf("创建测试文件失败: %v", err)
				}

				return tmpDir
			},
			setupDest: func(t *testing.T) string {
				tmpDir, err := os.MkdirTemp("", "compress_dest_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}
				return filepath.Join(tmpDir, "output.zip")
			},
			wantErr: false,
			verify: func(t *testing.T, zipPath string) {
				// 打开并验证zip文件
				reader, err := zip.OpenReader(zipPath)
				if err != nil {
					t.Fatalf("打开zip文件失败: %v", err)
				}
				defer reader.Close()

				// 应该只有一个文件
				if len(reader.File) != 1 {
					t.Errorf("zip文件中的文件数量不正确: got=%d, want=1", len(reader.File))
				}

				// 验证文件名和内容
				zf := reader.File[0]
				if zf.Name != "test.txt" {
					t.Errorf("文件名不正确: got=%s, want=test.txt", zf.Name)
				}

				// 验证文件内容
				rc, err := zf.Open()
				if err != nil {
					t.Fatalf("打开zip内文件失败: %v", err)
				}
				defer rc.Close()

				content, err := io.ReadAll(rc)
				if err != nil {
					t.Fatalf("读取zip内文件内容失败: %v", err)
				}

				if string(content) != "test content" {
					t.Errorf("文件内容不正确: got=%s, want=test content", string(content))
				}
			},
		},
		{
			name: "压缩目录及子目录",
			setupSrc: func(t *testing.T) string {
				// 创建临时目录结构
				tmpDir, err := os.MkdirTemp("", "compress_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				// 创建文件结构
				files := map[string]string{
					"file1.txt":           "content1",
					"dir1/file2.txt":      "content2",
					"dir1/dir2/file3.txt": "content3",
				}

				for path, content := range files {
					fullPath := filepath.Join(tmpDir, path)
					// 确保目录存在
					if err := os.MkdirAll(filepath.Dir(fullPath), 0755); err != nil {
						t.Fatalf("创建目录失败: %v", err)
					}
					// 写入文件内容
					if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
						t.Fatalf("创建文件失败: %v", err)
					}
				}

				// 创建空目录
				emptyDir := filepath.Join(tmpDir, "emptydir")
				if err := os.MkdirAll(emptyDir, 0755); err != nil {
					t.Fatalf("创建空目录失败: %v", err)
				}

				return tmpDir
			},
			setupDest: func(t *testing.T) string {
				tmpDir, err := os.MkdirTemp("", "compress_dest_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}
				return filepath.Join(tmpDir, "output.zip")
			},
			wantErr: false,
			verify: func(t *testing.T, zipPath string) {
				// 打开并验证zip文件
				reader, err := zip.OpenReader(zipPath)
				if err != nil {
					t.Fatalf("打开zip文件失败: %v", err)
				}
				defer reader.Close()

				// 验证文件和目录数量
				// 应该有: file1.txt, dir1/, dir1/file2.txt, dir1/dir2/, dir1/dir2/file3.txt, emptydir/
				expectedEntries := map[string]string{
					"file1.txt":           "content1",
					"dir1/":               "",
					"dir1/file2.txt":      "content2",
					"dir1/dir2/":          "",
					"dir1/dir2/file3.txt": "content3",
					"emptydir/":           "",
				}

				if len(reader.File) != len(expectedEntries) {
					t.Errorf("zip文件中的条目数量不正确: got=%d, want=%d", len(reader.File), len(expectedEntries))
				}

				// 验证每个条目
				for _, zf := range reader.File {
					expectedContent, exists := expectedEntries[zf.Name]
					if !exists {
						t.Errorf("找到意外的条目: %s", zf.Name)
						continue
					}

					// 如果是目录，跳过内容验证
					if zf.FileInfo().IsDir() {
						continue
					}

					// 验证文件内容
					rc, err := zf.Open()
					if err != nil {
						t.Fatalf("打开zip内文件失败: %v", err)
					}

					content, err := io.ReadAll(rc)
					rc.Close()
					if err != nil {
						t.Fatalf("读取zip内文件内容失败: %v", err)
					}

					if string(content) != expectedContent {
						t.Errorf("文件内容不正确 %s: got=%s, want=%s", zf.Name, string(content), expectedContent)
					}
				}
			},
		},
		{
			name: "源路径不存在",
			setupSrc: func(t *testing.T) string {
				return "/non/existent/path"
			},
			setupDest: func(t *testing.T) string {
				tmpDir, err := os.MkdirTemp("", "compress_dest_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}
				return filepath.Join(tmpDir, "output.zip")
			},
			wantErr: true,
			verify:  nil,
		},
		{
			name: "目标目录不存在",
			setupSrc: func(t *testing.T) string {
				// 创建临时文件
				tmpFile, err := os.CreateTemp("", "compress_src_*.txt")
				if err != nil {
					t.Fatalf("创建临时文件失败: %v", err)
				}
				defer tmpFile.Close()

				if _, err := tmpFile.WriteString("test content"); err != nil {
					t.Fatalf("写入临时文件失败: %v", err)
				}

				return tmpFile.Name()
			},
			setupDest: func(t *testing.T) string {
				return "/non/existent/dir/output.zip"
			},
			wantErr: true,
			verify:  nil,
		},
		{
			name: "压缩包含特殊字符的文件名",
			setupSrc: func(t *testing.T) string {
				// 创建临时目录
				tmpDir, err := os.MkdirTemp("", "compress_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				// 创建带特殊字符的文件
				files := map[string]string{
					"特殊文件名.txt":            "special content",
					"file with spaces.txt": "spaces content",
				}

				for name, content := range files {
					path := filepath.Join(tmpDir, name)
					if err := os.WriteFile(path, []byte(content), 0644); err != nil {
						t.Fatalf("创建文件失败: %v", err)
					}
				}

				return tmpDir
			},
			setupDest: func(t *testing.T) string {
				tmpDir, err := os.MkdirTemp("", "compress_dest_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}
				return filepath.Join(tmpDir, "output.zip")
			},
			wantErr: false,
			verify: func(t *testing.T, zipPath string) {
				// 打开并验证zip文件
				reader, err := zip.OpenReader(zipPath)
				if err != nil {
					t.Fatalf("打开zip文件失败: %v", err)
				}
				defer reader.Close()

				// 验证特殊文件名
				expectedFiles := map[string]string{
					"特殊文件名.txt":            "special content",
					"file with spaces.txt": "spaces content",
				}

				for _, zf := range reader.File {
					if zf.FileInfo().IsDir() {
						continue
					}

					expectedContent, exists := expectedFiles[zf.Name]
					if !exists {
						continue // 可能有其他文件
					}

					rc, err := zf.Open()
					if err != nil {
						t.Fatalf("打开zip内文件失败: %v", err)
					}

					content, err := io.ReadAll(rc)
					rc.Close()
					if err != nil {
						t.Fatalf("读取zip内文件内容失败: %v", err)
					}

					if string(content) != expectedContent {
						t.Errorf("文件内容不正确 %s: got=%s, want=%s", zf.Name, string(content), expectedContent)
					}
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备源路径
			srcPath := tt.setupSrc(t)
			if filepath.Base(srcPath) != "existent" { // 跳过不存在的路径清理
				defer func() {
					// 如果是目录，删除整个目录
					if info, err := os.Stat(srcPath); err == nil && info.IsDir() {
						os.RemoveAll(srcPath)
					} else {
						// 如果是文件，只删除文件
						os.Remove(srcPath)
					}
				}()
			}

			// 准备目标路径
			destPath := tt.setupDest(t)
			if filepath.Dir(destPath) != "/non" { // 跳过不存在的路径清理
				defer func() {
					os.RemoveAll(filepath.Dir(destPath))
				}()
			}

			// 执行压缩
			err := Compress(srcPath, destPath)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("Compress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果没有错误且有验证函数，执行验证
			if err == nil && tt.verify != nil {
				tt.verify(t, destPath)
			}
		})
	}
}

func TestDecompressLocal(t *testing.T) {
	t.Skip("本地打包测试, 忽略")
	zipFile := "/Users/<USER>/project/dw/dip-agent/agent.zip"
	destDir := "/Users/<USER>/project/dw/dip-agent/"
	entries, err := os.ReadDir(destDir)
	if err != nil {
		t.Errorf("读取目录失败: %v", err)
	}
	for _, entry := range entries {
		t.Logf("entry: %s", entry.Name())
	}

	err = Decompress(zipFile, destDir)
	if err != nil {
		t.Errorf("解压失败: %v", err)
	}
}
