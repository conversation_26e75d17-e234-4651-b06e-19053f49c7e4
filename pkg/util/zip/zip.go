package zip

import (
	"archive/zip"
	"github.com/pkg/errors"
	"io"
	"os"
	"path/filepath"
)

// Compress 压缩文件
// src: 源目录
// dest: 目标zip文件
func Compress(src string, dest string) error {
	// 创建目标zip文件
	zipfile, err := os.Create(dest)
	if err != nil {
		return errors.WithMessagef(err, "create zip file fail: %s", dest)
	}
	defer zipfile.Close()

	// 创建zip写入器
	archive := zip.NewWriter(zipfile)
	defer archive.Close()

	// 遍历源文件/目录
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过源目录
		if path == src {
			return nil
		}

		// 获取相对路径作为zip内路径
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}

		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}
		header.Name = relPath

		if info.IsDir() {
			header.Name += "/"
		} else {
			header.Method = zip.Deflate
		}

		// 创建压缩文件头
		writer, err := archive.CreateHeader(header)
		if err != nil {
			return err
		}

		// 如果是文件则写入内容
		if !info.IsDir() {
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()
			_, err = io.Copy(writer, file)
		}
		return err
	})
}

// Decompress 解压文件
// src: 源zip文件
// dest: 目标目录
func Decompress(src string, dest string) error {
	// 打开zip文件
	reader, err := zip.OpenReader(src)
	if err != nil {
		return errors.WithMessagef(err, "open zip file fail: %s", src)
	}
	defer reader.Close()

	// 创建目标目录
	if err := os.MkdirAll(dest, 0755); err != nil {
		return err
	}

	// 遍历zip文件中的所有文件
	for _, file := range reader.File {
		// 构建目标文件路径
		path := filepath.Join(dest, file.Name)

		// 如果是目录，创建目录
		if file.FileInfo().IsDir() {
			err := os.MkdirAll(path, file.Mode())
			if err != nil {
				return err
			}
			continue
		}

		// 创建目标文件所在的目录
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			return err
		}

		// 创建目标文件
		dstFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			return err
		}

		// 打开zip中的源文件
		srcFile, err := file.Open()
		if err != nil {
			dstFile.Close()
			return err
		}

		// 复制文件内容
		_, err = io.Copy(dstFile, srcFile)
		srcFile.Close()
		dstFile.Close()

		if err != nil {
			return err
		}
	}

	return nil
}
