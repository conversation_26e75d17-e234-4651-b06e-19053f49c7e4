package util

import (
	"bytes"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/pkg/errors"
)

// IsPathExist 判断指定的文件/目录是否存在
func IsPathExist(path string) bool {
	_, err := os.Stat(path)
	return err == nil || os.IsExist(err)
}

// RemoveIfExists 删除指定的文件或目录，如果不存在则忽略
func RemoveIfExists(path string) error {
	if IsPathExist(path) {
		return os.RemoveAll(path)
	}
	return nil
}

// ReadFileTo 读取文件内容, 并使用json反序列化为指定的结构
func ReadFileTo(filePath string, v interface{}) error {
	file, err := os.Open(filePath)
	if err != nil {
		return errors.Wrapf(err, "failed to open file: %s", filePath)
	}
	defer file.Close()
	err = json.NewDecoder(file).Decode(v)
	if err != nil {
		return errors.Wrapf(err, "failed to decode json file: %s", filePath)
	}
	return nil
}

// WriteJsonToFile 将指定的泛型结构序列化为json, 并写入到指定的文件中
func WriteJsonToFile(filePath string, data interface{}, pretty bool) error {
	var (
		err      error
		jsonData []byte
	)
	// json序列化
	if pretty {
		jsonData, err = json.MarshalIndentDefault(data)
	} else {
		jsonData, err = json.Marshal(data)
	}
	if err != nil {
		return errors.Wrapf(err, "error marshaling data: %+v", data)
	}

	return WriteRawToFile(filePath, jsonData)
}

func WriteRawToFile(filePath string, data []byte) error {
	// 确保目录存在
	if err := EnsureDir(filePath); err != nil {
		return err
	}

	// 写入文件
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return errors.Wrapf(err, "error writing JSON to file: %s", filePath)
	}

	return nil
}

// CreateDir 创建指定的目录
// removeIfExist: 如果目录已存在，是否删除已存在的目录
func CreateDir(dirPath string, removeIfExist bool) error {
	// 检查目录是否存在
	if _, err := os.Stat(dirPath); err == nil {
		if removeIfExist {
			// 如果选择删除已存在的目录
			if err := os.RemoveAll(dirPath); err != nil {
				return errors.Wrapf(err, "failed to remove existing directory: %s", dirPath)
			}
			// 创建目录
			if err := os.MkdirAll(dirPath, 0755); err != nil {
				return errors.Wrapf(err, "failed to create directory after removal: %s", dirPath)
			}
		} else {
			return nil
		}
	} else if os.IsNotExist(err) {
		// 目录不存在，直接创建
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			return errors.Wrapf(err, "failed to create directory: %s", dirPath)
		}
		log.Info("create dir: %s", dirPath)
	} else {
		// 如果检查目录存在时出现其他错误
		return errors.Wrapf(err, "error checking if directory exists: %s", dirPath)
	}

	return nil
}

// EnsureDir 确保文件的目录存在
// filePath: 文件路径, 如果是目录需要以/结尾
func EnsureDir(filePath string) error {
	dir := filepath.Dir(filePath)

	// Check if directory exists
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		// Create directory if it does not exist
		if err = os.MkdirAll(dir, 0755); err != nil {
			return errors.Wrapf(err, "failed to create dir: %s", dir)
		}
	}
	return nil
}

// RemoveAllExcept 删除指定目录中的所有文件和子目录，除了指定的排除项
// dirPath: 要清理的目录路径
// excludes: 排除的文件或目录名称列表(相对路径, 相对dirPath)
func RemoveAllExcept(dirPath string, excludes []string) error {
	// 如果目录不存在, 直接返回
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return nil
	}
	// 遍历目录
	return filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err // 返回遍历中的错误
		}

		// 检查是否为根目录，跳过根目录自身
		if path == dirPath {
			return nil
		}

		// 获取相对路径，用于检查是否排除
		relativePath, err := filepath.Rel(dirPath, path)
		if err != nil {
			return errors.Wrapf(err, "failed to get relative path: %s", path)
		}

		// 检查是否在排除列表中
		if ContainsString(excludes, relativePath) {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// 删除文件或目录
		if info.IsDir() {
			// 是目录且不在排除列表中，尝试递归删除
			err = os.RemoveAll(path)
			if err != nil {
				return errors.Wrapf(err, "failed to remove dir: %s", path)
			}
			return filepath.SkipDir // 跳过这个目录及其内部
		} else {
			// 是文件，直接删除
			err = os.Remove(path)
			if err != nil {
				return errors.Wrapf(err, "failed to remove file: %s", path)
			}
			return nil
		}
	})
}

// CreateFile 创建一个文件，如果所在目录不存在，则先创建目录。
func CreateFile(path string) error {
	file, err := CreateFileAndReturn(path)
	if err != nil {
		return err
	}
	file.Close()
	return nil
}

func CreateFileAndReturn(path string) (*os.File, error) {
	// 确保目录存在
	err := EnsureDir(path)
	if err != nil {
		return nil, err
	}

	// 创建文件
	file, err := os.Create(path)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create file: %s", path)
	}
	return file, nil
}

// CreateDirs 创建多个目录，如果目录已存在则跳过
// removeIfExist: 如果目录已存在，是否删除已存在的目录
func CreateDirs(dirs []string, removeIfExist bool) error {
	for _, dir := range dirs {
		if dir == "" {
			continue
		}

		// 创建目录
		if err := CreateDir(dir, removeIfExist); err != nil {
			return errors.WithMessagef(err, "failed to create dir: %s", dir)
		}
	}
	return nil
}

// CreatePaths 批量创建目录或文件。
// paths: 路径列表，目录应以/结尾, 否则认为是文件。
func CreatePaths(paths []string) error {
	for _, path := range paths {
		if path == "" {
			continue
		}

		// 检查路径是否已经存在
		if _, err := os.Stat(path); err == nil {
			// 文件或目录已存在
			log.Info("Path already exists: %s", path)
			continue
		} else if !os.IsNotExist(err) {
			// 发生了其他错误
			return errors.Wrapf(err, "failed to check path: %s", path)
		}

		if strings.HasSuffix(path, "/") {
			// 创建目录
			if err := EnsureDir(path); err != nil {
				return err
			}
		} else {
			// 创建文件
			if err := CreateFile(path); err != nil {
				return errors.WithMessagef(err, "failed to create file %s", path)
			}
		}
	}
	return nil
}

// CopyFilesToDir 复制文件列表到指定目录
func CopyFilesToDir(files []string, destDir string) error {
	// 确保目标目录存在
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return errors.Wrapf(err, "failed to create dest dir: %s", destDir)
	}

	for _, file := range files {
		// 检查源文件是否存在
		if !IsPathExist(file) {
			log.Warn("file not exist: %s", file)
			continue
		}

		// 计算目标文件路径
		destFilePath := filepath.Join(destDir, filepath.Base(file))

		// 执行复制操作
		if err := copyFile(file, destFilePath); err != nil {
			return errors.WithMessagef(err, "failed to copy '%s' to '%s'", file, destFilePath)
		}
	}
	return nil
}

// CopyFile copies a file from src to dst. If dst exists, it will be overwritten.
func CopyFile(src, dstDir string) error {
	// Ensure the destination directory exists
	if err := os.MkdirAll(dstDir, os.ModePerm); err != nil {
		return errors.Wrapf(err, "failed to create destination directory: %s", dstDir)
	}

	// Get the base name of the source file to use in the destination path
	dstPath := filepath.Join(dstDir, filepath.Base(src))

	if err := copyFile(src, dstPath); err != nil {
		return errors.WithMessagef(err, "failed to copy '%s' to '%s'", src, dstPath)
	}
	return nil
}

// copyFile copies a file from srcPath to destPath.
func copyFile(srcPath, destPath string) error {
	// Open the source file
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return errors.Wrapf(err, "failed to open source file: %s", srcPath)
	}
	defer srcFile.Close()

	// Open the destination file, create it if it doesn't exist, and overwrite it if it does
	dstFile, err := os.OpenFile(destPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return errors.Wrapf(err, "failed to open destination file: %s", destPath)
	}
	defer dstFile.Close()

	// Copy the content from the source file to the destination file
	if _, err := io.Copy(dstFile, srcFile); err != nil {
		return errors.Wrapf(err, "failed to copy file from %s to %s", srcPath, destPath)
	}

	// Sync the destination file to ensure the content is written to disk
	return dstFile.Sync()
}

// CopyDir copies all contents from the source directory to the destination directory.
// If the destination directory is a subdirectory of the source directory, it returns an error.
// The filter function determines which files to copy; if filter returns true, the file is copied.
// The overwrite parameter controls whether existing files in the destination should be overwritten.
func CopyDir(srcDir, dstDir string, filter func(string) bool, overwrite bool) error {
	// Before starting the copy process
	isSub, err := IsSubdirectory(srcDir, dstDir)
	if err != nil {
		return errors.WithMessage(err, "error checking dir")
	}
	if isSub {
		return errors.Errorf("cannot copy directory %s into its subdirectory %s", srcDir, dstDir)
	}

	// Ensure the destination directory exists
	if err := os.MkdirAll(dstDir, 0755); err != nil {
		return errors.Wrapf(err, "failed to create dir: %s", dstDir)
	}

	// Read all items in the source directory
	items, err := os.ReadDir(srcDir)
	if err != nil {
		return errors.Wrapf(err, "failed to read source dir: %s", srcDir)
	}

	for _, item := range items {
		name := item.Name()
		srcPath := filepath.Join(srcDir, name)
		dstPath := filepath.Join(dstDir, name)

		// Check if the file should be copied
		if filter != nil && !filter(name) {
			continue
		}

		if item.IsDir() {
			// Recursively copy subdirectories
			if err := CopyDir(srcPath, dstPath, filter, overwrite); err != nil {
				return err
			}
		} else {
			// Copy files
			if overwrite {
				if err := CopyFile(srcPath, dstDir); err != nil {
					return err
				}
			} else {
				if _, err := os.Stat(dstPath); os.IsNotExist(err) {
					if err := CopyFile(srcPath, dstDir); err != nil {
						return err
					}
				}
			}
		}
	}

	return nil
}

// IsSubdirectory Check if dstDir is a subdirectory of srcDir
func IsSubdirectory(srcDir, dstDir string) (bool, error) {
	// Get the absolute paths of the source and destination directories
	srcAbs, err := filepath.Abs(srcDir)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get absolute path for source directory: %s", srcDir)
	}

	dstAbs, err := filepath.Abs(dstDir)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get absolute path for destination directory: %s", dstDir)
	}

	// Check if dstDir starts with srcDir
	return strings.HasPrefix(dstAbs, srcAbs), nil
}

// MoveDir 将一个目录移动到另一个目录下
// srcDirPath: 源目录路径
// dstDirPath: 目标目录路径
// Deprecated: use MoveDirByCmd instead. 由于移动目录使用rename实现无法跨磁盘,因此优先使用MoveDirByCmd代替
func MoveDir(srcDirPath, dstDirPath string) error {
	// 获取源目录的绝对路径
	absSrcPath, err := filepath.Abs(srcDirPath)
	if err != nil {
		return errors.Wrapf(err, "getting absolute path for source directory fail: %s", srcDirPath)
	}

	// 确保目标目录存在
	if err = os.MkdirAll(dstDirPath, 0755); err != nil {
		return errors.Wrapf(err, "creating destination directory fail: %s", dstDirPath)
	}

	// 获取目标目录的绝对路径
	absDstPath, err := filepath.Abs(dstDirPath)
	if err != nil {
		return errors.Wrapf(err, "getting absolute path for destination directory fail: %s", dstDirPath)
	}

	// 构造目标目录中的新路径
	targetPath := filepath.Join(absDstPath, filepath.Base(absSrcPath))

	// 使用 os.Rename 移动目录
	if err = os.Rename(absSrcPath, targetPath); err != nil {
		return errors.Wrapf(err, "failed move dir from %s to %s", absSrcPath, targetPath)
	}

	return nil
}

// MoveDirByCmd 使用系统命令mv来移动目录
func MoveDirByCmd(src, dst string) error {
	// 确保目标目录存在
	if err := os.MkdirAll(dst, 0755); err != nil {
		return errors.Wrapf(err, "create dst dir fail: %s", dst)
	}
	// 构造mv命令
	cmd := exec.Command("mv", src, dst)
	log.Info("mv exec command: %s", cmd.String())
	// 将cmd执行过程中的输出记录到string返回
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	// 执行命令
	err := cmd.Run()
	// 输出命令执行结果
	if stdoutBuf.Len() > 0 {
		log.Info("mv stdout: %s", stdoutBuf.String())
	}
	if stderrBuf.Len() > 0 {
		log.Error("mv stderr: %s", stderrBuf.String())
	}
	if err != nil {
		return errors.Wrapf(err, "failed move dir from %s to %s fail", src, dst)
	}

	return nil
}

// MoveDirByRsync 使用系统命令rsync来移动目录
// note: 由于mv命令跨盘移动的时候内存占用波动较大, 修改为rsync命令
func MoveDirByRsync(src, dst string) error {
	// 确保目标目录存在
	if err := os.MkdirAll(dst, 0755); err != nil {
		return errors.Wrapf(err, "create dst dir fail: %s", dst)
	}
	// 构造rsync命令: rsync -avh --remove-source-files --progress src dst
	cmd := exec.Command("rsync", "-avh", "--remove-source-files", src, dst)
	log.Info("move exec command: %s", cmd.String())
	// 明确执行路径，以免当前路径被删除而造成执行失败
	cmd.Dir = filepath.Dir(dst)
	// 将cmd执行过程中的输出记录到string返回
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	// 执行命令
	err := cmd.Run()
	// 输出命令执行结果
	if stdoutBuf.Len() > 0 {
		log.Info("move stdout: %s", stdoutBuf.String())
	}
	if stderrBuf.Len() > 0 {
		log.Error("move stderr: %s", stderrBuf.String())
	}
	if err != nil {
		return errors.Wrapf(err, "failed move dir from %s to %s fail", src, dst)
	}

	return nil
}

// MakeFileExecutable 设置指定文件的权限，使其成为可执行文件
func MakeFileExecutable(filePath string) error {
	// 使用os.Chmod来改变文件权限
	// 在Unix系统中，0755通常代表可执行
	err := os.Chmod(filePath, 0755)
	if err != nil {
		return errors.Wrapf(err, "failed to set executable permission: %s", filePath)
	}
	return nil
}

// FileInfo holds information about each file
type FileInfo struct {
	Name   string
	Size   int64
	Offset int64
}

// MergeFilesByOrder 按顺序合并文件
func MergeFilesByOrder(dir string, n int) error {
	// 创建临时文件
	tempPath := filepath.Join(dir, "merged.done.tmp")
	tempFile, err := os.Create(tempPath)
	if err != nil {
		return errors.Wrapf(err, "failed to create temp file: %s", tempPath)
	}
	defer tempFile.Close()

	// 按顺序读取并合并文件
	for i := 0; i < n; i++ {
		fileName := fmt.Sprintf("%d.done", i)
		filePath := filepath.Join(dir, fileName)

		// 打开源文件
		srcFile, err := os.Open(filePath)
		if err != nil {
			return errors.Wrapf(err, "failed to open source file: %s", filePath)
		}

		// 复制文件内容
		_, err = io.Copy(tempFile, srcFile)
		srcFile.Close()
		if err != nil {
			return errors.Wrapf(err, "failed to copy content from %s", filePath)
		}

		// 删除源文件
		if err = os.Remove(filePath); err != nil {
			return errors.Wrapf(err, "failed to remove source file: %s", filePath)
		}
	}

	// 关闭临时文件，准备重命名
	tempFile.Close()

	// 将临时文件重命名为0.done
	targetPath := filepath.Join(dir, "0.done")
	if err := os.Rename(tempPath, targetPath); err != nil {
		return errors.Wrapf(err, "failed to rename temp file to: %s", targetPath)
	}

	return nil
}

// MergeFiles 并发读取多个文件并按顺序写入到0.done中
func MergeFiles(dir string, n int) error {
	start := time.Now()
	defer func() {
		log.Info("[%s]MergeFiles cost time: %s", dir, time.Since(start))
	}()

	var (
		err       error
		fileInfos []FileInfo
		totalSize int64
	)

	// Step 1: Pre-scan all files to get their sizes and calculate offsets
	for i := 0; i < n; i++ {
		fileName := fmt.Sprintf("%d.done", i)
		filePath := filepath.Join(dir, fileName)
		info, err := os.Stat(filePath)
		if err != nil {
			return errors.Wrapf(err, "failed to get info of file %s", filePath)
		}
		log.Info("file: %s, size: %d", fileName, info.Size())

		fileInfos = append(fileInfos, FileInfo{
			Name:   fileName,
			Size:   info.Size(),
			Offset: totalSize,
		})
		totalSize += info.Size()
	}

	log.Info("totalSize: %d", totalSize)

	// Step 2: Create a temporary output file and preallocate space
	tempPath := filepath.Join(dir, "merged.done.tmp") // 使用不同的临时文件名
	outputFile, err := os.Create(tempPath)
	if err != nil {
		return errors.Wrapf(err, "failed to create temp output file: %s", tempPath)
	}
	defer func() {
		outputFile.Close()
		os.Remove(tempPath)
	}()

	// 预分配文件大小
	if err = outputFile.Truncate(totalSize); err != nil {
		return errors.Wrapf(err, "failed to preallocate file size: %s", tempPath)
	}

	// Step 3: Concurrently read and write files
	var (
		wg       sync.WaitGroup
		asyncErr atomic.Value
	)

	for _, fi := range fileInfos {
		wg.Add(1)
		go func(fi FileInfo) {
			defer wg.Done()

			srcPath := filepath.Join(dir, fi.Name)
			srcFile, err := os.Open(srcPath)
			if err != nil {
				err = errors.Wrapf(err, "failed to open source file: %s", srcPath)
				asyncErr.Store(err)
				return
			}
			defer srcFile.Close()

			bufferSize := 1024 * 1024 // 1MB 缓冲区
			buffer := make([]byte, bufferSize)
			var offset int64 = fi.Offset

			for {
				nBytes, readErr := srcFile.Read(buffer)
				if nBytes > 0 {
					// 写入到目标文件的指定偏移量
					_, writeErr := outputFile.WriteAt(buffer[:nBytes], offset)
					if writeErr != nil {
						err = errors.Wrapf(err, "failed to write to file: %s", tempPath)
						asyncErr.Store(err)
						return
					}
					offset += int64(nBytes)
				}
				if readErr != nil {
					if readErr != io.EOF {
						err = errors.Wrapf(err, "failed to read from file: %s", srcPath)
						asyncErr.Store(err)
					}
					break
				}
			}
		}(fi)
	}

	// Step 4: Wait for all goroutines to finish
	wg.Wait()

	// Step 5: Check for errors
	if err, ok := asyncErr.Load().(error); ok {
		return err
	}

	// 删除xx.done文件
	for _, fi := range fileInfos {
		srcPath := filepath.Join(dir, fi.Name)
		if err = os.Remove(srcPath); err != nil && !os.IsNotExist(err) {
			return errors.Wrapf(err, "failed to remove file: %s", srcPath)
		}
	}

	// Step 6: Replace the original 0.done with the merged file
	zeroDonePath := filepath.Join(dir, "0.done")
	// 先将原有的0.done修改为0.done.bak
	//if err := os.Rename(zeroDonePath, zeroDonePath+".bak"); err != nil {
	//	return fmt.Errorf("failed to backup 0.done file: %v", err)
	//}
	// 将临时文件替换为0.done
	if err := os.Rename(tempPath, zeroDonePath); err != nil {
		return errors.Wrapf(err, "failed to rename source file: %s, dest file: %s", tempPath, zeroDonePath)
	}

	return nil
}

// GetSubdirectories 获取指定目录下的所有一级子目录名称
func GetSubdirectories(dir string) ([]string, error) {
	var subDirs []string
	// 先判断 dir 是否存在
	if !IsPathExist(dir) {
		return subDirs, nil
	}

	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to read dir: %s", dir)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			subDirs = append(subDirs, entry.Name())
		}
	}
	return subDirs, nil
}

// IsEmptyDir 判断目录内是否为空
func IsEmptyDir(dir string) (bool, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return false, errors.Wrapf(err, "failed to read dir: %s", dir)
	}
	return len(entries) == 0, nil
}

// GetFileCount 获取目录内的文件数量
func GetFileCount(dir string) (int, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to read dir: %s", dir)
	}
	return len(entries), nil
}

// GetDirSize 获取指定目录的大小
func GetDirSize(dirPath string) (int64, error) {
	return GetDirSizeWith(dirPath, nil)
}

// GetDirSizeWith 获取指定目录的大小, 并且可以指定过滤器
func GetDirSizeWith(dirPath string, filter func(string) bool) (int64, error) {
	var size int64
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if filter == nil || filter(path) {
			size += info.Size()
		}
		return nil
	})
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get dir size: %s", dirPath)
	}
	return size, nil
}

func FileSize(filePath string) int64 {
	fileInfo, err := os.Stat(filePath)
	if err != nil || fileInfo.IsDir() {
		return 0
	}
	return fileInfo.Size()
}

// CheckDirRW 校验指定的目录是否可以读写. 因为容器环境，可能挂载的磁盘有问题
func CheckDirRW(dirPath string) error {
	err := checkDirRW(dirPath)
	if err != nil {
		return errors.WithMessagef(err, "check dir read/write fail: %s", dirPath)
	}
	return nil
}

func checkDirRW(dirPath string) error {
	// 检查目录是否存在
	if _, err := os.Stat(dirPath); err != nil {
		return errors.Wrapf(err, "directory does not exist or cannot access: %s", dirPath)
	}

	// 尝试创建临时文件测试写权限
	tempFile := filepath.Join(dirPath, ".tmp_check_rw")
	if err := os.WriteFile(tempFile, []byte("test"), 0644); err != nil {
		return errors.Wrapf(err, "failed to write test file in directory: %s", dirPath)
	}
	defer os.Remove(tempFile)

	// 尝试读取临时文件测试读权限
	if _, err := os.ReadFile(tempFile); err != nil {
		return errors.Wrapf(err, "failed to read test file in directory: %s", dirPath)
	}

	// 尝试创建和删除临时目录测试写权限
	tempDir := filepath.Join(dirPath, ".tmp_check_rw_dir")
	if err := os.Mkdir(tempDir, 0755); err != nil {
		return errors.Wrapf(err, "failed to create test directory in: %s", dirPath)
	}
	if err := os.Remove(tempDir); err != nil {
		return errors.Wrapf(err, "failed to remove test directory in: %s", dirPath)
	}

	return nil
}
