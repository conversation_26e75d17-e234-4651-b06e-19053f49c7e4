package util

import (
	"bufio"
	"bytes"
	"dip-agent/pkg/core/log"
	"github.com/pkg/errors"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"syscall"
)

// ExecuteScript 执行指定的脚本文件，并捕获标准输出和标准错误输出
func ExecuteScript(clue, shellPath string, cmdArgs []string) (execCmd string, err error) {
	// 构建执行命令
	cmd := exec.Command(shellPath, cmdArgs...)
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true, // 确保可以为命令创建新的进程组
	}
	execCmd = cmd.String()
	log.Info("%s exec command: %s", clue, execCmd)
	log.Info("%s current process pid: %d", clue, os.Getpid())

	// 创建缓冲区捕获输出
	// var stdoutBuf, stderrBuf bytes.Buffer
	// cmd.Stdout = &stdoutBuf
	// cmd.Stderr = &stderrBuf

	// 确保继承所有必要的环境变量
	cmd.Env = os.Environ()

	// 创建管道捕获输出
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return execCmd, errors.Wrap(err, "failed to create stdout pipe")
	}
	defer stdoutPipe.Close()
	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		return execCmd, errors.Wrap(err, "failed to create stderr pipe")
	}
	defer stderrPipe.Close()

	// 异步读取标准输出并打印
	go copyOutputToLog(stdoutPipe, "STDOUT", clue)
	// 异步读取标准错误并打印
	go copyOutputToLog(stderrPipe, "STDERR", clue)

	// 执行命令
	if err = cmd.Run(); err != nil {
		return execCmd, errors.Wrapf(err, "failed to execute script: %s", execCmd)
	}
	return execCmd, nil
}

func ExecuteScriptToOs(clue, shellPath string, cmdArgs []string) (execCmd string, err error) {
	// 构建执行命令
	cmd := exec.Command(shellPath, cmdArgs...)
	execCmd = cmd.String()
	log.Info("%s exec command: %s", clue, execCmd)

	// 重定向标准输出和标准错误到os.Stdout和os.Stderr
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 确保继承所有必要的环境变量
	cmd.Env = os.Environ()

	// 执行命令
	if err = cmd.Run(); err != nil {
		return execCmd, errors.Wrapf(err, "failed to execute script: %s", execCmd)
	}
	return execCmd, nil
}

// ExecuteScriptToBuf 执行指定的脚本文件，并捕获标准输出和标准错误输出到缓冲区
func ExecuteScriptToBuf(clue, shellPath string, cmdArgs []string) (execCmd string, err error) {
	// 构建执行命令
	cmd := exec.Command(shellPath, cmdArgs...)
	execCmd = cmd.String()
	log.Info("%s exec command: %s", clue, execCmd)

	// 创建缓冲区捕获输出
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf

	// 执行命令
	if err = cmd.Run(); err != nil {
		// 获取 stderr
		if stderrBuf.Len() > 0 {
			stderr := stderrBuf.String()
			return execCmd, errors.Wrapf(err, "failed to execute script: %s, stderr: %s", execCmd, stderr)
		}
		return execCmd, errors.Wrapf(err, "failed to execute script: %s", execCmd)
	}
	log.Info("%s exec command result - stdout: %s, stderr: %s", clue, stdoutBuf.String(), stderrBuf.String())
	return execCmd, nil
}

// ExecuteScriptAsync 异步执行指定的脚本文件，并捕获标准输出和标准错误输出
// Deprecated: 请使用ExecuteScriptToFileAsync. 可能导致执行进程挂掉
func ExecuteScriptAsync(clue, shellPath string, cmdArgs []string) (execCmd string, err error) {
	// 构建执行命令
	cmd := exec.Command(shellPath, cmdArgs...)
	execCmd = cmd.String()
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true, // 确保可以为命令创建新的进程组
	}
	log.Info("%s exec command: %s", clue, execCmd)

	// 创建缓冲区捕获输出
	// var stdoutBuf, stderrBuf bytes.Buffer
	// cmd.Stdout = &stdoutBuf
	// cmd.Stderr = &stderrBuf

	// 创建管道捕获输出
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return execCmd, errors.Wrap(err, "failed to create stdout pipe")
	}
	defer stdoutPipe.Close()
	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		return execCmd, errors.Wrap(err, "failed to create stderr pipe")
	}
	defer stderrPipe.Close()

	// 异步读取标准输出并打印
	go copyOutputToLog(stdoutPipe, "STDOUT", clue)
	// 异步读取标准错误并打印
	go copyOutputToLog(stderrPipe, "STDERR", clue)

	go func() {
		// 开始执行命令，而不是运行
		if err := cmd.Start(); err != nil {
			log.Error("failed to start script: %v", err)
		}

		// 获取并打印进程ID
		log.Info("%s PID of the running command: %d", clue, cmd.Process.Pid)

		// 等待命令执行完成
		// TODO 增加超时机制
		if err := cmd.Wait(); err != nil {
			log.Error("failed to execute script: %v", err)
		}
	}()

	// 执行命令 cmd.Run() 会等待命令执行完成，然后返回错误信息
	//err = cmd.Run()
	//if err == nil {
	//	return stdoutBuf.String(), stderrBuf.String(), nil
	//}
	return execCmd, nil
}

// copyOutputToLog 从给定的Reader中读取内容，并通过log.Info打印输出
func copyOutputToLog(r io.Reader, tag, clue string) {
	scanner := bufio.NewScanner(r)
	for scanner.Scan() {
		log.Info("%s,%s: %s", clue, tag, scanner.Text()) // 使用log.Info打印每行输出
	}
	if err := scanner.Err(); err != nil && err != io.EOF {
		log.Error("%s,%s error reading data from process: %+v", clue, tag, err)
	}
}

// ExecuteScriptToFile 执行指定的脚本文件，并将输出和错误输出都重定向到同一个指定的文件
func ExecuteScriptToFile(clue, shellPath string, cmdArgs []string, outputPath string) (string, error) {
	// 确保输出文件的目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return "", errors.Wrapf(err, "failed to create output dir: %s", outputDir)
	}

	// 输出文件是否存在
	var (
		outputFile *os.File
		err        error
	)
	if IsPathExist(outputPath) {
		outputFile, err = os.OpenFile(outputPath, os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return "", errors.Wrapf(err, "failed to open output file: %s", outputPath)
		}
	} else {
		log.Info("output file not exist, create new file: %s", outputPath)
		outputFile, err = os.Create(outputPath)
		if err != nil {
			return "", errors.Wrapf(err, "failed to create output file: %s", outputPath)
		}
	}
	defer outputFile.Close()

	// 设置命令和重定向输出
	cmd := exec.Command(shellPath, cmdArgs...)
	execCmd := cmd.String()
	log.Info("%s exec command: %s", clue, execCmd)
	cmd.Stdout = outputFile // 标准输出重定向到文件
	cmd.Stderr = outputFile // 标准错误也重定向到同一个文件

	// 执行命令
	if err = cmd.Run(); err != nil {
		return execCmd, errors.Wrapf(err, "failed to execute script: %s", cmd.String())
	}

	return execCmd, nil
}

// ExecuteScriptToFileAsync 异步执行指定的脚本文件，并将输出和错误输出都重定向到同一个指定的文件
func ExecuteScriptToFileAsync(clue, shellPath string, cmdArgs []string, outputPath string) (string, error) {
	// 确保输出文件的目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return "", errors.Wrapf(err, "failed to create output dir: %s", outputDir)
	}

	// 输出文件是否存在
	var (
		outputFile *os.File
		err        error
	)
	if IsPathExist(outputPath) {
		outputFile, err = os.OpenFile(outputPath, os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return "", errors.Wrapf(err, "failed to open output file: %s", outputPath)
		}
	} else {
		outputFile, err = os.Create(outputPath)
		if err != nil {
			return "", errors.Wrapf(err, "failed to create output file: %s", outputPath)
		}
	}
	defer outputFile.Close()

	// 设置命令和重定向输出
	cmd := exec.Command(shellPath, cmdArgs...)
	execCmd := cmd.String()
	log.Info("%s exec command: %s", clue, execCmd)
	cmd.Stdout = outputFile // 标准输出重定向到文件
	cmd.Stderr = outputFile // 标准错误也重定向到同一个文件

	go func() {
		// 开始执行命令，而不是运行
		if err := cmd.Start(); err != nil {
			log.Error("failed to start script: %v", err)
		}

		// 获取并打印进程ID
		log.Info("%s PID of the running command: %d", clue, cmd.Process.Pid)

		// 等待命令执行完成
		// TODO 增加超时机制
		if err := cmd.Wait(); err != nil {
			log.Error("failed to execute script: %v", err)
		}
	}()

	return execCmd, nil
}
