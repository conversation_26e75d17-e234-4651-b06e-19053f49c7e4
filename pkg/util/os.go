package util

import (
	"bytes"
	"dip-agent/pkg/core/log"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"syscall"

	"github.com/pkg/errors"
	"github.com/shirou/gopsutil/mem"
)

// CheckDiskSpace 检查指定目录对应的磁盘是否有足够空间存储指定大小的文件
// dirPath 待检查的目录路径
// fileSize 文件大小，单位为字节
func CheckDiskSpace(dirPath string, fileSize int64) (bool, error) {
	// 获取文件系统的状态
	var stat syscall.Statfs_t
	if err := syscall.Statfs(dirPath, &stat); err != nil {
		return false, errors.Wrap(err, "error get disk info")
	}

	// 计算可用空间
	availableBytes := stat.Bavail * uint64(stat.Bsize)

	// 比较可用空间与文件大小
	return availableBytes >= uint64(fileSize), nil
}

// DiskUsage 获取指定目录的磁盘使用情况: 磁盘总大小\已经使用的空间
func DiskUsage(dirPath string) (total, used uint64, err error) {
	var stat syscall.Statfs_t
	if err = syscall.Statfs(dirPath, &stat); err != nil {
		return 0, 0, errors.Wrap(err, "error get disk info")
	}
	total = stat.Blocks * uint64(stat.Bsize)
	// Bfree是针对超级用户的剩余空间，Bavail是针对普通用户的剩余空间
	//free = stat.Bfree * uint64(stat.Bsize)
	free := stat.Bavail * uint64(stat.Bsize)
	// 已经使用的空间
	used = total - free
	return
}

// MemoryUsage 获取当前运行环境的内存限制和当前内存使用量. 优先获取cgroup中的内存限制与内存使用, 获取失败则获取系统内存限制与使用
func MemoryUsage() (limit uint64, usage uint64, err error) {
	// cgroup获取的usage偏高, 使用vmstat获取的内存使用量
	limit, usage, err = CgroupMemoryDetails()
	if err != nil {
		return 0, 0, errors.Wrap(err, "failed to get cgroup memory details")
	}
	return limit, usage, nil
}

// VirtualMemoryUsage 获取当前系统的内存总大小和已使用大小
// Deprecated: 不支持容器环境，CGROUP的 limit 相关配置，导致获取的内存比实际大的多
func VirtualMemoryUsage() (uint64, uint64, error) {
	v, err := mem.VirtualMemory()
	if err != nil {
		return 0, 0, errors.Wrap(err, "failed to get virtual memory info")
	}
	return v.Total, v.Used, nil
}

// CgroupMemoryDetails 获取当前运行环境的内存限制和当前内存使用量
func CgroupMemoryDetails() (limit uint64, usage uint64, err error) {
	basePath := "/sys/fs/cgroup/memory/"
	limitPath := filepath.Join(basePath, "memory.limit_in_bytes")
	usagePath := filepath.Join(basePath, "memory.usage_in_bytes")

	limit, err = readCgroupMemoryValue(limitPath)
	if err != nil {
		return 0, 0, errors.WithMessage(err, "error reading cgroup memory limit")
	}

	usage, err = readCgroupMemoryValue(usagePath)
	if err != nil {
		return limit, 0, errors.WithMessage(err, "error reading cgroup memory usage")
	}

	return limit, usage, nil
}

// readCgroupMemoryValue 从指定路径读取内存值
func readCgroupMemoryValue(filePath string) (uint64, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return 0, errors.Wrap(err, "failed to read file")
	}
	valueStr := strings.TrimSpace(string(content))
	value, err := strconv.ParseUint(valueStr, 10, 64)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to parse memory value from file %s, value: %s", filePath, valueStr)
	}
	return value, nil
}

// GetRSSMemory 获取 cgroup 中 total_rss 的内存使用量
func GetRSSMemory() int64 {
	cmd := exec.Command("bash", "-c", "cat /sys/fs/cgroup/memory/memory.stat | grep -v huge | grep total_rss")
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		log.Warn("get memory info error, return default rss 1.5 GB")
		return 1.5 * 1024 * 1024 * 1024
	}

	result := out.String()
	fields := strings.Fields(result)
	if len(fields) < 2 {
		log.Warn("failed to parse memory info, return default rss 1.5 GB")
		return 1.5 * 1024 * 1024 * 1024
	}

	memoryBytes, err := strconv.ParseInt(fields[1], 10, 64)
	if err != nil {
		log.Warn("failed to convert memory bytes to float: %v, return default rss 1.5 GB", err)
		return 1.5 * 1024 * 1024 * 1024
	}

	return memoryBytes
}

// GetWSSMemory 获取 cgroup 中 working set size 的内存使用量
func GetWSSMemory(allMemory int64) (int64, int64, error) {
	cmd := exec.Command("df", "-lh")
	log.Info("get wss memory cmd: %s", cmd.String())
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return -1, -1, errors.Wrap(err, "get wss memory info error")
	}

	result := out.String()
	if len(result) > 0 {
		var head, mem string
		array := strings.Split(strings.TrimSpace(result), "\n")
		for _, item := range array {
			if strings.Contains(item, "Filesystem") {
				head = item
				continue
			}
			// /data/cdb默认挂载的是内存盘
			if strings.Contains(item, "/data/cdb") {
				mem = item
				log.Info("get /data/cdb memory info: %s", mem)
				break
			}
		}

		if mem != "" {
			headArray := filterNonEmpty(strings.Split(head, " "))
			memArray := filterNonEmpty(strings.Split(mem, " "))
			uIUsed := findIndex(headArray, "Used")
			uISize := findIndex(headArray, "Size")

			if uIUsed != -1 && uISize != -1 {
				memCost, err := ParseSize(memArray[uIUsed])
				//memCost, err := strconv.ParseInt(memArray[uIUsed], 10, 64)
				if err != nil {
					return -1, -1, errors.Wrap(err, "failed to convert memCost to int")
				}
				memSize, err := ParseSize(memArray[uISize])
				//memSize, err := strconv.ParseInt(memArray[uISize], 10, 64)
				if err != nil {
					return -1, -1, errors.Wrap(err, "failed to convert memSize to int")
				}

				if memCost >= 0 {
					return memCost, memSize, nil
				}
			}
		}
	}

	// 没有mount的情况
	// 注意 du命令返回默认单位为KB. 节点上可以使用du -sh /app/rel/data/cdb/查看
	// 注意目录结尾要有/
	cmd = exec.Command("du", "-s", "/app/rel/data/cdb/")
	log.Info("get index disk size cmd: %s", cmd.String())
	out.Reset()
	cmd.Stdout = &out
	err = cmd.Run()
	if err != nil {
		return -1, -1, errors.Wrap(err, "get index disk size error")
	}

	duCmdResult := out.String()
	log.Info("get index disk size result: %s", duCmdResult)
	usedMemoryStr := strings.Fields(duCmdResult)[0]
	usedMemory, err := strconv.ParseInt(usedMemoryStr, 10, 64)
	if err != nil {
		return -1, -1, errors.Wrap(err, "failed to convert usedMemory to int")
	}
	// 转为byte
	usedMemory *= 1024

	return usedMemory, allMemory, nil
}

func ParseSize(sizeStr string) (int64, error) {
	length := len(sizeStr)
	if length < 2 {
		log.Warn("invalid size format: %s", sizeStr)
		// 尝试直接转换为数字
		return strconv.ParseInt(sizeStr, 10, 64)
	}

	unit := sizeStr[length-1]  // Last character, the unit
	size := sizeStr[:length-1] // Size part of the string
	sizeFloat, err := strconv.ParseFloat(size, 64)
	if err != nil {
		return 0, err
	}

	switch unit {
	case 'G', 'g':
		return int64(sizeFloat * 1024 * 1024 * 1024), nil
	case 'M', 'm':
		return int64(sizeFloat * 1024 * 1024), nil
	case 'K', 'k':
		return int64(sizeFloat * 1024), nil
	default:
		return int64(sizeFloat), nil // Assuming it's already in bytes if no unit
	}
}

func findIndex(array []string, search string) int {
	for i, item := range array {
		if strings.Contains(item, search) {
			return i
		}
	}
	return -1
}

func filterNonEmpty(array []string) []string {
	var filtered []string
	for _, item := range array {
		if strings.TrimSpace(item) != "" {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

func IsCmdExist(cmd string) bool {
	_, err := exec.LookPath(cmd)
	return err == nil
}

// CheckDiskUsageAfterMove 检查将源目录移动到目标目录后的磁盘使用率是否超过限制
// dstDir: 目标目录
// srcDir: 源目录
// dstUsageLimit: 目标目录允许使用的磁盘比例(百分比, 例如60表示60%)
// totalUsageLimit: 磁盘整体允许使用的比例(百分比, 例如90表示90%)
// 返回值:
// - exceed: 是否超过限制
// - err: 错误信息
func CheckDiskUsageAfterMove(dstDir, srcDir string, dstUsageLimit, totalUsageLimit int) (exceed bool, msg string, err error) {
	// 获取目标目录所在磁盘的总大小和已使用空间
	diskTotal, diskUsed, err := DiskUsage(dstDir)
	if err != nil {
		return false, "", errors.WithMessagef(err, "get disk usage failed for dst dir: %s", dstDir)
	}
	log.Info("dstDisk usage: %s(diskTotal: %s, diskUsed: %s)", dstDir, ByteToHumanReadable(int64(diskTotal)), ByteToHumanReadable(int64(diskUsed)))

	// 获取目标目录当前大小
	// 在 mac 系统下使用 -s 参数, linux 系统下使用 -sb 参数
	var duArgs string
	if runtime.GOOS == "darwin" {
		duArgs = "-s"
	} else {
		duArgs = "-sb"
	}
	cmd := exec.Command("du", duArgs, dstDir)
	log.Info("get dstDir size cmd: %s", cmd.String())
	var out bytes.Buffer
	cmd.Stdout = &out
	err = cmd.Run()
	if err != nil {
		return false, "", errors.WithMessagef(err, "get destination directory size failed: %s", dstDir)
	}
	log.Info("get dstDir size cmd result: %s", out.String())
	dstSize, err := strconv.ParseUint(strings.Fields(out.String())[0], 10, 64)
	if err != nil {
		return false, "", errors.WithMessagef(err, "parse destination directory size failed: %s", out.String())
	}
	log.Info("dstDirSize: %d", dstSize)

	// 获取源目录大小
	out.Reset()
	cmd = exec.Command("du", duArgs, srcDir)
	log.Info("get srcDir size cmd: %s", cmd.String())
	cmd.Stdout = &out
	err = cmd.Run()
	if err != nil {
		return false, "", errors.WithMessagef(err, "get source directory size failed: %s", srcDir)
	}
	log.Info("get srcDir size cmd result: %s", out.String())
	srcSize, err := strconv.ParseUint(strings.Fields(out.String())[0], 10, 64)
	if err != nil {
		return false, "", errors.WithMessagef(err, "parse source directory size failed: %s", out.String())
	}
	log.Info("srcDirSize: %d", srcSize)

	// 计算移动后目标目录的使用率
	afterMoveDstUsage := (dstSize + srcSize) * 100 / diskTotal
	isExceeded := afterMoveDstUsage > uint64(dstUsageLimit)
	msg = fmt.Sprintf("dstUsageLimitThreshold %s: (dstDirSize[%s] + srcDirSize[%s]) / diskTotal[%s] %s dstUsageLimitThreshold[%d%%]",
		func() string {
			if isExceeded {
				return "exceeded"
			}
			return "not exceeded"
		}(),
		ByteToHumanReadable(int64(dstSize)),
		ByteToHumanReadable(int64(srcSize)),
		ByteToHumanReadable(int64(diskTotal)),
		func() string {
			if isExceeded {
				return ">"
			}
			return "<="
		}(),
		dstUsageLimit)
	log.Info(msg)
	if isExceeded {
		return true, msg, nil
	}

	// 计算移动后磁盘整体的使用率
	afterMoveTotalUsage := (diskUsed + srcSize) * 100 / diskTotal
	isTotalExceeded := afterMoveTotalUsage > uint64(totalUsageLimit)
	msg = fmt.Sprintf("totalUsageLimitThreshold %s: (diskUsed[%s] + srcDirSize[%s]) / diskTotal[%s] %s totalUsageLimitThreshold[%d%%]",
		func() string {
			if isTotalExceeded {
				return "exceeded"
			}
			return "not exceeded"
		}(),
		ByteToHumanReadable(int64(diskUsed)),
		ByteToHumanReadable(int64(srcSize)),
		ByteToHumanReadable(int64(diskTotal)),
		func() string {
			if isTotalExceeded {
				return ">"
			}
			return "<="
		}(),
		totalUsageLimit)
	log.Info(msg)
	if isTotalExceeded {
		return true, msg, nil
	}

	return false, "", nil
}

// GetProcessRSS 获取指定进程名称使用的 rss
func GetProcessRSS(processName string) (int64, error) {
	// 获取指定进程名称的进程ID
	pid, err := GetPID(processName)
	if err != nil {
		return 0, errors.Wrap(err, "failed to get process ID")
	}
	// 根据 pid 获取进程的 rss
	// 使用命令 ps -o rss= -p pid
	// ps -o rss= 命令用于获取进程的RSS（Resident Set Size）, 结果为进程使用的物理内存大小, 单位为KB
	// -p pid 选项用于指定要查询的进程ID
	cmd := exec.Command("ps", "-o", "rss=", "-p", strconv.Itoa(pid))
	log.Info("get process rss cmd: %s", cmd.String())
	output, err := cmd.Output()
	if err != nil {
		return 0, errors.Wrap(err, "failed to execute ps command")
	}
	// 解析输出
	outputStr := string(output)
	log.Info("get process rss cmd output: %s", outputStr)
	// 去除换行符
	outputStr = strings.TrimSpace(outputStr)
	// 转换为int64
	rss, err := strconv.ParseInt(outputStr, 10, 64)
	if err != nil {
		return 0, errors.Wrap(err, "failed to convert rss to int64")
	}
	// 转换为 byte
	rss *= 1024
	return rss, nil
}

// GetProcessMemoryUsageRate 获取指定进程占用的内存比例
func GetProcessMemoryUsageRate(processName string) (float64, error) {
	// 获取指定进程名称的进程ID
	pid, err := GetPID(processName)
	if err != nil {
		return 0, errors.Wrap(err, "failed to get process ID")
	}
	// 根据 pid 获取进程的内存占用比例
	// 使用命令 ps -o pmem= -p pid
	// ps -o pmem= 命令用于获取进程的内存占用百分比
	// -p pid 选项用于指定要查询的进程ID
	cmd := exec.Command("ps", "-o", "pmem=", "-p", strconv.Itoa(pid))
	log.Info("get process memory usage rate cmd: %s", cmd.String())
	output, err := cmd.Output()
	if err != nil {
		return 0, errors.Wrap(err, "failed to execute ps command")
	}
	// 解析输出
	outputStr := string(output)
	log.Info("get process memory usage rate cmd output: %s", outputStr)
	// 去除换行符
	outputStr = strings.TrimSpace(outputStr)
	// 转换为float64
	memoryUsageRate, err := strconv.ParseFloat(outputStr, 64)
	if err != nil {
		return 0, errors.Wrap(err, "failed to parse memory usage rate")
	}
	return memoryUsageRate, nil
}

// GetPID 获取指定进程名称的进程ID
func GetPID(processName string) (int, error) {
	// 如果processName为shell脚本，需要使用 ps -eo pid,cmd | grep 'shellName'
	if strings.HasSuffix(processName, ".sh") {
		return GetPIDByShellScript(processName)
	}
	// 使用ps和grep组合命令查找进程
	// ps -eo pid,comm | grep 'processName'
	// ps -eo pid,comm 命令用于列出所有进程的进程ID（PID）和命令名（comm）
	// exec.Command 默认不支持shell特性如管道 | 、重定向 > 等，需要使用shell来执行(sh -c "ps -eo pid,comm | grep 'processName'")
	cmd := exec.Command("sh", "-c", fmt.Sprintf("ps -eo pid,comm | grep '%s'", processName))
	log.Info("get pid cmd: %s", cmd.String())
	output, err := cmd.Output()
	if err != nil {
		// grep没找到结果会返回非零状态码，需要特殊处理
		if exitErr, ok := err.(*exec.ExitError); ok && exitErr.ExitCode() == 1 {
			return 0, errors.Errorf("process not found: %s", processName)
		}
		return 0, errors.Wrap(err, "failed to execute ps command")
	}

	// 解析输出
	outputStr := string(output)
	log.Info("get pid cmd output: %s", outputStr)
	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) >= 2 {
			// 排除grep命令
			if strings.Contains(fields[1], "grep") {
				continue
			}
			pidStr := fields[0]
			if pid, err := strconv.Atoi(pidStr); err == nil {
				return pid, nil
			}
		}
	}

	return 0, errors.Errorf("process not found: %s", processName)
}

// GetPIDByShellScript 获取指定shell脚本的进程ID
func GetPIDByShellScript(shellName string) (int, error) {
	// 使用ps命令只输出pid和cmd两列，简化解析
	// ps -eo pid,cmd | grep 'shellName'
	// ps -eo pid,cmd 命令用于列出所有进程的PID和完整命令行
	cmd := exec.Command("sh", "-c", fmt.Sprintf("ps -eo pid,cmd | grep '%s'", shellName))
	log.Info("get pid cmd: %s", cmd.String())
	output, err := cmd.Output()
	if err != nil {
		// grep没找到结果会返回非零状态码，需要特殊处理
		if exitErr, ok := err.(*exec.ExitError); ok && exitErr.ExitCode() == 1 {
			return 0, errors.Errorf("process not found: %s", shellName)
		}
		return 0, errors.Wrap(err, "failed to execute ps command")
	}

	// 解析输出: 10100 /bin/bash /path/to/your/script.sh
	// 现在第一列就是PID
	outputStr := string(output)
	log.Info("get pid cmd output: %s", outputStr)
	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) >= 2 { // 至少有PID和命令两列
			// 排除grep命令本身
			if strings.Contains(line, "grep") {
				continue
			}
			// 确认是目标shell脚本
			if !strings.Contains(line, shellName) {
				continue
			}
			pidStr := fields[0] // 第一列是PID
			if pid, err := strconv.Atoi(pidStr); err == nil {
				return pid, nil
			}
		}
	}

	return 0, errors.Errorf("process not found: %s", shellName)
}
