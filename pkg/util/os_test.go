package util

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"testing"
)

func TestGetWSSMemory(t *testing.T) {
	result := `Filesystem     1K-blocks      Used Available Use% Mounted on
overlay        ********* ********* *********  39% /
tmpfs              65536         0     65536   0% /dev
tmpfs          *********         0 *********   0% /sys/fs/cgroup
/dev/nvme2n1   ********* ********* *********  39% /tmp
tmpfs          *********         8 *********   1% /etc/podinfo
/dev/nvme0n1p1 *********  ********  ********  13% /etc/localtime
tmpfs          *********  ********  ********  74% /data/cdb
shm                65536         0     65536   0% /dev/shm
/dev/nvme8n1   *********       204 *********   1% /app/rel/data
tmpfs          *********        12 *********   1% /run/secrets/kubernetes.io/serviceaccount
tmpfs          *********         0 *********   0% /proc/acpi
tmpfs          *********         0 *********   0% /proc/scsi
tmpfs          *********         0 *********   0% /sys/firmware`

	if len(result) > 0 {
		var head, mem string
		array := strings.Split(strings.TrimSpace(result), "\n")
		for _, item := range array {
			if strings.Contains(item, "Filesystem") {
				head = item
				continue
			}
			if strings.Contains(item, "/data/cdb") {
				mem = item
				break
			}
		}

		if mem != "" {
			headArray := filterNonEmpty(strings.Split(head, " "))
			memArray := filterNonEmpty(strings.Split(mem, " "))
			uIUsed := findIndex(headArray, "Used")
			uISize := findIndex(headArray, "Size")

			if uIUsed != -1 && uISize != -1 {
				memCost, err := strconv.ParseInt(memArray[uIUsed], 10, 64)
				if err != nil {
					fmt.Printf("%+v", errors.Wrap(err, "failed to convert memCost to int"))
					return
				}
				memSize, err := strconv.ParseInt(memArray[uISize], 10, 64)
				if err != nil {
					fmt.Printf("%+v", errors.Wrap(err, "failed to convert memSize to int"))
					return
				}

				if memCost > 0 {
					//return memCost, memSize, nil
					fmt.Printf("memCost: %d, memSize: %d\n", memCost, memSize)
				}
			}
		}
	}

}

func TestGetWSSMemory2(t *testing.T) {
	result := `Filesystem      Size  Used Avail Use% Mounted on
overlay         387G  142G  225G  39% /
tmpfs            64M     0   64M   0% /dev
tmpfs           248G     0  248G   0% /sys/fs/cgroup
/dev/nvme2n1    387G  142G  225G  39% /tmp
tmpfs           479G  8.0K  479G   1% /etc/podinfo
/dev/nvme0n1p1   99G   12G   83G  13% /etc/localtime
tmpfs           100G   75G   26G  75% /data/cdb
shm              64M     0   64M   0% /dev/shm
/dev/nvme8n1    295G  204K  295G   1% /app/rel/data
tmpfs           479G   12K  479G   1% /run/secrets/kubernetes.io/serviceaccount
tmpfs           248G     0  248G   0% /proc/acpi
tmpfs           248G     0  248G   0% /proc/scsi
tmpfs           248G     0  248G   0% /sys/firmware`
	if len(result) > 0 {
		var head, mem string
		array := strings.Split(strings.TrimSpace(result), "\n")
		for _, item := range array {
			if strings.Contains(item, "Filesystem") {
				head = item
				continue
			}
			if strings.Contains(item, "/data/cdb") {
				mem = item
				break
			}
		}

		if mem != "" {
			headArray := filterNonEmpty(strings.Split(head, " "))
			memArray := filterNonEmpty(strings.Split(mem, " "))
			uIUsed := findIndex(headArray, "Used")
			uISize := findIndex(headArray, "Size")

			if uIUsed != -1 && uISize != -1 {
				memCost, err := ParseSize(memArray[uIUsed])
				//memCost, err := strconv.ParseInt(memArray[uIUsed], 10, 64)
				if err != nil {
					fmt.Printf("%+v", errors.Wrap(err, "failed to convert memCost to int"))
					return
				}
				memSize, err := ParseSize(memArray[uISize])
				//memSize, err := strconv.ParseInt(memArray[uISize], 10, 64)
				if err != nil {
					fmt.Printf("%+v", errors.Wrap(err, "failed to convert memSize to int"))
					return
				}

				if memCost > 0 {
					//return memCost, memSize, nil
					fmt.Printf("memCost: %d, memSize: %d\n", memCost, memSize)
					fmt.Printf("memCost -h: %s, memSize -h: %s\n", ByteToHumanReadable(memCost), ByteToHumanReadable(memSize))
				}
			}
		}
	}
}

func TestGetWSSMemory3(t *testing.T) {
	result := `76965044        /app/rel/data/cdb/`
	usedMemoryStr := strings.Fields(result)[0]
	usedMemory, err := strconv.ParseInt(usedMemoryStr, 10, 64)
	if err != nil {
		//return -1, -1, errors.Wrap(err, "failed to convert usedMemory to int")
		fmt.Printf("%+v", errors.Wrap(err, "failed to convert usedMemory to int"))
		return
	}
	// 转为byte
	usedMemory *= 1024
	fmt.Printf("usedMemory: %d\n", usedMemory)
	fmt.Printf("usedMemory -h: %s\n", ByteToHumanReadable(usedMemory))
}

func TestCheckDiskUsageAfterMove(t *testing.T) {
	tests := []struct {
		name            string
		setupDirs       func(t *testing.T) (string, string) // 返回 dstDir, srcDir
		dstUsageLimit   int
		totalUsageLimit int
		wantExceed      bool
		wantErr         bool
	}{
		{
			name: "正常移动-未超过限制",
			setupDirs: func(t *testing.T) (string, string) {
				// 创建临时目录
				tmpDir, err := os.MkdirTemp("", "disk_usage_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				// 创建目标目录和源目录
				dstDir := filepath.Join(tmpDir, "dst")
				srcDir := filepath.Join(tmpDir, "src")

				// 在目标目录中创建一个10MB的文件
				if err := os.MkdirAll(dstDir, 0755); err != nil {
					t.Fatalf("创建目标目录失败: %v", err)
				}
				if err := createTestFile(filepath.Join(dstDir, "dst_file"), 10*1024*1024); err != nil {
					t.Fatalf("创建目标文件失败: %v", err)
				}

				// 在源目录中创建一个5MB的文件
				if err := os.MkdirAll(srcDir, 0755); err != nil {
					t.Fatalf("创建源目录失败: %v", err)
				}
				if err := createTestFile(filepath.Join(srcDir, "src_file"), 5*1024*1024); err != nil {
					t.Fatalf("创建源文件失败: %v", err)
				}

				return dstDir, srcDir
			},
			dstUsageLimit:   80,
			totalUsageLimit: 90,
			wantExceed:      false,
			wantErr:         false,
		},
		{
			name: "超过目标目录限制",
			setupDirs: func(t *testing.T) (string, string) {
				tmpDir, err := os.MkdirTemp("", "disk_usage_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				dstDir := filepath.Join(tmpDir, "dst")
				srcDir := filepath.Join(tmpDir, "src")

				// 在目标目录中创建一个大文件
				if err := os.MkdirAll(dstDir, 0755); err != nil {
					t.Fatalf("创建目标目录失败: %v", err)
				}
				if err := createTestFile(filepath.Join(dstDir, "dst_file"), 100*1024*1024); err != nil {
					t.Fatalf("创建目标文件失败: %v", err)
				}

				// 在源目录中创建一个大文件
				if err := os.MkdirAll(srcDir, 0755); err != nil {
					t.Fatalf("创建源目录失败: %v", err)
				}
				if err := createTestFile(filepath.Join(srcDir, "src_file"), 100*1024*1024); err != nil {
					t.Fatalf("创建源文件失败: %v", err)
				}

				return dstDir, srcDir
			},
			dstUsageLimit:   1,
			totalUsageLimit: 10,
			wantExceed:      true,
			wantErr:         false,
		},
		{
			name: "超过总体限制",
			setupDirs: func(t *testing.T) (string, string) {
				tmpDir, err := os.MkdirTemp("", "disk_usage_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				dstDir := filepath.Join(tmpDir, "dst")
				srcDir := filepath.Join(tmpDir, "src")

				if err := os.MkdirAll(dstDir, 0755); err != nil {
					t.Fatalf("创建目标目录失败: %v", err)
				}
				if err := os.MkdirAll(srcDir, 0755); err != nil {
					t.Fatalf("创建源目录失败: %v", err)
				}
				if err := createTestFile(filepath.Join(srcDir, "src_file"), 1024*1024*1024); err != nil { // 1GB
					t.Fatalf("创建源文件失败: %v", err)
				}

				return dstDir, srcDir
			},
			dstUsageLimit:   90,
			totalUsageLimit: 10,
			wantExceed:      true,
			wantErr:         false,
		},
		{
			name: "源目录不存在",
			setupDirs: func(t *testing.T) (string, string) {
				tmpDir, err := os.MkdirTemp("", "disk_usage_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				dstDir := filepath.Join(tmpDir, "dst")
				if err := os.MkdirAll(dstDir, 0755); err != nil {
					t.Fatalf("创建目标目录失败: %v", err)
				}

				return dstDir, "/non/existent/dir"
			},
			dstUsageLimit:   80,
			totalUsageLimit: 90,
			wantExceed:      false,
			wantErr:         true,
		},
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试目录
			dstDir, srcDir := tt.setupDirs(t)
			if filepath.Dir(dstDir) != "/non" {
				defer os.RemoveAll(filepath.Dir(dstDir))
			}

			// 执行测试
			exceed, msg, err := CheckDiskUsageAfterMove(dstDir, srcDir, tt.dstUsageLimit, tt.totalUsageLimit)
			log.Info("check msg: %s", msg)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckDiskUsageAfterMove() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if exceed != tt.wantExceed {
				t.Errorf("CheckDiskUsageAfterMove() exceed = %v, want %v", exceed, tt.wantExceed)
			}
		})
	}
}

// createTestFile 创建指定大小的测试文件
func createTestFile(path string, size int64) error {
	f, err := os.Create(path)
	if err != nil {
		return err
	}
	defer f.Close()

	// 写入指定大小的数据
	if err := f.Truncate(size); err != nil {
		return err
	}
	return nil
}
