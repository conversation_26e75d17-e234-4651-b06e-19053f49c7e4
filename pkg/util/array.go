package util

func ContainsString(arr []string, s string) bool {
	if len(arr) == 0 {
		return false
	}
	for _, v := range arr {
		if v == s {
			return true
		}
	}
	return false
}

func ContainsStrFunc[T any](arr []T, s string, f func(T) string) bool {
	if len(arr) == 0 {
		return false
	}
	for _, v := range arr {
		vs := f(v)
		if vs == s {
			return true
		}
	}
	return false
}

// RemoveDuplicate 对输入的切片进行去重
func RemoveDuplicate[T comparable](arr []T) []T {
	if len(arr) == 0 {
		return arr
	}
	m := make(map[T]struct{})
	for _, v := range arr {
		m[v] = struct{}{}
	}
	var result []T
	for k := range m {
		result = append(result, k)
	}
	return result
}