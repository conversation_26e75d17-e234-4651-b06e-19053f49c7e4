package persistence

import (
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"errors"
	"os"
)

var (
	TaskNotExist = errors.New("task does not exist")
	FileNotExist = os.ErrNotExist // "file does not exist"

	// 持久化实现注册
	persistenceRegistry = make(map[string]TaskPersistence)

	// 全局持久化实现
	globalPersistence TaskPersistence
)

func Init(config Config) {
	p, exists := persistenceRegistry[config.Driver]
	if !exists {
		log.Fatal("persistence driver not found")
	}
	err := cfg.UnpackFromCommonCfgWithYaml(config.Properties, p.Config()).DefaultAndValidate()
	if err != nil {
		log.Fatal("persistence driver config error: %v", err)
	}
	p.Init()
	globalPersistence = p
}

type Config struct {
	Driver     string        `yaml:"driver" default:"file"`
	Properties cfg.CommonCfg `yaml:",inline"`
}

// RegisterPersistence 注册持久化实现
func RegisterPersistence(name string, p TaskPersistence) {
	persistenceRegistry[name] = p
}

type TaskStatusUpdate struct {
	Status      string `json:"status"`
	StartTime   string `json:"startTime"`
	ProcessTime string `json:"processTime"`
	EndTime     string `json:"endTime"`
}

// TaskPersistence 任务状态持久化接口
type TaskPersistence interface {
	Config() interface{}
	Init()
	Save(taskId string, status TaskStatusUpdate) error
	SaveAll(status map[string]TaskStatusUpdate) error
	Load(taskId string) (TaskStatusUpdate, error)
	LoadAll() (map[string]TaskStatusUpdate, error)
}

func Save(taskId string, status TaskStatusUpdate) error {
	return globalPersistence.Save(taskId, status)
}

func SaveAll(statuses map[string]TaskStatusUpdate) error {
	return globalPersistence.SaveAll(statuses)
}

func Load(taskId string) (TaskStatusUpdate, error) {
	return globalPersistence.Load(taskId)
}

func LoadAll() (map[string]TaskStatusUpdate, error) {
	return globalPersistence.LoadAll()
}
