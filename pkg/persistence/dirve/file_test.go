package dirve

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/persistence"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"sync"
	"testing"
)

func TestFilePersistence_Save(t *testing.T) {
	type fields struct {
		config *Config
		mu     sync.Mutex
	}
	type args struct {
		taskId string
		status persistence.TaskStatusUpdate
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "case1",
			fields: fields{
				config: &Config{
					FilePath: "/tmp/task_status.json",
				},
			},
			args: args{
				taskId: "999",
				status: persistence.TaskStatusUpdate{
					Status:      "FAIL",
					StartTime:   "2024-01-01 00:00:00",
					ProcessTime: "2024-01-01 00:00:00",
					EndTime:     "2024-01-01 00:00:00",
				},
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fp := &FilePersistence{
				config: tt.fields.config,
				mu:     tt.fields.mu,
			}
			if err := fp.Save(tt.args.taskId, tt.args.status); (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
