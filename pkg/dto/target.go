package dto

import (
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/util"
	"github.com/pkg/errors"
	"path/filepath"
	"strconv"
	"strings"
)

type Target struct {
	Id         int           `yaml:"id" json:"id"`
	Timestamp  int64         `yaml:"timestamp" json:"timestamp"`
	Env        string        `yaml:"env" json:"env"`
	MustFinish bool          `yaml:"mustFinish" json:"mustFinish"`
	Cluster    *ClusterInfo  `yaml:"cluster" json:"cluster" validate:"required,dive"`
	TargetInfo []*TargetItem `yaml:"targetInfo" json:"targetInfo" validate:"dive"`
}

type ClusterInfo struct {
	Id         int    `yaml:"id" json:"id" validate:"required"`                                               // 集群id
	Name       string `yaml:"name" json:"name" validate:"required"`                                           // 集群名称, 实际对应平台是appName
	Group      string `yaml:"group" json:"group" validate:"required"`                                         // 集群分组
	EngineType string `yaml:"engineType" json:"engineType" validate:"required,oneof=Dgraph Dsearch DFeature"` // 引擎类型
}

type TargetItem struct {
	Name       string     `yaml:"name" json:"name" validate:"required"`
	Version    []Version  `yaml:"version" json:"version" validate:"required,min=1,dive"`
	Type       ItemType   `yaml:"type" json:"type" validate:"required,oneof=index model file"`            // item类型
	UpdateType UpdateType `yaml:"updateType" json:"updateType" validate:"required,oneof=hot reboot auto"` // 更新方式
}

type Version struct {
	DataVersion  string `yaml:"dataVersion" json:"dataVersion" validate:"required"`
	BuildVersion string `yaml:"buildVersion" json:"buildVersion"`
	KeyId        string `yaml:"keyId" json:"keyId"`
}

type UpdateType string
type ItemType string

const (
	HotReload    UpdateType = "hot"    // 热更新
	RebootReload UpdateType = "reboot" // 重启更新
	Auto         UpdateType = "auto"   // 自动判断

	Index ItemType = "index" // 索引
	Model ItemType = "model" // 模型
	File  ItemType = "file"  // 文件
)

func BuildTargetFromFile(filePath string) (*Target, error) {
	if !util.IsPathExist(filePath) {
		return nil, errors.Errorf("target file not exist: %s", filePath)
	}

	// 文件路径格式样例: /logs/agent/target/target-{id}-{clusterId}-{timestamp}.yaml
	// 文件名格式校验
	// 根据文件全路径获取文件名称
	fileName := filepath.Base(filePath)
	if !strings.HasPrefix(fileName, "target-") || !strings.HasSuffix(filePath, ".yaml") {
		return nil, errors.Errorf("invalid target file name: %s", fileName)
	}
	// 提取targetId、timestamp
	parts := strings.Split(fileName, "-")
	if len(parts) != 4 {
		return nil, errors.Errorf("invalid target file name: %s", fileName)
	}
	targetIdStr := parts[1]
	targetId, err := strconv.Atoi(targetIdStr)
	if err != nil {
		return nil, errors.WithMessagef(err, "invalid target id: %s", targetIdStr)
	}
	timestampStr := parts[3][:len(parts[3])-5]
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return nil, errors.WithMessagef(err, "invalid target timestamp: %s", timestampStr)
	}

	// 读取文件
	target := &Target{}
	err = cfg.UnPackFromFile(filePath, target).DefaultAndValidate()
	if err != nil {
		return nil, errors.WithMessagef(err, "parse target file failed: %s", filePath)
	}
	target.Id = targetId
	target.Timestamp = timestamp

	return target, nil
}
