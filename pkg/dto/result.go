package dto

type DumpResult struct {
	Count      int64  `json:"count"`      // 拉取的数据条目数量
	Size       int64  `json:"size"`       // 拉取的数据大小
	QueryCount int64  `json:"queryCount"` // 查询的数据条目数量
	EmptyCount int64  `json:"emptyCount"` // 空条目的数量
	RequestId  string `json:"requestId"`  // 请求ID
	FileCount  int    `json:"fileCount"`  // 文件数量
	DumpInfo   string `json:"dumpInfo"`   // dump请求信息. json string

	DumpCost   string `json:"dumpCost"`   // dump耗时
	SessionId  string `json:"sessionId"`  // odps tunnel session id
	LogViewUrl string `json:"logViewUrl"` // odps log view url

	// 目前未使用
	SegmentNum       int32   `json:"segmentNum"`       // 分段数量
	SegmentFileCount []int32 `json:"segmentFileCount"` // 每个分段的数据文件数量
}

type SimpleDumpResult struct {
	Count      int64  `json:"count"`          // 拉取的数据条目数量
	Size       int64  `json:"size"`           // 拉取的数据大小
	QueryCount int64  `json:"queryCount"`     // 查询的数据条目数量
	EmptyCount int64  `json:"emptyCount"`     // 空条目的数量
	RequestId  string `json:"requestId"`      // 请求ID
	FileCount  int    `json:"fileCount"`      // 文件数量
	Warn       string `json:"warn,omitempty"` // 警告信息
	Sql        string `json:"sql"`            // sql

	DumpServiceInitCost string `json:"dumpServiceInitCost"` // dump service初始化耗时
	DumpCost            string `json:"dumpCost"`            // dump耗时
	MergeCost           string `json:"mergeCost"`           // merge耗时
	SessionId           string `json:"sessionId"`           // odps tunnel session id
	LogViewUrl          string `json:"logViewUrl"`          // odps log view url
}

func (dr *DumpResult) ToSimpleDumpResult() *SimpleDumpResult {
	return &SimpleDumpResult{
		Count:      dr.Count,
		Size:       dr.Size,
		QueryCount: dr.QueryCount,
		EmptyCount: dr.EmptyCount,
		RequestId:  dr.RequestId,
		FileCount:  dr.FileCount,
		DumpCost:   dr.DumpCost,
		SessionId:  dr.SessionId,
		LogViewUrl: dr.LogViewUrl,
	}
}
