package dto

import "time"

type BaseIndexInfo struct {
	IndexName    string `json:"indexName" validate:"required"`    // 索引名称
	IndexVersion string `json:"indexVersion" validate:"required"` // 索引版本
}

type IndexDef struct {
	IndexName       string                 `json:"indexName" validate:"required"`       // 索引名称
	IndexVersion    string                 `json:"indexVersion" validate:"required"`    // 索引版本
	IndexType       string                 `json:"indexType" validate:"required"`       // 索引类型
	KeyFieldName    string                 `json:"keyFieldName" validate:"required"`    // 主键字段名称
	KeyFieldType    string                 `json:"keyFieldType" validate:"required"`    // 主键字段类型
	IsDoubleVersion *bool                  `json:"isDoubleVersion"`                     // 是否双版本
	IsInc           *bool                  `json:"isInc" validate:"required"`           // 是否增量索引
	KeyId           string                 `json:"keyId"`                               // 索引的key_id
	IncConfig       *IncConfig             `json:"incConfig" validate:"omitempty,dive"` // 增量配置
	FileConfig      *FileConfig            `json:"fileConfig" validate:"required,dive"` // 文件配置
	MaxRowNum       int32                  `json:"maxRowNum"`                           // 倒排截断最大数
	Fields          []*FieldSchema         `json:"fields" validate:"dive,required"`     // 索引字段
	ExtraConfig     map[string]interface{} `json:"extraConfig"`                         // 索引额外配置
}

type IncConfig struct {
	Topic string `json:"topic" validate:"required"` // topic
	//Brokers                string `json:"brokers" validate:"required"`        // broker地址
	// 后期在平台配置topic的时候需要配置brokers
	Brokers                string `json:"brokers" `                           // broker地址
	IncType                string `json:"incType" validate:"required"`        // mq类型: kafka, datahub, rabbitmq
	StartTimestamp         int64  `json:"startTimestamp" validate:"required"` // 消费的开始时间戳
	IgnoreMessageTableName *bool  `json:"ignoreMessageTableName"`             // 是否忽略增量表名
}

type FileConfig struct {
	InvertedCount int32  `json:"invertedCount" validate:"required"` // 倒排大小
	Count         int32  `json:"count" validate:"required"`         // 正排大小
	LineFlag      string `json:"lineFlag"`                          // 行分隔符
}

type FieldSchema struct {
	Name                    string           `json:"name" validate:"required"`              // 字段名称
	IndexAliasName          string           `json:"indexAliasName"`                        // 字段别名
	Type                    string           `json:"type" validate:"required"`              // 字段类型
	IsWs                    *bool            `json:"isWs"`                                  // is_ws -1:否,1是
	IsArray                 *bool            `json:"isArray"`                               // 是否数组 -1:否,1是
	IsDefaultNeed           *bool            `json:"isDefaultNeed"`                         // 是否默认必需
	IsIndex                 *bool            `json:"isIndex"`                               // is_index -1:否,1是
	ArrayLen                int32            `json:"arrayLen"`                              // 数组长度
	IsDefaultValueAvailable *bool            `json:"isDefaultValueAvailable"`               // 是否有默认值
	DefaultValue            string           `json:"defaultValue"`                          // 默认值
	IndexType               string           `json:"indexType"`                             // 索引类型
	IndexParams             *FieldIndexParam `json:"indexParams" validate:"omitempty,dive"` // 索引参数
	ArrayMaxLen             int32            `json:"arrayMaxLen"`                           // 序列最大长度
	Slop                    int32            `json:"slop"`                                  // 精密度阀值
	IsTf                    *bool            `json:"isTf"`                                  // 词频
	IsPrefix                *bool            `json:"isPrefix"`                              // 前缀索引
	IsPostfix               *bool            `json:"isPostfix"`                             // 后缀索引
	IsPosition              *bool            `json:"isPosition"`                            // 位置
	IsOffset                *bool            `json:"isOffset"`                              // 偏移
	IsToString              *bool            `json:"isToString"`                            // string输出
	IsDisk                  *bool            `json:"isDisk"`                                // 磁盘存储
	Remark                  string           `json:"remark"`                                // 备注
	Config                  string           `json:"config"`                                // 字段额外配置
	IsNullable              *bool            `json:"isNullable" default:"false"`            // 是否允许null
	IsSparse                *bool            `json:"isSparse" default:"false"`              // 是否稀疏
}

type FieldIndexParam struct {
	MinNprobe         int32  `json:"minNprobe"`         // 最小探测
	MaxNprobe         int32  `json:"maxNprobe"`         // 最大探测
	K                 int32  `json:"k"`                 // K值
	Type              string `json:"type"`              // 类型
	MaxNum            int32  `json:"maxNum"`            // 最大数
	CentrolNorm       *bool  `json:"centrolNorm"`       // 中心标准化
	M                 int32  `json:"m"`                 // M值
	EfConstruction    int32  `json:"efConstruction"`    // EF构造
	D                 int32  `json:"d"`                 // D值
	HnswV2            *bool  `json:"hnswV2"`            // HNSW V2
	FullSpace         *bool  `json:"fullSpace"`         // 全空间
	SubSpaceFieldName string `json:"subSpaceFieldName"` // 子空间字段名
	SubSpaceNum       int32  `json:"subSpaceNum"`       // 子空间数量
	PerSegment        *bool  `json:"perSegment"`        // 段分布

	TrainSampleNum    int32 `json:"trainSampleNum"`    // 训练样本数
	LeavesNum         int32 `json:"leavesNum"`         // 叶子节点数
	NumLeavesToSearch int32 `json:"numLeavesToSearch"` // 搜索叶子节点数
	ReorderNum        int32 `json:"reorderNum"`        // 重排数
}

type ExecParam struct {
	Table           string   `json:"table"`
	ServingType     string   `json:"serving_type"`
	LoadType        string   `json:"load_type"`
	ServingVersions []string `json:"serving_versions"`
}

// UpdateIndexInfo 待更新的索引信息
type UpdateIndexInfo struct {
	IndexName                 string `json:"indexName" validate:"required"`                                    // 必填. 索引名称
	IndexVersion              string `json:"indexVersion" validate:"required"`                                 // 必填. 索引版本
	DbPath                    string `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"` // 引擎构建出的索引存放目录
	GzPath                    string `json:"gzPath,omitempty" default:"/app/rel/data/gz" validate:"required"`  // 临时存放gz文件的目录
	Compress                  *bool  `json:"compress,omitempty" default:"false"`                               // 是否压缩, 默认false
	Checksum                  *bool  `json:"checksum,omitempty" default:"true"`                                // 是否进行文件完整性校验
	ChecksumFileName          string `json:"checksumFileName,omitempty" default:"checksum.json"`               // 校验和文件名称
	EngineBuildResultFileName string `json:"engineBuildResultFileName,omitempty" default:"check.json"`         // engine build结果文件名称
	AgentConfigDirName        string `json:"agentConfigDirName,omitempty" default:"agent_config"`              // agent配置文件目录名称
	DumpResultFileName        string `json:"dumpResultFileName,omitempty" default:"dump-result.json"`          // dump结果文件名称

	DiskUsageThreshold   int                    `json:"diskUsageThreshold,omitempty" default:"93"`   // 磁盘使用阈值, 默认90%
	MemoryUsageThreshold int                    `json:"memoryUsageThreshold,omitempty" default:"93"` // 内存使用阈值, 默认90%
	MemoryInflateFactor  int                    `json:"memoryInflateFactor,omitempty" default:"5"`   // 索引加载到内存的膨胀比率, 默认5%
	ExtraConfig          map[string]interface{} `json:"extraConfig"`                                 // 可选. 额外配置
	ReportUrl            string                 `json:"reportUrl"  validate:"omitempty,url"`         // 必填. 状态上报url
	CallbackUrl          string                 `json:"callbackUrl" validate:"omitempty,url"`        // 必填. 执行结果回调url

	EngineServerShellCmdPath      string        `json:"engineServerShellCmdPath,omitempty" default:"/usr/bin/bash" validate:"required"` // engine服务的停止脚本的文件路径
	EngineShellPath               string        `json:"engineShellPath,omitempty" default:"/app/rel/bin/appctl.sh" validate:"required"` // engine服务的停止脚本的文件路径
	EngineServerStopCmdArgs       []string      `json:"engineServerStopCmdArgs,omitempty" validate:"required,min=1"`                    // engine服务的停止脚本的参数(目前搜索&推荐为stop doe-server)
	EngineServerStopCheckCmdArgs  []string      `json:"engineServerStopCheckCmdArgs,omitempty" validate:"required,min=1"`               // engine服务的停止脚本的校验参数(目前搜索&推荐为check_stop doe-server)
	EngineServerStartCmdArgs      []string      `json:"engineServerStartCmdArgs,omitempty" validate:"required,min=1"`                   // engine服务的启动脚本的参数(目前搜索&推荐为start doe-server)
	IsDoubleVersion               *bool         `json:"isDoubleVersion" validate:"required" default:"false"`                            // 是否双版本索引
	ServingVersions               []string      `json:"servingVersions" validate:"required,min=1"`                                      // 索引最近更新成功的n个版本(即双版本索引应该保留的n个版本). 默认为n=2
	NeedCommit                    *bool         `json:"needCommit" default:"true"`                                                      // 是否需要commit
	CommitSleepTime               int           `json:"commitSleepTime" default:"18"`                                                   // commit后的等待时间, 单位秒.等待引擎延迟释放(dgraph的设定)
	NeedDumpCheck                 *bool         `json:"needDumpCheck,omitempty" default:"false"`                                        // 是否需要dump校验
	UnloadCheckWaitTime           time.Duration `json:"unloadCheckWaitTime" default:"5s"`                                               // 卸载索引检查等待时间
	UnloadCheckTimeout            time.Duration `json:"unloadCheckTimeout" default:"5m"`                                                // 卸载索引检查超时时间
	MoveWithRsync                 *bool         `json:"moveWithRsync,omitempty"`                                                        // 是否使用rsync移动索引
	IsSkipWaitInc                 *bool         `json:"isSkipWaitInc,omitempty" default:"false"`                                        // 是否跳过等待追增量
	SleepTimeAfterUnload          time.Duration `json:"sleepTimeAfterUnload" default:"18s"`                                             // 卸载索引后等待时间
	BackupDiskUsageRatio          int           `json:"backupDiskUsageRatio"`                                                           // 集群索引备份磁盘使用率阈值
	BackupDir                     string        `json:"backupIndexDir,omitempty" default:"/app/rel/data/backup" validate:"required"`    // 备份索引的路径
	HotReloadRsyncMemoryThreshold int           `json:"hotReloadRsyncMemoryThreshold" default:"83"`                                     // 热更新时, 如果内存使用率超过该阈值, 则使用rsync移动索引

	Oss *Oss `json:"oss,omitempty" validate:"omitempty,dive"` // oss相关配置, 根据Datasource填充

	//buildResult *engine.BuildResult // 构建结果
	CanUpdate bool                   `json:"canUpdate"` // 是否可以更新
	Reason    string                 `json:"reason"`    // 不能更新的原因
	ReportExt map[string]interface{} `json:"reportExt"` // 上报的扩展信息
}

type DeleteIndexInfo struct {
	IndexName           string        `json:"indexName" validate:"required"`                                    // 必填. 索引名称
	IndexVersion        string        `json:"indexVersion"`                                                     // 非必填. 索引版本
	DbPath              string        `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"` // 引擎构建出的索引存放目录
	GzPath              string        `json:"gzPath,omitempty" default:"/app/rel/data/gz" validate:"required"`  // 临时存放gz文件的目录
	IsDoubleVersion     *bool         `json:"isDoubleVersion" validate:"required"`                              // 是否双版本索引
	NeedCommit          *bool         `json:"needCommit" default:"true"`                                        // 是否需要commit
	UnloadCheckWaitTime time.Duration `json:"unloadCheckWaitTime" default:"5s"`                                 // 卸载索引检查等待时间
	UnloadCheckTimeout  time.Duration `json:"unloadCheckTimeout" default:"5m"`                                  // 卸载索引检查超时时间
	QpsCheckRetryCount  int           `json:"qpsCheckRetryCount" default:"20"`                                  // QPS检查重试次数
	QpsCheckRetryWait   time.Duration `json:"qpsCheckRetryWait" default:"5s"`                                   // QPS检查重试间隔
}
