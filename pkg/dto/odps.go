package dto

type Odps struct {
	AccessKey string `json:"accessKey" validate:"required"` // ODPS ak
	SecretKey string `json:"secretKey" validate:"required"` // ODPS sk
	Endpoint  string `json:"endpoint" validate:"required"`  // ODPS endpoint
	Project   string `json:"project" validate:"required"`   // ODPS project

	Concurrency        int               `json:"concurrency"`              // 拉取并发数
	Hints              map[string]string `json:"hints"`                    // odps hint
	Sql                string            `json:"sql" validate:"required"`  // ODPS sql
	TableName          string            `json:"tableName"`                // ODPS table name
	PartitionField     string            `json:"partitionField"`           // 分区字段
	PartitionCondition string            `json:"partitionCondition"`       // 分区条件
	DumpMethod         string            `json:"dumpMethod"`               // dump method: 目前仅仅table_tunnel和instance_tunnel,默认为instance_tunnel
	TunnelEndpoint     string            `json:"tunnelEndpoint,omitempty"` // odps tunnel endpoint
}
