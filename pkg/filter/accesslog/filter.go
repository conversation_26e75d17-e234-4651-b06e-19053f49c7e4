package accesslog

import (
	"bytes"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

const (
	MaxLength   = 8000
	SuccessCode = 200
)

var (
	// 忽略的静态资源
	ignoreStaticResource = []string{"html", "/icons", "/img", ".js", ".css", ".jpg", ".jpeg", ".png", ".gif", ".ico", ".svg", ".woff", ".woff2", ".ttf", ".eot"}
	// 忽略的path
	ignorePath = []string{"/health", "/version", "/metrics", "/swagger", "/swagger.json", "/ops/health/readiness"}
)

func init() {
	web.RegisterFilter(NewFilter())
}

type AccessLog struct {
	Success      bool          `json:"success"`      // 是否成功
	HttpStatus   int           `json:"httpStatus"`   // HTTP状态码
	Method       string        `json:"method"`       // HTTP方法
	ClientIP     string        `json:"clientIP"`     // 客户端IP
	Cost         time.Duration `json:"cost"`         // 耗时
	Uri          string        `json:"uri"`          // 请求的URI
	Parameters   string        `json:"parameters"`   // 请求参数
	Response     string        `json:"response"`     // 响应内容
	Other        string        `json:"other"`        // 其他信息
	RequestTime  time.Time     `json:"requestTime"`  // 请求时间
	ResponseTime time.Time     `json:"responseTime"` // 响应时间
	Exception    string        `json:"exception"`    // 异常信息
}

func (a AccessLog) MarshalJSON() ([]byte, error) {
	type Alias AccessLog
	return json.Marshal(&struct {
		Cost         string `json:"cost"`         // 添加一个同名字段来覆盖原始的Cost字段
		RequestTime  string `json:"requestTime"`  // 请求时间
		ResponseTime string `json:"responseTime"` // 响应时间
		*Alias
	}{
		Cost:         fmt.Sprintf("%dms", a.Cost.Milliseconds()), // 转换Cost为毫秒
		RequestTime:  a.RequestTime.Format("2006-01-02 15:04:05"),
		ResponseTime: a.ResponseTime.Format("2006-01-02 15:04:05"),
		Alias:        (*Alias)(&a), // 别名，用来避免递归调用MarshalJSON
	})
}

// ResponseWriterWrapper 自定义ResponseWriter，用于捕获响应体
type ResponseWriterWrapper struct {
	http.ResponseWriter
	buffer     bytes.Buffer
	statusCode int
}

func (w *ResponseWriterWrapper) Header() http.Header {
	return w.ResponseWriter.Header()
}

func (w *ResponseWriterWrapper) Write(data []byte) (int, error) {
	n, err := w.ResponseWriter.Write(data)
	if err == nil {
		w.buffer.Write(data)
	}
	return n, err
}

func (w *ResponseWriterWrapper) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

type Filter struct {
}

func NewFilter() *Filter {
	return &Filter{}
}

func (f *Filter) Order() int {
	return 0
}

func (f *Filter) String() string {
	return "accesslog"
}

func (f *Filter) DoFilter(w http.ResponseWriter, r *http.Request, chain web.FilterChain) {
	uri := r.RequestURI

	// 忽略的静态资源
	for _, resource := range ignoreStaticResource {
		if strings.HasSuffix(uri, resource) {
			chain.DoFilter(w, r)
			return
		}
	}

	// 忽略的path
	for _, path := range ignorePath {
		if strings.HasSuffix(uri, path) {
			chain.DoFilter(w, r)
			return
		}
	}

	start := time.Now()

	// 获取requestId
	requestId, r := util.GetRequestId(r)

	// 记录请求日志对象
	ac := AccessLog{
		Method:      r.Method,
		Uri:         uri,
		ClientIP:    util.GetIPAddress(r),
		RequestTime: start,
	}

	// 获取请求参数
	var parameters string
	if r.Method != http.MethodGet {
		parameters = getRequestBody(r)
	} else {
		parameters = r.URL.RawQuery
	}
	if len(parameters) > MaxLength {
		parameters = parameters[:MaxLength] + "..."
	}
	ac.Parameters = parameters

	// 包装response, 可重复读
	lrw := &ResponseWriterWrapper{
		ResponseWriter: w,
	}
	chain.DoFilter(lrw, r)

	// 设置响应体
	ac.Response = lrw.buffer.String()
	ac.HttpStatus = lrw.statusCode
	ac.Success = lrw.statusCode == SuccessCode
	// 如果响应格式为json的，记录响应内容
	//if strings.Contains(lrw.Header().Get("Content-Type"), "application/json") {
	//
	//}

	// 设置耗时
	end := time.Now()
	ac.ResponseTime = end
	ac.Cost = end.Sub(start)

	// 打印access log
	acl, err := json.Marshal(ac)
	if err != nil {
		log.Error("[%s]marshal access log error: %v", requestId, err)
		return
	}
	log.Info("[%s]access log: %s", requestId, acl)

}

func getRequestBody(r *http.Request) string {
	// 读取请求体
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Error("Error reading body: %v", err)
		return ""
	}
	// 确保请求体可以被下一个处理程序读取
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	return string(bodyBytes)
}
