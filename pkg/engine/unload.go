package engine

import (
	"dip-agent/pkg/util"
	"github.com/pkg/errors"
	"time"
)

const (
	unloadIndexPath = "/ApiService/UnloadIndex"
)

type UnloadIndexRequest struct {
	IndexName    string `json:"index_name"`    // 必填。索引名称
	IndexVersion string `json:"index_version"` // 必填。索引版本

	requestId  string
	httpClient *util.HttpClient
}

func NewUnloadIndexRequest(requestId, indexName, indexVersion string) *UnloadIndexRequest {
	hc := util.NewHttpClient(5 * time.Minute)
	hc.SetClue(requestId)
	return &UnloadIndexRequest{
		IndexName:    indexName,
		IndexVersion: indexVersion,

		requestId:  requestId,
		httpClient: hc,
	}
}

func (r *UnloadIndexRequest) Send() (*EmptyResponse, error) {
	url := Domain() + unloadIndexPath
	var response EmptyResponse
	err := r.httpClient.Post(url, r, nil, &response)
	if err != nil {
		err = errors.WithMessagef(err, "call engine api fail: %s", unloadIndexPath)
	} else if !response.Header.IsSuccess() {
		err = errors.Errorf("call engine api fail: %s. reason: %s", unloadIndexPath, response.Header.ErrMsg())
	}
	return &response, err
}
