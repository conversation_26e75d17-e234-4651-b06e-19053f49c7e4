package engine

import (
	"bytes"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"os/exec"
	"time"
)

var DefaultServiceInfo ServiceInfo

// InitFormEnv 从环境变量中初始化引擎服务信息
// TODO 需要提前在镜像的配置中设置环境变量
func InitFormEnv(env ServiceEnvConfigKey) {
	port := os.Getenv(env.ServicePort)
	if port == "" {
		port = "9109"
	}
	host := os.Getenv(env.ServiceHost)
	if host == "" {
		host = "localhost"
	}
	DefaultServiceInfo = ServiceInfo{
		Name: os.Getenv(env.ServiceName),
		Host: host,
		Port: port,
	}
}

func Host() string {
	return DefaultServiceInfo.Host
}

func Port() string {
	return DefaultServiceInfo.Port
}

func Domain() string {
	return "http://" + DefaultServiceInfo.Host + ":" + DefaultServiceInfo.Port
}

type ServiceInfo struct {
	Name string
	Host string
	Port string

	// 进程号
	Pid int
	// 服务启动时间
	StartTime time.Time
	// 服务运行时间
	Uptime time.Duration
}

type ServiceEnvConfigKey struct {
	// 服务名称
	ServiceName string `yaml:"serviceName" default:"APP_NAME"`
	// 服务监听端口
	ServicePort string `yaml:"servicePort" default:"SEARCH_SRV_PORT"`
	// 服务监听地址
	ServiceHost string `yaml:"serviceHost" default:"SEARCH_SRV_HOST"`
	// 引擎基础路径, 对应原有的SEARCH_REL, 目前实际路径为/app/rel
	EngineBasePath string `yaml:"engineBasePath" default:"SEARCH_REL"`
}

type EmptyResponse struct {
	Header BaseResponse `json:"header"`
}

func (r *EmptyResponse) IsSuccess() bool {
	return r.Header.IsSuccess()
}

func (r *EmptyResponse) ToApiResponse() *api.Response[string] {
	ar := &api.Response[string]{
		Code:            r.Header.Code,
		ResponseMessage: r.Header.Message,
		ResponseData:    "",
		ErrorDetails:    r.Header.ErrorDetails,
	}
	ar.SetStatus(api.NewStatusFromCode(r.Header.Code))
	return ar
}

type BaseResponse struct {
	Code         api.Code          `json:"code"`
	Message      string            `json:"message,omitempty"`
	ErrorDetails *api.ErrorDetails `json:"error_details,omitempty"`
}

func (r BaseResponse) IsSuccess() bool {
	return r.Code.IsSuccess()
}

func (r BaseResponse) ErrMsg() string {
	errDetails := ""
	if r.ErrorDetails != nil {
		errDetails = r.ErrorDetails.Error()
	}
	return fmt.Sprintf("message: %s, error_details: %s", r.Message, errDetails)
}

var (
	defaultDgraphEngineVersionCmdPath  = "/app/rel/bin/doe-console"
	defaultDgraphEngineVersionCmdArgs  = []string{"binary-version"}
	defaultDsearchEngineVersionCmdPath = "/app/rel/bin/doe-server"
	defaultDsearchEngineVersionCmdArgs = []string{"-v"}
)

// InitEngineBinaryVersions 获取引擎的二进制版本
func InitEngineBinaryVersions() (map[string]int, error) {
	return GetEngineBinaryVersions("init-engine-binary-versions", "", nil, "", nil)
}

// GetEngineBinaryVersions 获取引擎的二进制版本
func GetEngineBinaryVersions(requestId string, dgraphEngineVersionCmdPath string, dgraphEngineVersionCmdArgs []string, dsearchEngineVersionCmdPath string, dsearchEngineVersionCmdArgs []string) (map[string]int, error) {
	var (
		err                  error
		engineVersionCmdPath string
		engineVersionCmdArgs []string
	)

	if dgraphEngineVersionCmdPath == "" {
		dgraphEngineVersionCmdPath = defaultDgraphEngineVersionCmdPath
	}
	if len(dgraphEngineVersionCmdArgs) == 0 {
		dgraphEngineVersionCmdArgs = defaultDgraphEngineVersionCmdArgs
	}
	if dsearchEngineVersionCmdPath == "" {
		dsearchEngineVersionCmdPath = defaultDsearchEngineVersionCmdPath
	}
	if len(dsearchEngineVersionCmdArgs) == 0 {
		dsearchEngineVersionCmdArgs = defaultDsearchEngineVersionCmdArgs
	}

	// 优先判断doe的cmd是否存在
	if util.IsPathExist(dgraphEngineVersionCmdPath) {
		engineVersionCmdPath = dgraphEngineVersionCmdPath
		engineVersionCmdArgs = dgraphEngineVersionCmdArgs
	} else {
		engineVersionCmdPath = dsearchEngineVersionCmdPath
		engineVersionCmdArgs = dsearchEngineVersionCmdArgs
	}

	// 判断是否存在
	if !util.IsPathExist(engineVersionCmdPath) {
		return nil, errors.Errorf("engine version command not found: %s", engineVersionCmdPath)
	}

	cmd := exec.Command(engineVersionCmdPath, engineVersionCmdArgs...)
	log.Info("[%s]execute engine version command: %s", requestId, cmd.String())

	// 复制当前环境变量到新的进程
	cmd.Env = os.Environ()

	// 执行命令并获取输出
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	err = cmd.Run()
	//output, err := cmd.CombinedOutput()
	output := stdout.String()
	if err != nil {
		return nil, errors.Wrapf(err, "command(%s) execution fail. stdout: %s; stderr: %s", cmd.String(), output, stderr.String())
	}
	log.Info("[%s]engine version: %s", requestId, output)
	if stderr.Len() > 0 {
		log.Warn("[%s]engine version stderr: %s", requestId, stderr.String())
	}

	// 解析
	var versions map[string]int
	err = json.Unmarshal([]byte(output), &versions)
	if err != nil {
		return nil, errors.Wrapf(err, "parse engine version fail: %s", output)
	}

	return versions, nil
}
