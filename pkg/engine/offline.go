package engine

import (
	"dip-agent/pkg/util"
	"github.com/pkg/errors"
	"time"
)

const (
	offlinePath = "/ApiService/Offline"
)

type OfflineRequest struct {
	requestId  string
	httpClient *util.HttpClient
}

func NewOfflineRequest(requestId string) *OfflineRequest {
	hc := util.NewHttpClient(30 * time.Second)
	hc.SetClue(requestId)
	return &OfflineRequest{
		requestId:  requestId,
		httpClient: hc,
	}
}

func (r *OfflineRequest) Send() (*EmptyResponse, error) {
	url := Domain() + offlinePath
	var response EmptyResponse
	err := r.httpClient.Post(url, r, nil, &response)
	if err != nil {
		err = errors.WithMessagef(err, "call engine api fail: %s", offlinePath)
	} else if !response.Header.IsSuccess() {
		err = errors.Errorf("call engine api fail: %s. reason: %s", offlinePath, response.Header.ErrMsg())
	}
	return &response, err
}
