package engine

// BuildEngineResponse 构建引擎完成后的响应结构
type BuildEngineResponse struct {
	Header      *BaseResponse `json:"header"`
	BuildResult *BuildResult  `json:"build_result"`
}

type BuildResult struct {
	IndexName           string            `json:"index_name,omitempty"`    // 索引名称
	IndexVersion        string            `json:"index_version,omitempty"` // 索引版本
	BuildVersion        string            `json:"build_version"`           // 构建版本
	IndexTotalBytes     int64             `json:"index_total_bytes"`       // 构建的索引大小, 必填
	IndexMemoryBytes    int64             `json:"index_memory_bytes"`      // 索引在内存中的大小
	IndexTotalCount     int64             `json:"index_total_count"`       // 构建的索引文档数, 必填. 离线构建的文档数量=index_total_count - inc_doc_count
	IncDocCount         int64             `json:"inc_doc_count"`           // 追增量的文档数量
	FailDocCount        int64             `json:"fail_doc_count"`          // 失败的文档数
	BuildCostMs         int64             `json:"build_cost_ms"`           // 构建耗时, 单位: 毫秒, 必填
	EngineBinaryVersion string            `json:"engine_binary_version"`   // 构建的引擎二进制版本, 必填
	ExtraInfo           map[string]string `json:"extra_info,omitempty"`    // 额外信息
}

// BuildIndexRequest build-engine的请求
// json格式是下划线
type BuildIndexRequest struct {
	IndexName    string    `json:"index_name"`    // 索引名称
	IndexVersion string    `json:"index_version"` // 索引版本
	DataPath     string    `json:"data_path"`     // 构建的源表数据路径
	LogPath      string    `json:"log_path"`      // 构建的日志输出路径
	DBPath       string    `json:"db_path"`       // 构建的索引输出路径
	MetadataPath string    `json:"metadata_path"` // 元数据文件路径
	SchemaPath   string    `json:"schema_path"`   // schema文件路径
	ShardNum     int32     `json:"shard_num"`     // 分片数量
	ShardID      int32     `json:"shard_id"`      // 分片号
	MaxFailRate  int32     `json:"max_fail_rate"` // 最大失败比例，默认10，表示10%
	IndexDef     *IndexDef `json:"index_def"`     // 索引定义
	FileCount    int       `json:"file_count"`    // 文件数量
	DocNum       int64     `json:"doc_num"`       // 文档数量

	// 目前未使用
	SegmentNum       int32   `json:"segment_num"`        // segment数量
	SegmentFileCount []int32 `json:"segment_file_count"` // 每个分段的数据文件数量, 第i个表示第i段中的数据文件数量
}

type ModelBuildResult struct {
	ModelName           string            `json:"model_name"`            // 模型名称, 必填
	ModelVersion        string            `json:"model_version"`         // 模型版本, 必填
	ModelBytes          int64             `json:"model_bytes"`           // 模型大小, 必填
	ModelType           string            `json:"model_type"`            // 模型类型, 必填
	BuildCostMs         int64             `json:"build_cost_ms"`         // 构建耗时, 单位: 毫秒
	EngineBinaryVersion string            `json:"engine_binary_version"` // 构建的引擎二进制版本
	ExtraInfo           map[string]string `json:"extra_info,omitempty"`  // 额外信息
}
