package engine

import (
	"dip-agent/pkg/util"
	"github.com/pkg/errors"
	"time"
)

// WARNING: 目前只有Dgraph有此接口
const (
	unloadIndexCheckPath = "/ApiService/CheckUnload"
)

type UnloadIndexCheckRequest struct {
	IndexName    string `json:"index_name"`    // 必填。索引名称
	IndexVersion string `json:"index_version"` // 必填。索引版本

	requestId  string
	httpClient *util.HttpClient
}

type UnloadIndexCheckResponse struct {
	Header     BaseResponse `json:"header"`      // 必填。基础响应结构
	IsUnloaded *bool        `json:"is_unloaded"` // 必填。是否已经卸载完成
}

func NewUnloadIndexCheckRequest(requestId, indexName, indexVersion string) *UnloadIndexCheckRequest {
	hc := util.NewHttpClient(5 * time.Second)
	hc.SetClue(requestId)
	return &UnloadIndexCheckRequest{
		IndexName:    indexName,
		IndexVersion: indexVersion,

		requestId:  requestId,
		httpClient: hc,
	}
}

func (r *UnloadIndexCheckRequest) Send() (*UnloadIndexCheckResponse, error) {
	url := Domain() + unloadIndexCheckPath
	var response UnloadIndexCheckResponse
	err := r.httpClient.Post(url, r, nil, &response)
	if err != nil {
		err = errors.WithMessagef(err, "call engine api fail: %s", unloadIndexCheckPath)
	} else if !response.Header.IsSuccess() {
		err = errors.Errorf("call engine api fail: %s. reason: %s", unloadIndexCheckPath, response.Header.ErrMsg())
	}
	return &response, err
}