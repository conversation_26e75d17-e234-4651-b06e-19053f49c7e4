package engine

import (
	"dip-agent/pkg/util"
	"github.com/pkg/errors"
	"time"
)

const (
	serviceStatusPath = "/ApiService/ServiceStatus"
)

type ServiceStatusRequest struct {
	requestId  string
	httpClient *util.HttpClient
}

type ServicerStatusResponse struct {
	Header        BaseResponse   `json:"header"`         // 必填。基础响应结构
	ServiceStatus *ServiceStatus `json:"service_status"` // 必填。服务状态
}

// ServiceStatus 服务状态
type ServiceStatus struct {
	Online              *bool             `json:"online"`              // 必填. 服务是否在线
	EngineBinaryVersion string            `json:"engineBinaryVersion"` // 必填. 引擎二进制版本
	EngineBuildVersion  string            `json:"engineBuildVersion"`  // 引擎构建版本. 例如 git commit id
	ExtraInfo           *ServiceExtraInfo `json:"extra_info"`          // 额外信息
	Properties          map[string]string `json:"properties"`          // 服务属性
}

// ServiceExtraInfo 额外信息
type ServiceExtraInfo struct {
	KvBinaryVersion      string `json:"kv_binary_version"`       // kv二进制版本
	KvvBinaryVersion     string `json:"kvv_binary_version"`      // kvv二进制版本
	InvertBinaryVersion  string `json:"invert_binary_version"`   // invert二进制版本
	DiskKvBinaryVersion  string `json:"disk_kv_binary_version"`  // disk_kv二进制版本
	DenseKvBinaryVersion string `json:"dense_kv_binary_version"` // dense_kv二进制版本
}

func NewServiceStatusRequest(requestId string) *ServiceStatusRequest {
	hc := util.NewHttpClient(30 * time.Second)
	hc.SetClue(requestId)
	return &ServiceStatusRequest{
		requestId:  requestId,
		httpClient: hc,
	}
}

func NewServiceStatusRequestWithTimeout(requestId string, timeout time.Duration) *ServiceStatusRequest {
	hc := util.NewHttpClient(timeout)
	hc.SetClue(requestId)
	return &ServiceStatusRequest{
		requestId:  requestId,
		httpClient: hc,
	}
}

func (r *ServiceStatusRequest) Send() (*ServicerStatusResponse, error) {
	url := Domain() + serviceStatusPath
	var response ServicerStatusResponse
	err := r.httpClient.Post(url, r, nil, &response)
	if err != nil {
		err = errors.WithMessagef(err, "call engine api fail: %s", serviceStatusPath)
	} else if !response.Header.IsSuccess() {
		err = errors.Errorf("call engine api fail: %s. reason: %s", serviceStatusPath, response.Header.ErrMsg())
	}
	return &response, err
}
