package include

import (
	_ "dip-agent/pkg/controller/generalize"
	_ "dip-agent/pkg/controller/mock"
	_ "dip-agent/pkg/controller/ops/index"
	_ "dip-agent/pkg/controller/platform"
	_ "dip-agent/pkg/controller/platform/agent"
	_ "dip-agent/pkg/controller/platform/batch"
	_ "dip-agent/pkg/controller/platform/engine"
	_ "dip-agent/pkg/controller/platform/index"
	_ "dip-agent/pkg/controller/platform/model"
	_ "dip-agent/pkg/controller/platform/ops"
	_ "dip-agent/pkg/controller/system"
	_ "dip-agent/pkg/controller/tool"
	_ "dip-agent/pkg/filter/accesslog"
	_ "dip-agent/pkg/intercept/batch/report"
	_ "dip-agent/pkg/intercept/chaos"
	_ "dip-agent/pkg/intercept/cost"
	_ "dip-agent/pkg/intercept/exception"
	_ "dip-agent/pkg/intercept/pause"
	_ "dip-agent/pkg/intercept/report"
	_ "dip-agent/pkg/intercept/skip"
	_ "dip-agent/pkg/intercept/stop"
	_ "dip-agent/pkg/persistence/dirve"
	_ "dip-agent/pkg/process/batch/check"
	_ "dip-agent/pkg/process/batch/check/qps"
	_ "dip-agent/pkg/process/batch/commit"
	_ "dip-agent/pkg/process/batch/deleteindex"
	_ "dip-agent/pkg/process/batch/downloadindex"
	_ "dip-agent/pkg/process/batch/end"
	_ "dip-agent/pkg/process/batch/hotreload"
	_ "dip-agent/pkg/process/batch/rmdb"
	_ "dip-agent/pkg/process/batch/start"
	_ "dip-agent/pkg/process/batch/unloadindex"
	_ "dip-agent/pkg/process/buildindex"
	_ "dip-agent/pkg/process/check/index"
	_ "dip-agent/pkg/process/check/index/qps"
	_ "dip-agent/pkg/process/check/model"
	_ "dip-agent/pkg/process/commit/index"
	_ "dip-agent/pkg/process/commit/model"
	_ "dip-agent/pkg/process/deleteindex"
	_ "dip-agent/pkg/process/download/index"
	_ "dip-agent/pkg/process/download/model"
	_ "dip-agent/pkg/process/download/model/config"
	_ "dip-agent/pkg/process/dump"
	_ "dip-agent/pkg/process/echo"
	_ "dip-agent/pkg/process/hotreload/index"
	_ "dip-agent/pkg/process/hotreload/model"
	_ "dip-agent/pkg/process/offline"
	_ "dip-agent/pkg/process/online"
	_ "dip-agent/pkg/process/qps"
	_ "dip-agent/pkg/process/rmdb/index"
	_ "dip-agent/pkg/process/rmdb/model"
	_ "dip-agent/pkg/process/schema"
	_ "dip-agent/pkg/process/sleep"
	_ "dip-agent/pkg/process/startserver"
	_ "dip-agent/pkg/process/stopserver"
	_ "dip-agent/pkg/process/unloadindex"
	_ "dip-agent/pkg/process/uploadindex"
	_ "dip-agent/pkg/process/waitinc"
	_ "dip-agent/pkg/util/json/jsoniter"
	_ "dip-agent/pkg/util/json/sonic"
	_ "dip-agent/pkg/util/json/std"
)
