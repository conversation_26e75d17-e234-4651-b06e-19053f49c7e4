package end

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
	"strings"
)

const (
	TypeName = "end-index-update"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type PartialSuccessResult struct {
	SuccessIndexNames string `json:"successIndexNames"`
	FailIndexNames    string `json:"failIndexNames"`
}

type ProcessRequest struct {
	Tasks    []*dto.UpdateIndexInfo `json:"tasks,omitempty" validate:"required,dive"`
	IsReboot *bool                  `json:"isReboot,omitempty" default:"false"` // 是否重启更新
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}

	var (
		// 更新成功的索引名称
		successIndexNames []string
		// 更新失败的索引名称
		failIndexNames []string
	)

	for _, task := range pr.Tasks {
		if task.CanUpdate {
			successIndexNames = append(successIndexNames, task.IndexName)
		} else {
			failIndexNames = append(failIndexNames, task.IndexName)
		}
	}

	// 全部失败
	if len(failIndexNames) == len(pr.Tasks) {
		return api.UpdateFail[string](errors.New("all index update fail"))
	}

	// 部分失败
	if len(failIndexNames) > 0 {
		// error details
		partialSuccessResult := PartialSuccessResult{
			SuccessIndexNames: strings.Join(successIndexNames, ","),
			FailIndexNames:    strings.Join(failIndexNames, ","),
		}
		jsonContent, _ := json.MarshalToString(partialSuccessResult)
		errorDetails := &api.ErrorDetails{
			ErrorType:       api.UpdateException_BatchUpdatePartialFailed,
			DetailedMessage: jsonContent,
		}
		// response
		partialSuccess := api.OfSuccessWithMessage[string]("batch update index partial success", jsonContent)
		partialSuccess.ErrorDetails = errorDetails
		return partialSuccess
	}

	return api.OfSuccess[string]("batch update index success")
}
