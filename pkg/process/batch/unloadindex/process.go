package unloadindex

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/retry"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"time"
)

const (
	TypeName = "batch-unload-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	HasUnloadIndex   bool     `json:"hasUnloadIndex,omitempty"`   // 是否有卸载索引
	UnloadIndex      []string `json:"unloadIndex,omitempty"`      // 卸载的索引
	RemovedIndexPath []string `json:"removedIndexPath,omitempty"` // 删除的索引路径
}

type ProcessRequest struct {
	Tasks []*dto.UpdateIndexInfo `json:"tasks" validate:"required,min=1,dive"` // 必填. 任务列表
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	log.Info("[%s]engine unload index request: %+v", ctx.RequestId, pr)

	rmi := &ResultMessageInfo{}
	rmi.UnloadIndex = make([]string, 0)
	rmi.RemovedIndexPath = make([]string, 0)

	// 调用unload接口卸载索引, 并清理本地过期索引目录
	err = runTask(ctx.RequestId, pr, rmi)
	if err != nil {
		err = errors.WithMessage(err, "unload index fail")
		return api.UpdateFail[string](err)
	}

	// 只有推荐需要sleep, commit也是只有推荐需要调用
	needCommit := pr.Tasks[0].NeedCommit != nil && *pr.Tasks[0].NeedCommit
	if rmi.HasUnloadIndex && needCommit {
		// 等待索引的保护时间结束(索引attach以及延迟释放)
		sleepTimeAfterUnload := pr.Tasks[0].SleepTimeAfterUnload
		log.Info("[%s]wait for index protection time end: sleep %s", ctx.RequestId, sleepTimeAfterUnload.String())
		time.Sleep(sleepTimeAfterUnload)
	}

	resultMessageContent, _ := json.Marshal(rmi)
	return api.OfSuccessWithMessage("engine unload index success", string(resultMessageContent))
}

// 获取需要卸载的索引
func runTask(requestId string, pr *ProcessRequest, rmi *ResultMessageInfo) error {
	var err error
	tasks := pr.Tasks
	for _, indexInfo := range tasks {
		if !indexInfo.CanUpdate {
			continue
		}

		// 本地索引路径
		localIndexDbPath := filepath.Join(indexInfo.DbPath, indexInfo.IndexName)

		log.Info("[%s][%s]serving versions: %v", requestId, indexInfo.IndexName, indexInfo.ServingVersions)
		if *indexInfo.IsDoubleVersion {
			// 获取索引当前存在的版本: 从本地dbPath中scan出所有的索引版本
			if util.IsPathExist(localIndexDbPath) {
				// 读取localIndexDbPath目录下的所有文件夹, 作为索引版本
				localVersions, err := util.GetSubdirectories(localIndexDbPath)
				if err != nil {
					err = errors.Wrapf(err, "read local index db path fail: %s", localIndexDbPath)
					return err
				}
				log.Info("[%s][%s]local versions: %v", requestId, indexInfo.IndexName, localVersions)
				if len(localVersions) > 0 {
					// 调用引擎接口卸载所有的索引版本
					for _, version := range localVersions {
						err = unloadIndex(requestId, indexInfo.IndexName, indexInfo.IndexVersion)
						if err != nil {
							err = errors.WithMessagef(err, "engine unload index fail, index: %s(%s)", indexInfo.IndexName, version)
							return err
						}

						// 校验索引是否完成卸载
						_, err := common.CheckIndexUnload(requestId, indexInfo.IndexName, indexInfo.IndexVersion, indexInfo.UnloadCheckTimeout, indexInfo.UnloadCheckWaitTime)
						if err != nil {
							return err
						}
						log.Info("[%s]index unload success: %s(%s)", requestId, indexInfo.IndexName, indexInfo.IndexVersion)

						rmi.HasUnloadIndex = true
						rmi.UnloadIndex = append(rmi.UnloadIndex, fmt.Sprintf("%s(%s)", indexInfo.IndexName, version))
					}

					// 删除过期的本地版本
					outdatedVersion, err := util.GetOutdatedVersionWithServingVersions(indexInfo.DbPath, indexInfo.IndexName, indexInfo.ServingVersions)
					if err != nil {
						err = errors.WithMessage(err, "remove outdated index version fail")
						return err
					}
					log.Info("[%s][%s]outdated versions: %v", requestId, indexInfo.IndexName, outdatedVersion)
					if len(outdatedVersion) > 0 {
						for _, version := range outdatedVersion {
							// 删除本地索引目录
							outdatedIndexVersionDir := filepath.Join(localIndexDbPath, version)
							err = os.RemoveAll(outdatedIndexVersionDir)
							if err != nil {
								err = errors.Wrapf(err, "remove outdated index db path fail: %s", outdatedIndexVersionDir)
								return err
							}
							log.Info("[%s]remove outdated index version dir: %s", requestId, outdatedIndexVersionDir)
							rmi.RemovedIndexPath = append(rmi.RemovedIndexPath, outdatedIndexVersionDir)
						}
					}
				}
			}
		} else {
			err = unloadIndex(requestId, indexInfo.IndexName, indexInfo.IndexVersion)
			if err != nil {
				err = errors.WithMessagef(err, "engine unload index fail, index: %s(%s)", indexInfo.IndexName, indexInfo.IndexVersion)
				return err
			}

			// 校验索引是否完成卸载
			_, err := common.CheckIndexUnload(requestId, indexInfo.IndexName, indexInfo.IndexVersion, indexInfo.UnloadCheckTimeout, indexInfo.UnloadCheckWaitTime)
			if err != nil {
				return err
			}
			log.Info("[%s]index unload success: %s(%s)", requestId, indexInfo.IndexName, indexInfo.IndexVersion)

			rmi.HasUnloadIndex = true
			rmi.UnloadIndex = append(rmi.UnloadIndex, fmt.Sprintf("%s(%s)", indexInfo.IndexName, indexInfo.IndexVersion))

			// 删除本地gz目录
			localIndexGzPath := filepath.Join(indexInfo.GzPath, indexInfo.IndexName)
			err = os.RemoveAll(localIndexGzPath)
			if err != nil {
				err = errors.Wrapf(err, "remove local index gz path fail: %s", localIndexGzPath)
				return err
			}
			log.Info("[%s]remove local index gz dir: %s", requestId, localIndexGzPath)
			// 删除本地索引目录
			err = os.RemoveAll(localIndexDbPath)
			if err != nil {
				err = errors.Wrapf(err, "remove local index db path fail: %s", localIndexDbPath)
				return err
			}
			log.Info("[%s]remove local index db dir: %s", requestId, localIndexDbPath)
			rmi.RemovedIndexPath = append(rmi.RemovedIndexPath, localIndexDbPath)
		}
	}
	return nil
}

// 调用引起接口unload索引
func unloadIndex(requestId, indexName, indexVersion string) error {
	err := retry.DoRetry(func() error {
		_, err := engine.NewUnloadIndexRequest(requestId, indexName, indexVersion).Send()
		return err
	})
	if err != nil {
		err = errors.WithMessagef(err, "engine unload index fail, index: %s(%s)", indexName, indexVersion)
		return err
	}
	return nil
}
