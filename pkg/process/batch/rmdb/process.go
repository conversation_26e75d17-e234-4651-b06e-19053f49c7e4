package rmdb

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/rmdb/index"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
	"strings"
)

const (
	TypeName = "batch-rmdb"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	Tasks []*dto.UpdateIndexInfo `json:"tasks,omitempty" validate:"required,dive"`
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	context, _ := json.MarshalToString(pr)
	log.Info("[%s]remove index db request: %s", ctx.RequestId, context)

	// default param
	isRollback := false

	removedIndexNames := make([]string, 0)
	for _, task := range pr.Tasks {
		if !task.CanUpdate {
			continue
		}

		ipr := &index.ProcessRequest{
			IndexName:            task.IndexName,
			DbPath:               task.DbPath,
			ServingVersions:      task.ServingVersions,
			DiskUsageThreshold:   task.DiskUsageThreshold,
			BackupDiskUsageRatio: 0, // 批量更新不备份
			BackupDir:            task.BackupDir,
			IsRollback:           &isRollback,
		}
		// 参数校验
		err = cfg.SetDefaultAndValidate(ipr)
		if err != nil {
			return api.UpdateFail[string](errors.WithMessagef(err, "validate remove index request fail: %s", ipr.IndexName))
		}

		err := index.RunTask(ctx.RequestId, ipr)
		if err != nil {
			return api.UpdateFail[string](errors.WithMessage(err, "unload outdated index failed"))
		}

		removedIndexNames = append(removedIndexNames, task.IndexName)
	}

	if len(removedIndexNames) > 0 {
		log.Info("[%s]remove index db success: %s", ctx.RequestId, strings.Join(removedIndexNames, ","))
	}

	return api.OfSuccess[string]("remove index db dir success")
}
