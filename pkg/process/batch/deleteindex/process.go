package deleteindex

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/deleteindex"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
)

const (
	TypeName = "batch-delete-index" // 卸载索引并删除本地索引相关的目录
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	UnloadIndex      []string `json:"unloadIndex,omitempty"`      // 卸载的索引
	RemovedIndexPath []string `json:"removedIndexPath,omitempty"` // 删除的索引路径
}

type ProcessRequest struct {
	Tasks []*dto.DeleteIndexInfo `json:"tasks" validate:"required,min=1,dive"` // 必填. 任务列表
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	requestContent, _ := json.Marshal(pr)
	log.Info("[%s]engine delete index request: %s", ctx.RequestId, requestContent)

	rmi := &ResultMessageInfo{}
	rmi.UnloadIndex = make([]string, 0)
	rmi.RemovedIndexPath = make([]string, 0)

	for _, task := range pr.Tasks {
		deleteIndexRequest := &deleteindex.ProcessRequest{
			IndexName:           task.IndexName,
			IsDoubleVersion:     task.IsDoubleVersion,
			DbPath:              task.DbPath,
			GzPath:              task.GzPath,
			NeedCommit:          task.NeedCommit,
			UnloadCheckWaitTime: task.UnloadCheckWaitTime,
			UnloadCheckTimeout:  task.UnloadCheckTimeout,
		}
		// 参数校验
		err = cfg.SetDefaultAndValidate(deleteIndexRequest)
		if err != nil {
			return api.UpdateFail[string](errors.WithMessage(err, "invalid delete index param: deleteIndexRequest"))
		}
		tRmi, err := deleteindex.RunTask(ctx.RequestId, deleteIndexRequest)
		if err != nil {
			return api.UpdateFail[string](errors.WithMessage(err, "batch delete index fail"))
		}
		rmi.UnloadIndex = append(rmi.UnloadIndex, tRmi.UnloadIndex...)
		rmi.RemovedIndexPath = append(rmi.RemovedIndexPath, tRmi.RemovedIndexPath...)
	}

	resultMessageContent, _ := json.Marshal(rmi)
	return api.OfSuccessWithMessage("engine delete index success", string(resultMessageContent))
}
