package qps

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/check/index/qps"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
)

const (
	TypeName = "batch-check-index-qps"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	Tasks    []*dto.DeleteIndexInfo `json:"tasks" validate:"required,min=1,dive"` // 必填. 任务列表
	IsReboot *bool                  `json:"isReboot,omitempty" default:"false"`   // 是否重启更新
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	requestContent, _ := json.MarshalToString(pr)
	log.Info("[%s]batch check delete index qps request: %s", ctx.RequestId, requestContent)

	// 校验索引是否存在流量
	for _, task := range pr.Tasks {
		qpsCheckRequest := &qps.ProcessRequest{
			IndexName:          task.IndexName,
			QpsCheckRetryCount: task.QpsCheckRetryCount,
			QpsCheckRetryWait:  task.QpsCheckRetryWait,
		}
		err = qps.RunTask(ctx.RequestId, qpsCheckRequest)
		if err != nil {
			return api.UpdateFail[string](err)
		}
	}

	return api.OfSuccess("batch check delete index qps success")
}
