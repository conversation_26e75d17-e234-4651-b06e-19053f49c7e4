package start

import (
	"dip-agent/pkg/consts"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"github.com/pkg/errors"
)

const (
	TypeName = "begin-index-update"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	Tasks    []*dto.UpdateIndexInfo `json:"tasks,omitempty" validate:"required,dive"`
	IsReboot *bool                  `json:"isReboot,omitempty" default:"false"` // 是否重启更新
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}

	// note: 无实际业务逻辑, 仅仅表示开始处理批量更新任务
	// context中塞入需要上报的索引信息
	ctx.Carrier.Put(consts.ReportIndexInfo, pr.Tasks)

	return api.OfSuccess[string]("start batch update index success")
}
