package waitinc

import (
	"dip-agent/pkg/consts"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/retry"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"time"
)

const (
	TypeName = "batch-commit"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	Tasks []*dto.UpdateIndexInfo `json:"tasks" validate:"required,min=1,dive"` // 必填. 任务列表
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	log.Info("[%s]engine index commit request: %+v", ctx.RequestId, pr)

	tasks := pr.Tasks
	for _, task := range tasks {
		if !task.CanUpdate {
			continue
		}
		if task.ReportExt == nil {
			task.ReportExt = make(map[string]interface{})
		}
		task.ReportExt[consts.ReportStartTime] = time.Now()
		err = RunTask(ctx.RequestId, ctx.TaskId, task)
		task.ReportExt[consts.ReportEndTime] = time.Now()
		if err != nil {
			return api.UpdateFail[string](err)
		}
	}

	return api.OfSuccess("engine commit success")
}

func RunTask(requestId, taskId string, task *dto.UpdateIndexInfo) error {
	needCommit := task.NeedCommit != nil && *task.NeedCommit
	if needCommit {
		// 调用commit接口
		err := retry.DoRetry(func() error {
			_, err := engine.NewCommitRequest(requestId, task.IndexName, task.IndexVersion).Send()
			return err
		})
		if err != nil {
			return err
		}
	}

	// 清除索引的老版本
	var servingVersions []string
	if task.IsDoubleVersion != nil && *task.IsDoubleVersion {
		servingVersions = task.ServingVersions
	} else {
		servingVersions = []string{task.IndexVersion}
	}
	log.Info("[%s]remove outdated index version. keep servingVersions: %v", requestId, servingVersions)
	indexDbDir := filepath.Join(task.DbPath, task.IndexName)
	// 读取localIndexDbPath目录下的所有文件夹, 作为索引版本
	localVersions, err := util.GetSubdirectories(indexDbDir)
	if err != nil {
		err = errors.WithMessagef(err, "read local index db path fail. indexDbDir: %s", indexDbDir)
		return err
	}
	for _, localVersion := range localVersions {
		if !util.ContainsString(servingVersions, localVersion) {
			localIndexDbPath := filepath.Join(indexDbDir, localVersion)
			err = os.RemoveAll(localIndexDbPath)
			if err != nil && !os.IsNotExist(err) {
				log.Warn("[%s]remove outdated index version fail: %s", requestId, localIndexDbPath)
			} else {
				log.Info("[%s]remove outdated index version success: %s", requestId, localIndexDbPath)
			}
		}
	}

	return nil
}
