package qps

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/retry"
	"time"

	"github.com/pkg/errors"
)

const (
	TypeName = "check-index-qps"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	IndexName          string        `json:"indexName,omitempty" validate:"required"` // 必填。索引名称
	QpsCheckRetryCount int           `json:"qpsCheckRetryCount" default:"3"`          // QPS检查重试次数
	QpsCheckRetryWait  time.Duration `json:"qpsCheckRetryWait" default:"5s"`          // QPS检查重试间隔
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	requestContent, _ := json.Marshal(pr)
	log.Info("[%s]check delete index qps request: %s", ctx.RequestId, requestContent)

	// 校验索引是否存在流量
	err = RunTask(ctx.RequestId, pr)
	if err != nil {
		return api.UpdateFail[string](err)
	}

	return api.OfSuccess("check delete index qps success")
}

func RunTask(requestId string, pr *ProcessRequest) error {
	return checkIndexQps(requestId, pr.IndexName, pr.QpsCheckRetryCount, pr.QpsCheckRetryWait)
}

func checkIndexQps(requestId string, indexName string, retryCount int, retryWait time.Duration) error {
	if retryCount <= 0 {
		retryCount = 3 // 默认重试3次
	}
	if retryWait <= 0 {
		retryWait = 5 * time.Second // 默认每次重试间隔5秒
	}

	// 重试校验
	indexStatusRequest := engine.NewIndexStatusRequest(requestId, nil)
	retryFunc := func() error {
		// 校验索引是否存在流量
		engineIndexStatusResp, err := indexStatusRequest.Send()
		if err != nil {
			err = errors.WithMessage(err, "call engine index status failed")
			log.Error("[%s]call engine index status failed: %v", requestId, err)
			return err
		}

		engineIndexInfos := engineIndexStatusResp.IndexInfos
		if len(engineIndexInfos) == 0 {
			log.Info("[%s]index not exist: %s", requestId, indexName)
			return nil
		}

		for _, engineIndexInfo := range engineIndexInfos {
			if engineIndexInfo.IndexName == indexName {
				indexQPS := engineIndexInfo.IndexQPS
				if indexQPS > 0 {
					log.Info("[%s]index %s still has qps: %d", requestId, indexName, indexQPS)
					return errors.Errorf("index has qps: %d, cannot delete: %s", indexQPS, indexName)
				}
			}
		}

		return nil
	}
	return retry.DoRetryWithMaxRetries(retryFunc, retryCount, retryWait, "check-delete-index-qps")
}
