package index

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/dump"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/oss"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/pkg/errors"
)

const (
	TypeName = "check-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	IndexSize           int64  `json:"indexSize"`
	IndexMemorySize     int64  `json:"indexMemorySize,omitempty"`
	DiskIndexMemorySize int64  `json:"diskIndexMemorySize,omitempty"`
	DumpCheckInfo       string `json:"dumpCheckInfo,omitempty"`
	MemoryUsageInfo     string `json:"memoryUsageInfo"`
	DiskUsageInfo       string `json:"diskUsageInfo"`
}

type ProcessRequest struct {
	IndexName                 string   `json:"indexName,omitempty" validate:"required"`
	IndexVersion              string   `json:"indexVersion,omitempty" validate:"required"`
	DbPath                    string   `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"` // 引擎构建出的索引存放目录
	Oss                       *dto.Oss `json:"oss,omitempty" validate:"required,dive"`                           // oss相关配置
	EngineBuildResultFileName string   `json:"engineBuildResultFileName,omitempty" default:"check.json"`         // engine build结果文件名称
	AgentConfigDirName        string   `json:"agentConfigDirName,omitempty" default:"agent_config"`              // agent配置文件目录名称

	DiskUsageThreshold       int      `json:"diskUsageThreshold,omitempty" default:"93"`               // 磁盘使用阈值, 默认90%
	MemoryUsageThreshold     int      `json:"memoryUsageThreshold,omitempty" default:"93"`             // 内存使用阈值, 默认90%
	MemoryInflateFactor      int      `json:"memoryInflateFactor,omitempty" default:"5"`               // 索引加载到内存的膨胀比率, 默认5%
	DumpResultFileName       string   `json:"dumpResultFileName,omitempty" default:"dump-result.json"` // dump结果文件名称
	IsReboot                 *bool    `json:"isReboot,omitempty" default:"false"`                      // 是否重启更新
	NeedDumpCheck            *bool    `json:"needDumpCheck,omitempty" default:"false"`                 // 是否需要dump校验
	IsDoubleVersion          *bool    `json:"isDoubleVersion" validate:"required" default:"false"`     // 是否双版本索引
	ServingVersions          []string `json:"servingVersions" validate:"required,min=1"`               // 索引最近更新成功的n个版本(即双版本索引应该保留的n个版本). 默认为n=2
	DiskIndexMemoryThreshold float64  `json:"diskIndexMemoryThreshold" default:"0.2"`                  // 磁盘索引保留的内存阈值
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	log.Info("[%s]check index from oss request: %+v", ctx.RequestId, pr)

	c, err := oss.NewClient(pr.Oss.Endpoint, pr.Oss.Bucket, pr.Oss.AccessKey, pr.Oss.SecretKey)
	if err != nil {
		err = errors.WithMessage(err, "create oss client fail")
		return api.UpdateFail[string](err)
	}

	resultMessageInfo, err := RunTask(ctx.RequestId, ctx.TaskId, c, pr)
	if err != nil {
		err = errors.WithMessage(err, "check index fail")
		return api.UpdateFail[string](err)
	}

	// 响应消息
	var resultMessage string
	resultMessageContent, err := json.Marshal(resultMessageInfo)
	if err != nil {
		log.Warn("[%s]marshal result message info fail: %+v", ctx.RequestId, err)
	} else {
		resultMessage = string(resultMessageContent)
	}
	return api.OfSuccessWithMessage[string]("check index success", resultMessage)
}

func RunTask(requestId, taskId string, c *oss.Client, pr *ProcessRequest) (*ResultMessageInfo, error) {
	var (
		err               error
		resultMessageInfo = &ResultMessageInfo{}
	)

	// 卡点校验: 当前任务是否属于当前集群
	err = common.CheckTask(requestId, taskId)
	if err != nil {
		return nil, err
	}

	// 卡点校验: 校验拉取的数据量是否符合最新的分区数据量
	if *pr.NeedDumpCheck {
		err = CheckDump(requestId, pr, c, resultMessageInfo)
		if err != nil {
			return nil, err
		}
	}

	// 卡点校验: 判断磁盘和内存能否承载索引
	err = checkIndexSize(requestId, pr, c, resultMessageInfo)
	if err != nil {
		return nil, err
	}

	return resultMessageInfo, nil
}

// 读取oss上的check.json,获取索引的大小.判断磁盘和内存能否承载
func checkIndexSize(requestId string, pr *ProcessRequest, c *oss.Client, resultMessageInfo *ResultMessageInfo) error {
	// 读取oss上的check.json
	checkFilePath := filepath.Join(pr.Oss.Dir, pr.EngineBuildResultFileName)
	checkContent, err := c.ReadFileContent(checkFilePath)
	if err != nil {
		return errors.WithMessagef(err, "read oss check.json file fail. oss path: %s", checkFilePath)
	}
	// 反序列化
	ber := &engine.BuildEngineResponse{}
	err = json.Unmarshal(checkContent, ber)
	if err != nil {
		return errors.Wrapf(err, "unmarshal oss check.json file fail: %s", checkContent)
	}
	// 检查结果的响应头
	if ber.Header == nil {
		return errors.New("invalid check.json file, header is nil")
	}
	// 检查结果是否成功
	if !ber.Header.IsSuccess() {
		return errors.Errorf("check index size fail, build result fail: %s", ber.Header.ErrMsg())
	}
	// 获取构建结果
	buildResult := ber.BuildResult
	if buildResult == nil {
		return errors.New("invalid check.json file, build result is nil")
	}

	// 判断磁盘和内存能否承载
	indexBytes := uint64(buildResult.IndexTotalBytes)
	resultMessageInfo.IndexSize = int64(indexBytes)

	indexMemorySize := indexBytes
	if buildResult.IndexMemoryBytes > 0 {
		indexMemorySize = uint64(buildResult.IndexMemoryBytes)
		log.Info("[%s]use index_memory_bytes: %d", requestId, indexMemorySize)

		// 如果是磁盘索引，需要加上磁盘文件的保留内存
		diskBytes := uint64(buildResult.IndexTotalBytes - buildResult.IndexMemoryBytes)
		if pr.DiskIndexMemoryThreshold > 0 && diskBytes > 0 {
			diskIndexMemory := float64(diskBytes) * pr.DiskIndexMemoryThreshold
			log.Info("[%s]add disk index memory: %s", requestId, util.ByteToHumanReadable(int64(diskIndexMemory)))
			indexMemorySize += uint64(diskIndexMemory)

			resultMessageInfo.DiskIndexMemorySize = int64(diskIndexMemory)
		}

		resultMessageInfo.IndexMemorySize = int64(indexMemorySize)
	}

	// 判断磁盘能否承载
	err = checkDisk(requestId, pr, indexBytes, resultMessageInfo)
	if err != nil {
		return err
	}

	// 判断内存能否承载
	err = checkMemory(requestId, pr, indexMemorySize, resultMessageInfo)
	if err != nil {
		return err
	}

	return nil
}

func checkMemory(requestId string, pr *ProcessRequest, indexBytes uint64, resultMessageInfo *ResultMessageInfo) error {
	// 获取当前已经使用内存大小
	memLimit, memUsed, err := common.GetMemoryUsage(requestId, pr.DbPath)
	if err != nil {
		return errors.WithMessage(err, "get memory usage fail")
	}

	// 获取rss大小
	rssMemory := util.GetRSSMemory()

	// 获取原有索引的大小
	indexDbDir := filepath.Join(pr.DbPath, pr.IndexName)
	originalIndexSize, err := common.GetLocalRemoveIndexSize(requestId, pr.IndexVersion, indexDbDir, pr.EngineBuildResultFileName, *pr.IsReboot, *pr.IsDoubleVersion, pr.ServingVersions)
	if err != nil {
		return errors.WithMessage(err, "get local index size fail")
	}

	// 计算索引加载到内存需要的大小: 索引加载到内存会膨胀约5%的大小
	indexMemorySize := indexBytes + indexBytes*uint64(pr.MemoryInflateFactor)/100
	// 判断内存加上索引的大小使用是否会超过阈值
	var (
		afterIndexMemoryUsageRate float64
		memoryUsageInfo           string
	)
	if *pr.IsReboot || *pr.IsDoubleVersion {
		// 如果是重启更新或者是双版本, 需要考虑减去原有索引的大小
		// note: 这里存在减法, 不能使用uint64, 否则可能会溢出
		afterIndexMemoryUsageRate = float64(int64(memUsed)+int64(indexMemorySize)-originalIndexSize+rssMemory) * 100 / float64(memLimit)
		memoryUsageInfo = fmt.Sprintf("(memoryUsedSize[%s] + indexSize[%s] - originalIndexSize[%s] + rssMemory[%s]) / totalMemorySize[%s] = memoryUsageRate[%.2f%%] %s threshold[%d%%]",
			util.ByteToHumanReadable(int64(memUsed)),
			util.ByteToHumanReadable(int64(indexMemorySize)),
			util.ByteToHumanReadable(originalIndexSize),
			util.ByteToHumanReadable(rssMemory),
			util.ByteToHumanReadable(int64(memLimit)),
			afterIndexMemoryUsageRate,
			func() string {
				if afterIndexMemoryUsageRate > float64(pr.MemoryUsageThreshold) {
					return ">"
				}
				return "<="
			}(),
			pr.MemoryUsageThreshold)
	} else {
		afterIndexMemoryUsageRate = float64(memUsed+indexMemorySize+uint64(rssMemory)) * 100 / float64(memLimit)
		memoryUsageInfo = fmt.Sprintf("(memoryUsedSize[%s] + indexSize[%s] + rssMemory[%s]) / totalMemorySize[%s] = memoryUsageRate[%.2f%%] %s threshold[%d%%]",
			util.ByteToHumanReadable(int64(memUsed)),
			util.ByteToHumanReadable(int64(indexMemorySize)),
			util.ByteToHumanReadable(rssMemory),
			util.ByteToHumanReadable(int64(memLimit)),
			afterIndexMemoryUsageRate,
			func() string {
				if afterIndexMemoryUsageRate > float64(pr.MemoryUsageThreshold) {
					return ">"
				}
				return "<="
			}(),
			pr.MemoryUsageThreshold)
	}
	log.Info("[%s]index memory check info: %s", requestId, memoryUsageInfo)
	resultMessageInfo.MemoryUsageInfo = memoryUsageInfo
	if afterIndexMemoryUsageRate > float64(pr.MemoryUsageThreshold) {
		return errors.Errorf("memory space not enough!!! %s", memoryUsageInfo)
	}
	return nil
}

func checkDisk(requestId string, pr *ProcessRequest, indexBytes uint64, resultMessageInfo *ResultMessageInfo) error {
	// 获取pr.DbPath的父目录
	diskPath := filepath.Dir(pr.DbPath)
	diskTotal, diskUsed, err := util.DiskUsage(diskPath)
	if err != nil {
		return errors.WithMessage(err, "get disk usage fail")
	}
	// 判断磁盘加上索引的大小使用是否会超过阈值
	afterIndexDiskUsageRate := float64(diskUsed+indexBytes) * 100 / float64(diskTotal)
	// 磁盘使用情况日志格式化为: (磁盘已使用大小 + 索引大小) / 磁盘总大小 = 磁盘使用率 >或者是<= 阈值
	diskUsageInfo := fmt.Sprintf("(diskUsedSize[%s] + indexSize[%s]) / totalDiskSize[%s] = diskUsageRate[%.2f%%] %s threshold[%d%%]",
		util.ByteToHumanReadable(int64(diskUsed)),
		util.ByteToHumanReadable(int64(indexBytes)),
		util.ByteToHumanReadable(int64(diskTotal)),
		afterIndexDiskUsageRate,
		func() string {
			if afterIndexDiskUsageRate > float64(pr.DiskUsageThreshold) {
				return ">"
			}
			return "<="
		}(),
		pr.DiskUsageThreshold)
	log.Info("[%s]index disk check info: %s", requestId, diskUsageInfo)
	resultMessageInfo.DiskUsageInfo = diskUsageInfo
	if afterIndexDiskUsageRate > float64(pr.DiskUsageThreshold) {
		return errors.Errorf("disk space not enough: %s!!! %s", diskPath, diskUsageInfo)
	}
	return nil
}

// CheckDump 校验拉取的数据量是否符合最新的分区数据量
func CheckDump(requestId string, pr *ProcessRequest, c *oss.Client, resultMessageInfo *ResultMessageInfo) error {
	// 读取oss上的dump-result.json
	ossConfigDir := strings.Replace(pr.Oss.Dir, "dip_new", "dip_config", 1)
	dumpResultFilePath := filepath.Join(ossConfigDir, pr.DumpResultFileName)
	log.Info("[%s]check dump result file: %s", requestId, dumpResultFilePath)
	dumpResultContent, err := c.ReadFileContent(dumpResultFilePath)
	if err != nil {
		return errors.WithMessagef(err, "read oss %s file fail", pr.DumpResultFileName)
	}
	// 反序列化
	dumpResult := &dto.DumpResult{}
	err = json.Unmarshal(dumpResultContent, dumpResult)
	if err != nil {
		return errors.Wrapf(err, "unmarshal dump result fail: %s", dumpResultContent)
	}
	// 获取当前最新的分区数据量
	dumpInfo := &dump.Info{}
	dumpInfoContent := dumpResult.DumpInfo
	err = json.Unmarshal([]byte(dumpInfoContent), dumpInfo)
	if err != nil {
		return errors.Wrapf(err, "unmarshal dump info fail: %s", dumpInfoContent)
	}
	// 校验的时候, 超时时间设置的短一点
	dumpInfo.WaitOdpsSuccessTimeout = 3 * time.Minute
	dumpService, err := dump.NewService(requestId, dumpInfo)
	if err != nil {
		// 创建odps client超时
		if strings.Contains(err.Error(), "context deadline exceeded") {
			return api.NewUpdateErrorWithCode(api.UpdateException_OdpsClientTimeout, fmt.Sprintf("create dump service fail: odps clinet timeout. %+v", err))
		}
		return errors.WithMessage(err, "create dump service fail")
	}
	total := dumpService.Total()
	dumpCheckInfo := fmt.Sprintf("dump dataCount[%d] + emptyCount[%d] %s odpsTotal[%d]",
		dumpResult.Count,
		dumpResult.EmptyCount,
		func() string {
			if dumpResult.Count+dumpResult.EmptyCount != int64(total) {
				return "!="
			}
			return "="
		}(),
		total)
	log.Info("[%s]dump check info: %s", requestId, dumpCheckInfo)
	resultMessageInfo.DumpCheckInfo = dumpCheckInfo
	// 检查dump的数据量是否符合最新的分区数据量
	if dumpResult.Count+dumpResult.EmptyCount != int64(total) {
		return api.NewUpdateErrorWithCode(api.UpdateException_DumpNotEqual, fmt.Sprintf("dump data check fail: %s", dumpCheckInfo))
	}
	return nil
}

// 校验引擎加载的索引版本，在本地是否存在
func checkEngineVersion(requestId string, pr *ProcessRequest) error {
	// 调用引擎接口查询加载的索引版本
	indexStatusResp := engine.NewIndexStatusRequest(requestId, nil)
	indexInfos := indexStatusResp.Indexes
	// 校验索引版本是否存在
	for _, indexInfo := range indexInfos {
		// 校验索引版本是否存在
		if indexInfo.IndexName == pr.IndexName {
			// 校验索引版本是否存在
			localIndexPath := filepath.Join(pr.DbPath, indexInfo.IndexName, indexInfo.IndexVersion)
			if !util.IsPathExist(localIndexPath) {
				return errors.Errorf("index version exist, but not found in local: %s", localIndexPath)
			}
		}
	}
	return nil
}
