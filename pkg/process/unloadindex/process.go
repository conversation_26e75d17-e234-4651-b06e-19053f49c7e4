package unloadindex

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"time"
)

const (
	TypeName = "unload-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	UnloadIndex      []string `json:"unloadIndex,omitempty"`      // 卸载的索引
	RemovedIndexPath []string `json:"removedIndexPath,omitempty"` // 删除的索引路径
	BackupIndexPath  string   `json:"backupIndexPath,omitempty"`  // 备份的索引路径
	IsRollback       bool     `json:"isRollback,omitempty"`       // 是否回滚
}

type ProcessRequest struct {
	IndexName            string        `json:"indexName,omitempty" validate:"required"`                                     // 必填。索引名称
	IndexVersion         string        `json:"indexVersion,omitempty" validate:"required"`                                  // 必填。索引版本
	IsDoubleVersion      *bool         `json:"isDoubleVersion" validate:"required"`                                         // 是否双版本索引
	DbPath               string        `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`            // 引擎构建出的索引存放目录
	GzPath               string        `json:"gzPath,omitempty" default:"/app/rel/data/gz" validate:"required"`             // 临时存放gz文件的目录
	ServingVersions      []string      `json:"servingVersions" validate:"required,min=1"`                                   // 索引最近更新成功的n个版本(双版本索引应该保留的n个版本). 默认为n=2
	NeedCommit           *bool         `json:"needCommit" default:"true"`                                                   // 是否需要commit
	UnloadCheckWaitTime  time.Duration `json:"unloadCheckWaitTime" default:"5s"`                                            // 卸载索引检查等待时间
	UnloadCheckTimeout   time.Duration `json:"unloadCheckTimeout" default:"5m"`                                             // 卸载索引检查超时时间
	SleepTimeAfterUnload time.Duration `json:"sleepTimeAfterUnload" default:"18s"`                                          // 卸载索引后等待时间
	DiskUsageThreshold   int           `json:"diskUsageThreshold,omitempty" default:"93"`                                   // 磁盘使用阈值, 默认90%
	BackupDiskUsageRatio int           `json:"backupDiskUsageRatio"`                                                        // 集群索引备份磁盘使用率阈值
	BackupDir            string        `json:"backupIndexDir,omitempty" default:"/app/rel/data/backup" validate:"required"` // 备份索引的路径
	IsRollback           *bool         `json:"isRollback,omitempty"`                                                        // 是否回滚
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	requestContent, _ := json.Marshal(pr)
	log.Info("[%s]engine unload index request: %s", ctx.RequestId, requestContent)

	rmi := &ResultMessageInfo{}
	rmi.UnloadIndex = make([]string, 0)
	rmi.RemovedIndexPath = make([]string, 0)

	// 本地索引路径
	localIndexDbPath := filepath.Join(pr.DbPath, pr.IndexName)
	// 是否需要commit
	needCommit := pr.NeedCommit != nil && *pr.NeedCommit
	isRollback := pr.IsRollback != nil && *pr.IsRollback
	rmi.IsRollback = isRollback

	log.Info("[%s][%s]serving versions: %v", ctx.RequestId, pr.IndexName, pr.ServingVersions)
	if isRollback || *pr.IsDoubleVersion {
		// 获取索引当前存在的版本: 从本地dbPath中scan出所有的索引版本
		if util.IsPathExist(localIndexDbPath) {
			// 读取localIndexDbPath目录下的所有文件夹, 作为索引版本
			localVersions, err := util.GetSubdirectories(localIndexDbPath)
			if err != nil {
				err = errors.WithMessagef(err, "get local versions fail: %s", localIndexDbPath)
				return api.UpdateFail[string](err)
			}
			log.Info("[%s]local versions: %s(%v)", ctx.RequestId, pr.IndexName, localVersions)

			if len(localVersions) > 0 {
				// 调用引擎接口卸载所有的索引版本
				var needSleep bool
				for _, version := range localVersions {
					err = common.UnloadIndex(ctx.RequestId, pr.IndexName, version)
					if err != nil {
						return api.UpdateFail[string](err)
					}
					rmi.UnloadIndex = append(rmi.UnloadIndex, version)

					// 校验索引是否完成卸载
					ns, err := common.CheckIndexUnload(ctx.RequestId, pr.IndexName, version, pr.UnloadCheckTimeout, pr.UnloadCheckWaitTime)
					if err != nil {
						return api.UpdateFail[string](err)
					}
					needSleep = needSleep || ns
					log.Info("[%s]index unload success: %s(%s)", ctx.RequestId, pr.IndexName, version)
				}

				// 只有推荐需要sleep, commit也是只有推荐需要调用
				if needCommit && needSleep {
					// 等待索引的保护时间结束(索引attach以及延迟释放)
					log.Info("[%s]wait for index protection time end: sleep %s", ctx.RequestId, pr.SleepTimeAfterUnload.String())
					time.Sleep(pr.SleepTimeAfterUnload)
				}
				// TODO 目前Dsearch重启更新为"真重启", 无需调用unload processor. 因此也无需针对性的校验是否unload成功

				outdatedVersions, err := util.GetOutdatedVersionWithServingVersions(pr.DbPath, pr.IndexName, pr.ServingVersions)
				if err != nil {
					err = errors.WithMessagef(err, "get outdated version fail: %s", localIndexDbPath)
					return api.UpdateFail[string](err)
				}
				log.Info("[%s]outdated versions: %s(%v)", ctx.RequestId, pr.IndexName, outdatedVersions)

				// 删除过期的本地索引目录
				if len(outdatedVersions) > 0 {
					isRollback := pr.IsRollback != nil && *pr.IsRollback
					// 如果开启了索引备份且非回滚的情况下, 则备份最近的索引版本
					if pr.BackupDiskUsageRatio > 0 && !isRollback {
						backupIndexVersion := outdatedVersions[len(outdatedVersions)-1]
						err := common.BackupIndex(pr.DbPath, pr.BackupDir, pr.IndexName, backupIndexVersion, pr.BackupDiskUsageRatio, pr.DiskUsageThreshold)
						if err != nil {
							// 备份失败只发送报警
							log.Error("[%s]backup index failed: %s. err: %v", ctx.RequestId, pr.IndexName, err)
							go platform.NewWarnRequestWithMsg(ctx.RequestId, fmt.Sprintf("backup index failed: %s. err: %s", pr.IndexName, err)).Send()
						} else {
							backUpIndexPath := filepath.Join(pr.BackupDir, pr.IndexName, backupIndexVersion)
							log.Info("[%s]backup index success: %s", ctx.RequestId, backUpIndexPath)
							rmi.BackupIndexPath = backUpIndexPath
						}
					}

					// 删除过期的索引版本
					for _, version := range outdatedVersions {
						// 删除本地索引目录
						outdatedIndexVersionDir := filepath.Join(localIndexDbPath, version)
						err = os.RemoveAll(outdatedIndexVersionDir)
						if err != nil {
							err = errors.Wrapf(err, "remove outdated index db path fail: %s", outdatedIndexVersionDir)
							return api.UpdateFail[string](err)
						}
						log.Info("[%s]remove outdated index version dir: %s", ctx.RequestId, outdatedIndexVersionDir)
						rmi.RemovedIndexPath = append(rmi.RemovedIndexPath, outdatedIndexVersionDir)
					}
				}
			}
		}
	} else {
		err = common.UnloadIndex(ctx.RequestId, pr.IndexName, pr.IndexVersion)
		if err != nil {
			return api.UpdateFail[string](err)
		}
		rmi.UnloadIndex = append(rmi.UnloadIndex, pr.IndexVersion)

		// 校验索引是否完成卸载
		needSleep, err := common.CheckIndexUnload(ctx.RequestId, pr.IndexName, pr.IndexVersion, pr.UnloadCheckTimeout, pr.UnloadCheckWaitTime)
		if err != nil {
			return api.UpdateFail[string](err)
		}
		log.Info("[%s]index unload success: %s(%s)", ctx.RequestId, pr.IndexName, pr.IndexVersion)

		// 只有推荐需要sleep, commit也是只有推荐需要调用
		if needCommit && needSleep {
			// 等待索引的保护时间结束(索引attach以及延迟释放)
			log.Info("[%s]wait for index protection time end: sleep %s", ctx.RequestId, pr.SleepTimeAfterUnload.String())
			time.Sleep(pr.SleepTimeAfterUnload)
		}
		// TODO 目前Dsearch重启更新为"真重启", 无需调用unload processor. 因此也无需针对性的校验是否unload成功

		// 删除本地gz目录
		localIndexGzPath := filepath.Join(pr.GzPath, pr.IndexName)
		if util.IsPathExist(localIndexGzPath) {
			err = os.RemoveAll(localIndexGzPath)
			if err != nil {
				err = errors.Wrapf(err, "remove local index gz path fail: %s", localIndexGzPath)
				return api.UpdateFail[string](err)
			}
			log.Info("[%s]remove local index gz dir: %s", ctx.RequestId, localIndexGzPath)
		}

		// 删除本地索引目录
		if util.IsPathExist(localIndexDbPath) {
			isRollback := pr.IsRollback != nil && *pr.IsRollback
			// 如果开启了索引备份且非回滚的情况下, 则备份最近的索引版本
			if pr.BackupDiskUsageRatio > 0 && !isRollback {
				backupVersion, err := common.BackupLatestVersionIndex(pr.DbPath, pr.BackupDir, pr.IndexName, pr.BackupDiskUsageRatio, pr.DiskUsageThreshold)
				if err != nil {
					// 备份失败只发送报警
					log.Error("[%s]backup index failed: %s. err: %v", ctx.RequestId, pr.IndexName, err)
					go platform.NewWarnRequestWithMsg(ctx.RequestId, fmt.Sprintf("backup index failed: %s. err: %s", pr.IndexName, err)).Send()
				} else if backupVersion != "" {
					backUpIndexPath := filepath.Join(pr.BackupDir, pr.IndexName, backupVersion)
					log.Info("[%s]backup index success: %s", ctx.RequestId, backUpIndexPath)
					rmi.BackupIndexPath = backUpIndexPath
				}
			}

			// 删除本地索引db目录
			err = os.RemoveAll(localIndexDbPath)
			if err != nil {
				err = errors.Wrapf(err, "remove local index db path fail: %s", localIndexDbPath)
				return api.UpdateFail[string](err)
			}
			log.Info("[%s]remove local index db dir: %s", ctx.RequestId, localIndexDbPath)
			rmi.RemovedIndexPath = append(rmi.RemovedIndexPath, localIndexDbPath)
		}
	}

	resultMessageContent, _ := json.Marshal(rmi)
	return api.OfSuccessWithMessage("engine unload index success", string(resultMessageContent))
}
