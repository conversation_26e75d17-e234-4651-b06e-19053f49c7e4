package deleteindex

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/retry"
	"os"
	"path/filepath"
	"time"

	"github.com/pkg/errors"
)

const (
	TypeName = "delete-index" // 卸载索引并删除本地索引相关的目录
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	UnloadIndex      []string `json:"unloadIndex,omitempty"`      // 卸载的索引
	RemovedIndexPath []string `json:"removedIndexPath,omitempty"` // 删除的索引路径
}

type ProcessRequest struct {
	IndexName           string        `json:"indexName,omitempty" validate:"required"`                          // 必填。索引名称
	IsDoubleVersion     *bool         `json:"isDoubleVersion" validate:"required"`                              // 是否双版本索引
	DbPath              string        `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"` // 引擎构建出的索引存放目录
	GzPath              string        `json:"gzPath,omitempty" default:"/app/rel/data/gz" validate:"required"`  // 临时存放gz文件的目录
	NeedCommit          *bool         `json:"needCommit" default:"true"`                                        // 是否需要commit
	UnloadCheckWaitTime time.Duration `json:"unloadCheckWaitTime" default:"5s"`                                 // 卸载索引检查等待时间
	UnloadCheckTimeout  time.Duration `json:"unloadCheckTimeout" default:"5m"`                                  // 卸载索引检查超时时间
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	requestContent, _ := json.MarshalToString(pr)
	log.Info("[%s]engine delete index request: %s", ctx.RequestId, requestContent)

	rmi, err := RunTask(ctx.RequestId, pr)
	if err != nil {
		return api.UpdateFail[string](err)
	}

	resultMessageContent, _ := json.Marshal(rmi)
	return api.OfSuccessWithMessage("engine delete index success", string(resultMessageContent))
}

func RunTask(requestId string, pr *ProcessRequest) (*ResultMessageInfo, error) {
	rmi := &ResultMessageInfo{}
	rmi.UnloadIndex = make([]string, 0)
	rmi.RemovedIndexPath = make([]string, 0)

	// 本地索引路径
	localIndexDbPath := filepath.Join(pr.DbPath, pr.IndexName)

	// 获取引擎服务中存在的版本
	engineVersions, err := getEngineIndexVersions(requestId, pr.IndexName)
	if err != nil {
		return rmi, err
	}
	log.Info("[%s]engine serving versions: %s(%v)", requestId, pr.IndexName, engineVersions)

	if len(engineVersions) > 0 {
		err = unloadIndexAllVersion(requestId, pr, engineVersions, rmi)
		if err != nil {
			return rmi, err
		}
	}

	// 删除本地索引gz目录
	localIndexGzPath := filepath.Join(pr.GzPath, pr.IndexName)
	err = os.RemoveAll(localIndexGzPath)
	if err != nil {
		err = errors.Wrapf(err, "remove local index gz dir fail: %s", localIndexGzPath)
		return rmi, err
	}
	log.Info("[%s]remove local index gz dir: %s", requestId, localIndexGzPath)
	rmi.RemovedIndexPath = append(rmi.RemovedIndexPath, localIndexGzPath)

	// 删除本地索引db目录
	err = os.RemoveAll(localIndexDbPath)
	if err != nil {
		err = errors.Wrapf(err, "remove local index db dir fail: %s", localIndexDbPath)
		return rmi, err
	}
	log.Info("[%s]remove local index db dir: %s", requestId, localIndexDbPath)
	rmi.RemovedIndexPath = append(rmi.RemovedIndexPath, localIndexDbPath)
	return rmi, nil
}

func unloadIndexAllVersion(requestId string, pr *ProcessRequest, engineServingVersions []string, rmi *ResultMessageInfo) error {
	var err error
	// 是否需要commit
	needCommit := pr.NeedCommit != nil && *pr.NeedCommit

	// Dsearch删除索引. 由于Dsearch原有的unload index接口会保障至少存在一个索引, 因此需要调用另一个接口
	if !needCommit {
		deleteVersion := "-1"
		deleteIndexRequest := engine.NewDeleteIndexRequest(requestId, pr.IndexName, deleteVersion)
		err = retry.DoRetry(func() error {
			_, innerErr := deleteIndexRequest.Send()
			if innerErr != nil {
				return errors.WithMessage(innerErr, "delete index fail")
			}
			return nil
		})
		if err != nil {
			return err
		}

		// 校验索引是否删除完成
		for _, version := range engineServingVersions {
			_, err = common.CheckIndexUnload(requestId, pr.IndexName, version, pr.UnloadCheckTimeout, pr.UnloadCheckWaitTime)
			if err != nil {
				return err
			}
			log.Info("[%s]index unload success: %s(%s)", requestId, pr.IndexName, version)
			rmi.UnloadIndex = append(rmi.UnloadIndex, deleteVersion)
		}
	} else {
		// Dgraph删除索引, 调用unload index删除所有正在服务的索引版本
		var needSleep bool
		for _, version := range engineServingVersions {
			err = common.UnloadIndex(requestId, pr.IndexName, version)
			if err != nil {
				return err
			}
			rmi.UnloadIndex = append(rmi.UnloadIndex, version)

			// 校验索引是否完成卸载
			ns, err := common.CheckIndexUnload(requestId, pr.IndexName, version, pr.UnloadCheckTimeout, pr.UnloadCheckWaitTime)
			if err != nil {
				return err
			}
			needSleep = needSleep || ns
			log.Info("[%s]index unload success: %s(%s)", requestId, pr.IndexName, version)
		}

		// 只有推荐需要sleep, commit也是只有推荐需要调用
		if needSleep {
			// 等待索引的保护时间结束(索引attach以及延迟释放)
			log.Info("[%s]wait for index protection time end: sleep 18s", requestId)
			time.Sleep(18 * time.Second)
		}
	}
	return nil
}

// 获取引擎内指定索引的版本
func getEngineIndexVersions(requestId string, indexName string) ([]string, error) {
	indexStatusRequest := engine.NewIndexStatusRequest(requestId, nil)
	versions := make([]string, 0)
	retryFunc := func() error {
		engineIndexStatusResp, err := indexStatusRequest.Send()
		if err != nil {
			err = errors.WithMessage(err, "call engine index status failed")
			log.Error("[%s]call engine index status failed: %v", requestId, err)
			return err
		}

		engineIndexInfos := engineIndexStatusResp.IndexInfos
		if len(engineIndexInfos) == 0 {
			log.Info("[%s]index not exist: %s", requestId, indexName)
			return nil
		}

		for _, engineIndexInfo := range engineIndexInfos {
			if engineIndexInfo.IndexName == indexName {
				versions = append(versions, engineIndexInfo.IndexVersion)
			}
		}

		return nil
	}
	err := retry.DoRetryWithMaxRetries(retryFunc, 3, 5*time.Second, "get-engine-index-versions")
	return versions, err
}
