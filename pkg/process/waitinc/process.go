package waitinc

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util/json"
	"fmt"
	"github.com/pkg/errors"
	"time"
)

const (
	TypeName = "wait-inc"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	IsSkipWaitInc bool `json:"isSkipWaitInc,omitempty"` // 是否跳过等待追增量
}

type ProcessRequest struct {
	IndexName             string `json:"indexName,omitempty"`
	IndexVersion          string `json:"indexVersion,omitempty"`
	IsInc                 *bool  `json:"isInc" default:"true"`                    // 是否增量索引
	WaitIncCount          int    `json:"waitIncCount,omitempty" default:"60"`     // 等待追增量的最大重试次数
	IncWaitTimeoutSeconds int    `json:"incWaitTimeoutSeconds" default:"1800"`    // 可选. 等待追增量的超时时间, 默认1800s
	IncOffsetThreshold    int    `json:"incOffsetThreshold" default:"120"`        // 可选. 追增量的offset阈值, 默认120
	WaitIncRetryCount     int    `json:"waitIncRetryCount,omitempty" default:"5"` // 等待追增量超时后的最大重试次数
	IsReboot              *bool  `json:"isReboot,omitempty" default:"false"`      // 是否重启更新
	IsSkipWaitInc         *bool  `json:"isSkipWaitInc,omitempty" default:"false"` // 是否跳过等待追增量
}

type Processor struct {
	config *Config
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		config: &Config{},
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return p.config
}

func (p *Processor) Start() error {
	content, _ := json.Marshal(p.config)
	log.Info("[%s]engine wait inc config: %s", p.Name(), string(content))
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	prContent, _ := json.Marshal(pr)
	log.Info("[%s]engine wait inc request: %s", ctx.RequestId, string(prContent))

	rmi := &ResultMessageInfo{}
	rmi.IsSkipWaitInc = false

	// 如果跳过等待追增量
	if *pr.IsSkipWaitInc {
		log.Info("[%s]skip wait inc: %s(%s)", ctx.RequestId, pr.IndexName, pr.IndexVersion)
		rmi.IsSkipWaitInc = true
		resultMessageContent, _ := json.Marshal(rmi)
		return api.OfSuccessWithMessage("skip wait inc", string(resultMessageContent))
	}

	// 如果是真重启更新, 则需要等待所有索引都追增量完成
	if *pr.IsReboot && *p.config.IsRealReboot {
		return waitAllInc(ctx.RequestId, pr)
	}

	// 不是增量索引就不用等待追增量
	if !*pr.IsInc {
		log.Info("[%s]index is not inc, no need to wait: %s(%s)", ctx.RequestId, pr.IndexName, pr.IndexVersion)
		return api.OfSuccess("index is not inc, no need to wait")
	}

	// 热更新的情况: IndexName和IndexVersion是必填的
	if pr.IndexName == "" || pr.IndexVersion == "" {
		return api.UpdateFail[string](errors.New("invalid param: indexName and indexVersion are required"))
	}

	// 等待当前索引的追增量进度
	return waitInc(pr, ctx.RequestId)
}

// 等待所有索引的追增量完成
func waitAllInc(requestId string, pr *ProcessRequest) api.Result {
	var (
		timeOffset   int64
		maxIndexName string
		err          error
	)
	log.Info("[%s]wait all index inc timeout: %d, retry count: %d", requestId, pr.IncWaitTimeoutSeconds, pr.WaitIncRetryCount)
	retry := pr.WaitIncRetryCount
	sleepTime := pr.IncWaitTimeoutSeconds / pr.WaitIncCount
	for j := 0; j < retry; j++ {
		for i := 0; i < pr.WaitIncCount; i++ {
			timeOffset, maxIndexName, err = getMaxTimeOffset(requestId)
			if err != nil {
				return api.UpdateFail[string](err)
			}
			log.Info("[%s]current max index inc offset: %d", requestId, timeOffset)
			if timeOffset < int64(pr.IncOffsetThreshold) {
				log.Info("[%s]index inc offset is less than threshold: %d < %d", requestId, timeOffset, pr.IncOffsetThreshold)
				return api.OfSuccess("index wait inc success")
			}
			log.Info("[%s]wait inc offset: %d, retry after %ds", requestId, timeOffset, sleepTime)

			time.Sleep(time.Duration(sleepTime) * time.Second)
		}

		// 等待超时后, 重试, 同时发送报警
		errMsg := fmt.Sprintf("[%s][%s]index wait inc timeout: current timeOffset: %d > threshold: %d. retry: %d/%d", requestId, maxIndexName, timeOffset, pr.IncOffsetThreshold, j+1, retry)
		log.Warn(errMsg)
		// 发送报警
		wr := platform.NewWarnRequestWithMsg(requestId, errMsg)
		_, err := wr.Send()
		if err != nil {
			log.Error("%s", errors.WithMessage(err, "send warn msg fail"))
		}

	}
	return api.UpdateFail[string](errors.Errorf("index wait inc timeout: current timeOffset: %d > threshold: %d. timeout threshold: %dseconds", timeOffset, pr.IncOffsetThreshold, pr.IncWaitTimeoutSeconds))
}

// 等待索引追增量完成
func waitInc(pr *ProcessRequest, requestId string) api.Result {
	var (
		timeOffset int64
		err        error
	)
	log.Info("[%s]wait inc timeout: %d, retry count: %d", requestId, pr.IncWaitTimeoutSeconds, pr.WaitIncRetryCount)
	retry := pr.WaitIncRetryCount
	//envWaitRetryCountStr := os.Getenv("WAIT_INC_COUNT")
	//if envWaitRetryCountStr != "" {
	//	envWaitRetryCount, err := strconv.Atoi(envWaitRetryCountStr)
	//	if err == nil {
	//		if envWaitRetryCount > retry {
	//			retry = envWaitRetryCount
	//		}
	//	} else {
	//		log.Warn("parse env WAIT_INC_COUNT error: %s. envWaitRetryCountStr: %s", err, envWaitRetryCountStr)
	//	}
	//}
	log.Info("[%s]wait inc retry count: %d", requestId, retry)
	sleepTime := pr.IncWaitTimeoutSeconds / pr.WaitIncCount
	for j := 0; j < retry; j++ {
		for i := 0; i < pr.WaitIncCount; i++ {
			timeOffset, err = getIndexTimeOffset(requestId, pr)
			if err != nil {
				return api.UpdateFail[string](err)
			}
			log.Info("[%s]current index inc offset: %d, index: %s(%s)", requestId, timeOffset, pr.IndexName, pr.IndexVersion)
			if timeOffset < int64(pr.IncOffsetThreshold) {
				log.Info("[%s]index inc offset is less than threshold: %d < %d, index: %s(%s)", requestId, timeOffset, pr.IncOffsetThreshold, pr.IndexName, pr.IndexVersion)
				return api.OfSuccess[string]("index wait inc success")
			}
			log.Info("[%s]wait inc offset: %d, retry after %ds", requestId, timeOffset, sleepTime)

			time.Sleep(time.Duration(sleepTime) * time.Second)
		}

		// 等待超时后, 重试, 同时发送报警
		errMsg := fmt.Sprintf("[%s][%s]index wait inc timeout: current timeOffset: %d > threshold: %d. retry: %d/%d", requestId, pr.IndexName, timeOffset, pr.IncOffsetThreshold, j+1, retry)
		log.Warn(errMsg)
		// 发送报警
		wr := platform.NewWarnRequestWithMsg(requestId, errMsg)
		_, err := wr.Send()
		if err != nil {
			log.Error("%s", errors.WithMessage(err, "send warn msg fail"))
		}

	}
	return api.UpdateFail[string](errors.Errorf("[%s(%s)]index wait inc timeout: current timeOffset: %d > threshold: %d. timeout threshold: %dseconds", pr.IndexName, pr.IndexVersion, timeOffset, pr.IncOffsetThreshold, pr.IncWaitTimeoutSeconds))
}

// 获取当前服务中索引的最大追增量
func getMaxTimeOffset(requestId string) (int64, string, error) {
	// 调用引擎接口获取索引的追增量信息
	infos, err := getIndexIncStatus(requestId, nil)
	if err != nil {
		return 0, "", err
	}
	if len(infos) == 0 {
		log.Info("[%s]no index inc info found)", requestId)
		return 0, "", nil
	}

	// 获取当前最大的索引追增量信息
	var maxTimeOffset int64
	var maxIndexName string
	for _, incInfo := range infos {
		if incInfo.EOF {
			continue
		}
		if incInfo.IndexSource == "formal" {
			continue
		}
		if incInfo.TimeOffset > maxTimeOffset {
			maxTimeOffset = incInfo.TimeOffset
			maxIndexName = incInfo.IndexName
		}
	}
	return maxTimeOffset, maxIndexName, nil
}

// 获取当前索引的追增量
func getIndexTimeOffset(requestId string, pr *ProcessRequest) (int64, error) {
	// 调用引擎接口获取索引的追增量信息
	infos, err := getIndexIncStatus(requestId, &engine.BaseIndexInfo{
		IndexName:    pr.IndexName,
		IndexVersion: pr.IndexVersion,
	})
	if err != nil {
		return 0, err
	}
	if len(infos) == 0 {
		log.Info("[%s]call engine no index inc info found: %s(%s)", requestId, pr.IndexName, pr.IndexVersion)
		return 0, nil
	}

	// 获取当前索引的追增量信息
	for _, incInfo := range infos {
		if incInfo.IndexName == pr.IndexName {
			if incInfo.EOF {
				return 0, nil
			}
			if incInfo.IndexVersion == pr.IndexVersion && incInfo.IndexSource == "formal" {
				return 0, nil
			}
			// buffer and not EOF
			return incInfo.TimeOffset, nil
		}
	}

	return 0, errors.Errorf("call engine no index found in inc status: %s(%s)", pr.IndexName, pr.IndexVersion)
}

// 调用引擎接口获取索引的追增量信息
func getIndexIncStatus(requestId string, indexInfo *engine.BaseIndexInfo) ([]engine.IndexIncInfo, error) {
	var ii []engine.BaseIndexInfo
	if indexInfo != nil {
		ii = []engine.BaseIndexInfo{
			{
				IndexName:    indexInfo.IndexName,
				IndexVersion: indexInfo.IndexVersion,
			},
		}
	} else {
		ii = []engine.BaseIndexInfo{}
	}
	er := engine.NewIncStatusRequest(requestId, ii)
	resp, err := er.Send()
	if err != nil {
		err = errors.WithMessagef(err, "failed to get index inc status: %s(%s)", indexInfo.IndexName, indexInfo.IndexVersion)
		return nil, err
	}

	return resp.IndexIncInfos, nil
}
