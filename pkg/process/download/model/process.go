package model

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/oss"
	"dip-agent/pkg/util/zip"
	"os"
	"path/filepath"
	"time"

	"github.com/pkg/errors"
)

const (
	TypeName = "download-model"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	ModelDbPath    string `json:"modelDbPath"`    // 模型存放目录
	OssPath        string `json:"ossPath"`        // oss上的模型路径
	DownloadCost   string `json:"downloadCost"`   // 下载模型的耗时
	DecompressCost string `json:"decompressCost"` // 解压的耗时
}

type ProcessRequest struct {
	ModelName      string   `json:"modelName,omitempty" validate:"required"`
	ModelVersion   string   `json:"modelVersion,omitempty" validate:"required"`
	ModelDbPath    string   `json:"modelDbPath,omitempty" default:"/app/rel/data/model/" validate:"required"` // 模型存放目录
	Oss            *dto.Oss `json:"oss,omitempty" validate:"required,dive"`                                   // oss相关配置
	OnlySingleFile *bool    `json:"onlySingleFile,omitempty" default:"false"`                                 // 是否下载的模型只允许单个文件

	IsReboot *bool `json:"isReboot,omitempty" default:"false"` // 是否重启更新

}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	log.Info("[%s]download model from oss request: %+v", ctx.RequestId, pr)

	// 执行下载任务
	rmi, err := RunTask(ctx.RequestId, pr)
	if err != nil {
		return api.UpdateFail[string](err)
	}

	// 响应内容
	resultMessageContent, _ := json.Marshal(rmi)
	return api.OfSuccessWithMessage[string]("download model success", string(resultMessageContent))
}

// RunTask 下载模型
func RunTask(requestId string, pr *ProcessRequest) (*ResultMessageInfo, error) {
	var (
		err error
		rmi = &ResultMessageInfo{}
	)

	// 本地模型目录
	localModelDbPath := filepath.Join(pr.ModelDbPath, pr.ModelName, pr.ModelVersion)
	rmi.ModelDbPath = localModelDbPath
	rmi.OssPath = pr.Oss.Dir

	// 创建本地模型目录
	err = util.CreateDir(localModelDbPath, true)
	if err != nil {
		return rmi, errors.WithMessagef(err, "create local model dir fail: %s", localModelDbPath)
	}
	log.Info("[%s]create model dir: %s", requestId, localModelDbPath)

	// oss client
	c, err := oss.NewClient(pr.Oss.Endpoint, pr.Oss.Bucket, pr.Oss.AccessKey, pr.Oss.SecretKey)
	if err != nil {
		return rmi, errors.WithMessage(err, "create oss client fail")
	}

	// 下载模型到本地
	err = downloadModel(requestId, localModelDbPath, c, pr, rmi)
	if err != nil {
		return rmi, err
	}

	// 获取模型目录下的文件信息
	entries, err := os.ReadDir(localModelDbPath)
	if err != nil {
		return rmi, errors.Wrapf(err, "failed to read dir: %s", localModelDbPath)
	}
	// 模型目录下的文件数量
	fileCount := len(entries)

	// 检测是否下载的空目录
	if fileCount == 0 {
		return rmi, errors.New("model dir is empty: " + localModelDbPath)
	}

	// 校验下载的模型文件. Dsearch只有一个模型压缩包
	// 2025-05-16 Dsearch无需此校验
	//if *pr.OnlySingleFile && fileCount != 1 {
	//	return rmi, errors.Errorf("model dir file count is not 1: %s. current count: %d", localModelDbPath, fileCount)
	//}

	// 原地解压目录中的文件
	start := time.Now()
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}
		if filepath.Ext(entry.Name()) != ".zip" {
			continue
		}
		log.Info("[%s]decompress model: %s", requestId, entry.Name())
		zipFilePath := filepath.Join(localModelDbPath, entry.Name())
		err = zip.Decompress(zipFilePath, localModelDbPath)
		if err != nil {
			return rmi, errors.WithMessagef(err, "decompress model fail: %s", zipFilePath)
		}
		log.Info("[%s]decompress model: %s", requestId, zipFilePath)

		// 删除zip文件
		err = os.Remove(zipFilePath)
		if err != nil {
			return rmi, errors.WithMessagef(err, "remove zip file fail: %s", zipFilePath)
		}
		log.Info("[%s]remove zip file: %s", requestId, zipFilePath)
	}
	rmi.DecompressCost = util.DurationToHumanReadable(time.Since(start))

	// 解压后也应该只有1个文件
	//if *pr.OnlySingleFile {
	//	fileCount, err = util.GetFileCount(localModelDbPath)
	//	if err != nil {
	//		return nil, errors.WithMessagef(err, "get file count fail: %s", localModelDbPath)
	//	}
	//	if fileCount != 1 {
	//		return nil, errors.Errorf("model dir file count is not 1: %s. current count: %d", localModelDbPath, fileCount)
	//	}
	//}

	return rmi, nil
}

// 下载模型
func downloadModel(requestId, localDir string, c *oss.Client, pr *ProcessRequest, rmi *ResultMessageInfo) error {
	start := time.Now()
	log.Info("[%s]start download model: %s(%s)", requestId, pr.ModelName, pr.ModelVersion)
	defer func() {
		rmi.DownloadCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]download model complete: %s(%s). cost: %s", requestId, pr.ModelName, pr.ModelVersion, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 从oss下载到本地目录
	err := c.DownloadDir(pr.Oss.Dir, localDir, pr.Oss.Concurrency, true)
	if err != nil {
		return errors.WithMessagef(err, "download model fail: %s", pr.Oss.Dir)
	}
	return nil
}
