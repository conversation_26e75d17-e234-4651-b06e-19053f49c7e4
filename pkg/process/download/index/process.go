package index

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/checksum"
	"dip-agent/pkg/util/gz"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/oss"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"strings"
	"time"
)

const (
	TypeName = "download-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	RequestId      string `json:"requestId"`
	IndexDbPath    string `json:"indexDbPath"`            // 索引存放目录
	OssPath        string `json:"ossPath"`                // oss上的索引路径
	DownloadCost   string `json:"downloadCost"`           // 下载索引的耗时
	DecompressCost string `json:"decompressCost"`         // 解压索引的耗时
	ChecksumCost   string `json:"checksumCost"`           // 校验索引的耗时
	MoveCost       string `json:"moveCost"`               // 移动索引的耗时
	IsBackup       bool   `json:"isBackup"`               // 是否使用了备份
	BackupCost     string `json:"backupCost,omitempty"`   // 备份索引的耗时
	IsUseRsync     bool   `json:"isUseRsync"`             // 是否使用rsync
	IsRsyncExist   bool   `json:"isRsyncExist,omitempty"` // 是否存在rsync命令
}

type ProcessRequest struct {
	IndexName                     string   `json:"indexName,omitempty" validate:"required"`
	IndexVersion                  string   `json:"indexVersion,omitempty" validate:"required"`
	DbPath                        string   `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`            // 引擎构建出的索引存放目录
	GzPath                        string   `json:"gzPath,omitempty" default:"/app/rel/data/gz" validate:"required"`             // 临时存放gz文件的目录
	Compress                      *bool    `json:"compress,omitempty" default:"false"`                                          // 是否压缩, 默认false
	CheckBuildResult              *bool    `json:"checkBuildResult,omitempty" default:"true"`                                   // 是否进行索引构建结果校验
	Checksum                      *bool    `json:"checksum,omitempty" default:"true"`                                           // 是否进行文件完整性校验
	ChecksumFileName              string   `json:"checksumFileName,omitempty" default:"checksum.json"`                          // 校验和文件名称
	Oss                           *dto.Oss `json:"oss,omitempty" validate:"required,dive"`                                      // oss相关配置
	EngineBuildResultFileName     string   `json:"engineBuildResultFileName,omitempty" default:"check.json"`                    // engine build结果文件名称
	AgentConfigDirName            string   `json:"agentConfigDirName,omitempty" default:"agent_config"`                         // agent配置文件目录名称
	IsReboot                      *bool    `json:"isReboot,omitempty" default:"false"`                                          // 是否重启更新
	Force                         *bool    `json:"force,omitempty" default:"false"`                                             // 是否强制下载覆盖本地索引
	IsVersionEqualCover           *bool    `json:"isVersionEqualCover,omitempty" default:"true"`                                // 本地存在相同版本, 是否覆盖下载(只针对热更新生效, 重启更新肯定覆盖)
	MemoryInflateFactor           int      `json:"memoryInflateFactor,omitempty" default:"5"`                                   // 索引加载到内存的膨胀比率, 默认5%
	MemoryUsageThreshold          int      `json:"memoryUsageThreshold,omitempty" default:"93"`                                 // 内存使用阈值, 默认90%
	NeedCommit                    *bool    `json:"needCommit" default:"true"`                                                   // 是否需要commit
	MoveWithRsync                 *bool    `json:"moveWithRsync,omitempty"`                                                     // 是否使用rsync移动索引
	BackupDiskUsageRatio          int      `json:"backupDiskUsageRatio"`                                                        // 集群索引备份磁盘使用率阈值
	BackupDir                     string   `json:"backupIndexDir,omitempty" default:"/app/rel/data/backup" validate:"required"` // 备份索引的路径
	IsRollback                    *bool    `json:"isRollback,omitempty"`                                                        // 是否回滚
	HotReloadRsyncMemoryThreshold int      `json:"hotReloadRsyncMemoryThreshold" default:"83"`                                  // 热更新时, 如果内存使用率超过该阈值, 则使用rsync移动索引
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	context, _ := json.MarshalToString(pr)
	log.Info("[%s]download index from oss request: %s", ctx.RequestId, context)

	// 执行下载任务
	rmi, err := RunTask(ctx.RequestId, pr)
	if err != nil {
		return api.UpdateFail[string](err)
	}

	// 响应内容
	resultMessageContent, _ := json.Marshal(rmi)
	return api.OfSuccessWithMessage[string]("download index success", string(resultMessageContent))
}

// RunTask 下载索引
func RunTask(requestId string, pr *ProcessRequest) (*ResultMessageInfo, error) {
	var (
		err error
		rmi = &ResultMessageInfo{}
	)
	// 本地索引目录
	localIndexDbPath := filepath.Join(pr.DbPath, pr.IndexName, pr.IndexVersion)
	rmi.IndexDbPath = localIndexDbPath
	rmi.OssPath = pr.Oss.Dir

	// oss client
	c, err := oss.NewClient(pr.Oss.Endpoint, pr.Oss.Bucket, pr.Oss.AccessKey, pr.Oss.SecretKey)
	if err != nil {
		return rmi, errors.WithMessage(err, "create oss client fail")
	}

	// 判断本地索引目录是否存在, 如果存在, 则进一步判断文件完整性是否一致, 避免重复下载
	isEqual := false
	isForce := pr.Force != nil && *pr.Force
	if !isForce && util.IsPathExist(localIndexDbPath) {
		// 本地和oss中的校验和一致,认为索引已经下载过
		isEqual, err = checksumEqualOss(localIndexDbPath, pr.Oss.Dir, pr.ChecksumFileName, c)
		if err != nil {
			log.Warn("[%s]check exist index file integrity fail: %+v", requestId, err)
		}
	}

	// 本地存在索引, 且和oss上的索引完整性一致, 则不需要重复下载
	if isEqual {
		log.Info("[%s]local index is equal to oss index, no need to download: %s", requestId, localIndexDbPath)
		return rmi, nil
	}

	// 如果是热更新, 并且同版本不覆盖, 并且本地存在同版本, 则不需要重新下载
	// 这里要反这来判断, 因为IsVersionEqualCover默认为true, nil也认为是true
	isVersionEqualNotCover := pr.IsVersionEqualCover != nil && !*pr.IsVersionEqualCover
	isReboot := pr.IsReboot != nil && *pr.IsReboot
	if !isReboot && isVersionEqualNotCover && util.IsPathExist(localIndexDbPath) {
		// 保证目录不为空
		isEmptyDir, err := util.IsEmptyDir(localIndexDbPath)
		if err != nil {
			return nil, errors.WithMessagef(err, "check index dir is empty fail: %s", localIndexDbPath)
		}
		if !isEmptyDir {
			log.Info("[%s]hot-reload local index is exist, no need to download", requestId)
			return rmi, nil
		}
		log.Info("[%s]hot-reload local index is exist, but is empty, need to download", requestId)
	}

	// 先下载到一个gz目录(可能需要解压),再移动到目标目录
	gzDir := filepath.Join(pr.GzPath, pr.IndexName, pr.IndexVersion)

	// 如果是回滚更新, 判断是否有备份版本
	isRollback := pr.IsRollback != nil && *pr.IsRollback
	backupSwitchSuccess := false
	if isRollback && pr.BackupDiskUsageRatio > 0 {
		// 使用备份索引，移动到 gzDir
		backupSwitchSuccess, err = BackupSwitch(requestId, pr.GzPath, pr.BackupDir, pr.IndexName, pr.IndexVersion, rmi)
		if err != nil {
			// 使用备份失败, 继续下载
			log.Warn("[%s]backup switch fail, continue download index: %s", requestId, err.Error())
			// 发送报警
			go platform.NewWarnRequestWithMsg(requestId, fmt.Sprintf("backup switch fail, continue download index: %s", err.Error())).Send()
		}
	}

	// 如果使用了备份, 则不需要下载
	if backupSwitchSuccess {
		log.Info("[%s]use backup index, no need to download", requestId)
	} else {
		err = downloadAndCheck(requestId, gzDir, c, pr, rmi)
		if err != nil {
			return rmi, err
		}
	}

	// 如果索引目录存在,先删除
	if util.IsPathExist(localIndexDbPath) {
		err = os.RemoveAll(localIndexDbPath)
		if err != nil {
			return nil, errors.WithMessagef(err, "remove local index dir fail: %s", localIndexDbPath)
		}
		log.Info("[%s]remove local index db dir: %s", requestId, localIndexDbPath)
	}

	// double check: 移动前校验目标目录内存是否足够
	afterIndexMemoryUsageRate, err := common.CheckLocalIndexMemory(requestId, gzDir, pr.DbPath, pr.MemoryInflateFactor, pr.MemoryUsageThreshold)
	if err != nil {
		return rmi, err
	}
	log.Info("[%s][%s]after index move, memory usage rate: %.2f%%", requestId, pr.IndexName, afterIndexMemoryUsageRate)

	// 移动到索引db目录
	err = moveIndex(requestId, gzDir, afterIndexMemoryUsageRate, pr, rmi)
	if err != nil {
		return rmi, err
	}
	return rmi, nil
}

func downloadAndCheck(requestId, gzDir string, c *oss.Client, pr *ProcessRequest, rmi *ResultMessageInfo) error {
	// 下载索引到本地
	err := downloadIndex(requestId, gzDir, c, pr, rmi)
	if err != nil {
		return err
	}

	// 检测是否空目录
	isEmptyDir, err := util.IsEmptyDir(gzDir)
	if err != nil {
		return errors.WithMessagef(err, "check index dir is empty fail: %s", gzDir)
	}
	if isEmptyDir {
		err = os.RemoveAll(gzDir)
		if err != nil {
			return errors.WithMessagef(err, "remove empty index dir fail: %s", gzDir)
		}
		return errors.New("index dir is empty: " + gzDir)
	}

	// (原地)解压缩
	if *pr.Compress {
		err = decompress(requestId, gzDir, pr, rmi)
		if err != nil {
			return errors.WithMessagef(err, "decompress index fail: %s", gzDir)
		}
	}

	// 校验check.json
	if *pr.CheckBuildResult {
		// 先判断是否有_SUCCESS文件, 如果有说明是老的脚本构建的, 直接认为成功
		successFilePath := filepath.Join(gzDir, "_SUCCESS")
		if util.IsPathExist(successFilePath) {
			log.Info("[%s]script build result file exist: %s", requestId, successFilePath)
		} else {
			buildResultFilePath := filepath.Join(gzDir, pr.EngineBuildResultFileName)
			_, err = common.CheckBuildResult(buildResultFilePath)
			if err != nil {
				return err
			}
		}
	}

	// 校验文件完整性
	if *pr.Checksum {
		start := time.Now()
		defer func() {
			rmi.ChecksumCost = util.DurationToHumanReadable(time.Since(start))
			log.Info("[%s]checksum index complete: %s. cost: %s", requestId, pr.IndexName, util.DurationToHumanReadable(time.Since(start)))
		}()

		isEqual, err := checksum.CheckEqualLocal(gzDir, pr.ChecksumFileName)
		if err != nil {
			return errors.WithMessage(err, "check index file integrity fail")
		}
		if !isEqual {
			return errors.New("index file integrity not equal")
		}
	}

	return nil
}

// 下载索引
func downloadIndex(requestId, localDir string, c *oss.Client, pr *ProcessRequest, rmi *ResultMessageInfo) error {
	start := time.Now()
	log.Info("[%s]start download index: %s", requestId, pr.IndexName)
	defer func() {
		rmi.DownloadCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]download index complete: %s. cost: %s", requestId, pr.IndexName, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 从oss下载到临时目录
	err := c.DownloadDir(pr.Oss.Dir, localDir, pr.Oss.Concurrency, true)
	if err != nil {
		if strings.Contains(err.Error(), pr.Oss.Dir) {
			return errors.WithMessage(err, "download index fail")
		}
		return errors.WithMessagef(err, "download index fail: %s", pr.Oss.Dir)
	}
	return nil
}

// 解压
func decompress(requestId, gzDir string, pr *ProcessRequest, rmi *ResultMessageInfo) error {
	start := time.Now()
	log.Info("[%s]start decompress index dir: %s", requestId, gzDir)
	defer func() {
		rmi.DecompressCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]decompress index complete: %s. cost: %s", requestId, pr.IndexName, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 重启更新的时候, 根据cpu核数解压. 0表示根据cpu核数解压
	concurrency := pr.Oss.Concurrency
	// 非重启更新, 只能单线程解压
	if !*pr.IsReboot && api.CurrentEnv.IsPrd() {
		concurrency = 1
	}
	log.Info("[%s]decompress index concurrency: %d", requestId, concurrency)
	return gz.DecompressGzipFiles(gzDir, true, func(fileName string) bool {
		return !strings.HasSuffix(fileName, pr.AgentConfigDirName)
	}, concurrency)
}

// 校验本地的校验和与oss上的校验和是否一致
func checksumEqualOss(localDir, ossDir, checksumFile string, c *oss.Client) (bool, error) {
	// 读取oss上的checksum信息
	ossCheckSumFile := filepath.Join(ossDir, checksumFile)
	ossCheckSumContent, err := c.ReadFileContent(ossCheckSumFile)
	if err != nil {
		return false, errors.WithMessagef(err, "read oss checksum file fail: %s", ossCheckSumFile)
	}
	// 反序列化
	var ossChecksumInfo checksum.Info
	err = json.Unmarshal(ossCheckSumContent, &ossChecksumInfo)
	if err != nil {
		return false, errors.Wrapf(err, "unmarshal oss checksum file fail: %s", ossCheckSumContent)
	}
	// 对比本地和oss上的校验和
	return checksum.EqualWith(localDir, checksumFile, &ossChecksumInfo)
}

// 移动到索引db目录
func moveIndex(requestId, gzDir string, afterIndexMemoryUsageRate float64, pr *ProcessRequest, rmi *ResultMessageInfo) error {
	start := time.Now()
	indexName := pr.IndexName
	dbPath := pr.DbPath
	indexDbDirWithoutVersion := filepath.Join(dbPath, indexName)
	log.Info("[%s]start move index dir: %s to %s", requestId, gzDir, indexDbDirWithoutVersion)
	defer func() {
		rmi.MoveCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]move index complete: %s. cost: %s", requestId, indexName, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 是否使用 rsync 命令移动索引
	isUseRsyncMoveIndex, isRsyncExist := IsUseRsync(requestId, afterIndexMemoryUsageRate, pr)
	rmi.IsUseRsync = isUseRsyncMoveIndex
	rmi.IsRsyncExist = isRsyncExist

	// 移动目录
	return moveDir(isUseRsyncMoveIndex, gzDir, indexDbDirWithoutVersion)
}

func moveDir(isUseRsyncMoveIndex bool, src, dst string) error {
	var err error

	// rsync命令复制慢但是内存占用小, 根据当前cpu/内存使用情况, 选择合适的复制方式(mv/rsync)
	if isUseRsyncMoveIndex {
		err = util.MoveDirByRsync(src, dst)
	} else {
		err = util.MoveDirByCmd(src, dst)
	}
	if err != nil {
		return errors.WithMessagef(err, "move index dir fail. src: %s, dest: %s", src, dst)
	}

	return nil
}

// IsUseRsync 是否使用rsync
func IsUseRsync(requestId string, afterIndexMemoryUsageRate float64, pr *ProcessRequest) (bool, bool) {
	isDgraph := pr.NeedCommit != nil && *pr.NeedCommit
	isUseRsyncMoveIndex := ShouldUseRsync(pr.MoveWithRsync, isDgraph, *pr.IsReboot, afterIndexMemoryUsageRate, pr.HotReloadRsyncMemoryThreshold)
	log.Info("[%s]isUseRsyncMoveIndex: %v", requestId, isUseRsyncMoveIndex)

	// 判断是否存在rsync命令
	isRsyncExist := util.IsCmdExist("rsync")
	if isUseRsyncMoveIndex && !isRsyncExist {
		log.Warn("[%s]rsync command not exist, use mv command", requestId)
		isUseRsyncMoveIndex = false
	}
	return isUseRsyncMoveIndex, isRsyncExist
}

// ShouldUseRsync 是否应该使用rsync移动索引
func ShouldUseRsync(moveWithRsync *bool, isDgraph, isReboot bool, afterIndexMemoryUsageRate float64, hotReloadRsyncMemoryThreshold int) bool {
	if moveWithRsync != nil {
		return *moveWithRsync
	}
	// 非Dgraph默认不使用rsync, 因为只有Dgraph使用了内存盘
	if !isDgraph {
		return false
	}

	// 目前只简单判断是否重启更新, 如果是重启更新, 则使用rsync
	if isReboot {
		return true
	}

	// 根据内存判断, 内存超过80%的情况下, 使用rsync
	if afterIndexMemoryUsageRate > float64(hotReloadRsyncMemoryThreshold) {
		return true
	}

	return false
}

func BackupSwitch(requestId, gzDir, backupDir, indexName, indexVersion string, rmi *ResultMessageInfo) (bool, error) {
	start := time.Now()
	backupIndexDir := filepath.Join(backupDir, indexName, indexVersion)
	log.Info("[%s]index backup enable, check backup index dir: %s", requestId, backupIndexDir)
	if !util.IsPathExist(backupIndexDir) {
		log.Info("[%s]backup index dir not exist. skip backup switch: %s", requestId, backupIndexDir)
		return false, nil
	}

	// 检查备份索引目录是否为空
	isEmptyDir, err := util.IsEmptyDir(backupIndexDir)
	if err != nil {
		log.Warn("[%s]check backup index dir is empty fail: %s", requestId, backupIndexDir)
		return false, err
	}
	if isEmptyDir {
		log.Warn("[%s]backup index dir is empty. skip backup switch. try to remove: %s", requestId, backupIndexDir)
		_ = os.RemoveAll(backupIndexDir)
		return false, nil
	}

	// 移动备份索引到 gzDir
	gzIndexDir := filepath.Join(gzDir, indexName)
	log.Info("[%s]backup index is exist, try to move backup index: from %s to %s", requestId, backupIndexDir, gzIndexDir)
	// 确保目录存在
	err = util.CreateDir(gzIndexDir, true)
	if err != nil {
		return false, err
	}
	// 移动目录, 移动到 gzDir 的时候直接使用 mv 命令
	err = util.MoveDirByCmd(backupIndexDir, gzIndexDir)
	if err != nil {
		return false, err
	}
	rmi.IsBackup = true
	rmi.BackupCost = util.DurationToHumanReadable(time.Since(start))
	return true, nil
}
