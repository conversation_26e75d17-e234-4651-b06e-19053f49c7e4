package index

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/sonic"
	"os"
	"path/filepath"
	"testing"
)

func TestBackupSwitch(t *testing.T) {
	tests := []struct {
		name         string
		requestId    string
		setupDirs    func(t *testing.T) (string, string, string) // 返回 gzDir, backupDir, indexName
		indexVersion string
		wantSuccess  bool
		wantErr      bool
		verify       func(t *testing.T, gzDir, backupDir string, rmi *ResultMessageInfo)
	}{
		{
			name:      "成功切换备份索引",
			requestId: "test-request-1",
			setupDirs: func(t *testing.T) (string, string, string) {
				// 创建临时目录
				tmpDir, err := os.MkdirTemp("", "backup_switch_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				gzDir := filepath.Join(tmpDir, "gz")
				backupDir := filepath.Join(tmpDir, "backup")
				indexName := "test_index"

				// 创建备份目录并添加测试文件
				backupIndexDir := filepath.Join(backupDir, indexName, "v1")
				if err := os.MkdirAll(backupIndexDir, 0755); err != nil {
					t.Fatalf("创建备份目录失败: %v", err)
				}
				if err := os.WriteFile(filepath.Join(backupIndexDir, "test.txt"), []byte("test content"), 0644); err != nil {
					t.Fatalf("创建测试文件失败: %v", err)
				}

				return gzDir, backupDir, indexName
			},
			indexVersion: "v1",
			wantSuccess:  true,
			wantErr:      false,
			verify: func(t *testing.T, gzDir, backupDir string, rmi *ResultMessageInfo) {
				// 验证备份目录是否已被移动
				backupPath := filepath.Join(backupDir, "test_index", "v1")
				if util.IsPathExist(backupPath) {
					t.Errorf("备份目录未被移动: %s", backupPath)
				}

				// 验证目标目录是否包含移动的文件
				gzPath := filepath.Join(gzDir, "test_index", "v1")
				testFile := filepath.Join(gzPath, "test.txt")
				if !util.IsPathExist(testFile) {
					t.Errorf("目标文件不存在: %s", testFile)
				}

				// 验证 ResultMessageInfo
				if !rmi.IsBackup {
					t.Error("IsBackup 应该为 true")
				}
				if rmi.BackupCost == "" {
					t.Error("BackupCost 不应该为空")
				}
			},
		},
		{
			name:      "备份目录不存在",
			requestId: "test-request-2",
			setupDirs: func(t *testing.T) (string, string, string) {
				tmpDir, err := os.MkdirTemp("", "backup_switch_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				gzDir := filepath.Join(tmpDir, "gz")
				backupDir := filepath.Join(tmpDir, "backup")
				if err := os.MkdirAll(gzDir, 0755); err != nil {
					t.Fatalf("创建gz目录失败: %v", err)
				}

				return gzDir, backupDir, "test_index"
			},
			indexVersion: "v1",
			wantSuccess:  false,
			wantErr:      false,
			verify: func(t *testing.T, gzDir, backupDir string, rmi *ResultMessageInfo) {
				if rmi.IsBackup {
					t.Error("IsBackup 应该为 false")
				}
				if rmi.BackupCost != "" {
					t.Error("BackupCost 应该为空")
				}
			},
		},
		{
			name:      "备份目录为空",
			requestId: "test-request-3",
			setupDirs: func(t *testing.T) (string, string, string) {
				tmpDir, err := os.MkdirTemp("", "backup_switch_test_*")
				if err != nil {
					t.Fatalf("创建临时目录失败: %v", err)
				}

				gzDir := filepath.Join(tmpDir, "gz")
				backupDir := filepath.Join(tmpDir, "backup")
				indexName := "test_index"

				// 创建空的备份目录
				backupIndexDir := filepath.Join(backupDir, indexName, "v1")
				if err := os.MkdirAll(backupIndexDir, 0755); err != nil {
					t.Fatalf("创建备份目录失败: %v", err)
				}

				return gzDir, backupDir, indexName
			},
			indexVersion: "v1",
			wantSuccess:  false,
			wantErr:      false,
			verify: func(t *testing.T, gzDir, backupDir string, rmi *ResultMessageInfo) {
				// 验证空目录是否被删除
				backupPath := filepath.Join(backupDir, "test_index", "v1")
				if util.IsPathExist(backupPath) {
					t.Error("空的备份目录应该被删除")
				}

				if rmi.IsBackup {
					t.Error("IsBackup 应该为 false")
				}
			},
		},
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试目录
			gzDir, backupDir, indexName := tt.setupDirs(t)
			defer os.RemoveAll(filepath.Dir(gzDir)) // 清理临时目录

			// 执行备份切换
			rmi := &ResultMessageInfo{}
			success, err := BackupSwitch(tt.requestId, gzDir, backupDir, indexName, tt.indexVersion, rmi)

			// 验证结果
			if success != tt.wantSuccess {
				t.Errorf("BackupSwitch() success = %v, wantSuccess %v", success, tt.wantSuccess)
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("BackupSwitch() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 执行额外的验证
			if tt.verify != nil {
				tt.verify(t, gzDir, backupDir, rmi)
			}
		})
	}
}
