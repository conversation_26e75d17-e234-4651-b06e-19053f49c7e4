package common

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/engine/model"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/retry"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/pkg/errors"
)

// CheckBuildResult 校验构建完后的check.json
func CheckBuildResult(buildResultFilePath string) (*engine.BuildEngineResponse, error) {
	// 检测是否写入了check.json
	if !util.IsPathExist(buildResultFilePath) {
		return nil, errors.Errorf("engine build index fail, check.json not exist: %s", buildResultFilePath)
	}
	// 将文件内容转为对象
	ber := &engine.BuildEngineResponse{}
	err := util.ReadFileTo(buildResultFilePath, ber)
	if err != nil {
		err = errors.WithMessagef(err, "read build-engine result file(check.json) fail: %s", buildResultFilePath)
		return nil, err
	}
	// 检查结果的响应头
	header := ber.Header
	if header == nil {
		return nil, errors.Errorf("engine build index fail, engine result file header is nil: %s", buildResultFilePath)
	}
	// 检查build-engine的构建结果
	if !header.IsSuccess() {
		err = errors.Errorf("engine build index fail: %s", header.ErrMsg())
		return nil, err
	}
	buildResult := ber.BuildResult
	if buildResult == nil {
		return nil, errors.Errorf("engine build index fail, build result is nil: %s", buildResultFilePath)
	}
	return ber, nil
}

// GetServingVersion 获取节点指定索引的运行版本
func GetServingVersion(requestId, taskId string) ([]string, error) {
	execParam, err := util.GetExecParam(requestId, taskId)
	if err != nil {
		return nil, err
	}
	servingVersions := execParam.ServingVersions
	return servingVersions, nil
}

// GetIndexSize 获取指定目录中的索引大小
func GetIndexSize(indexDir, buildResultFileName string) (int64, error) {
	var (
		err       error
		indexSize int64
	)
	indexSize, err = GetIndexSizeByBuildResult(indexDir, buildResultFileName)
	if err != nil || indexSize == 0 {
		if err != nil {
			log.Warn("get index size by build result fail: %+v", err)
		}
		// 可能老的脚本方式构建, 读取目录大小作为索引大小
		indexSize, err = util.GetDirSizeWith(indexDir, func(path string) bool {
			return !strings.HasSuffix(path, ".dsk")
		})
	}
	return indexSize, err
}

// GetLocalRemoveIndexSize 获取本地需要删除的索引的大小
func GetLocalRemoveIndexSize(requestId, indexVersion, indexDbDir, engineBuildResultFileName string, isReboot, isDoubleVersion bool, servingVersions []string) (int64, error) {
	exist := util.IsPathExist(indexDbDir)
	if !exist {
		log.Warn("[%s]local index dir not exist: %s", requestId, indexDbDir)
		return 0, nil
	}
	// 获取目录中的版本
	localVersions, err := util.GetSubdirectories(indexDbDir)
	if err != nil {
		return 0, errors.WithMessagef(err, "get local index versions fail: %s", indexDbDir)
	}
	if len(localVersions) == 0 {
		log.Warn("[%s]local index dir is empty: %s", requestId, indexDbDir)
		return 0, nil
	}

	// 获取最小的索引
	var minIndexSize int64

	// 如果是双版本, 获取过期的索引大小
	if isDoubleVersion {
		for _, localVersion := range localVersions {
			if !util.ContainsString(servingVersions, localVersion) {
				indexDir := filepath.Join(indexDbDir, localVersion)
				indexSize, err := GetIndexSize(indexDir, engineBuildResultFileName)
				if err != nil {
					log.Warn("get index size fail: %+v", err)
					indexSize = 0
				}
				log.Info("[%s]local index size: %s = %d", requestId, indexDir, indexSize)
				if minIndexSize == 0 || indexSize < minIndexSize {
					minIndexSize = indexSize
				}
			} else if isReboot && localVersion == indexVersion { // 重启更新的时候, 如果是同版本更新, 则需要减去同版本的索引大小
				indexDir := filepath.Join(indexDbDir, localVersion)
				indexSize, err := GetIndexSize(indexDir, engineBuildResultFileName)
				if err != nil {
					log.Warn("get index size fail: %+v", err)
				}
				log.Info("[%s]local index size: %s = %d", requestId, indexDir, indexSize)
				if minIndexSize == 0 || indexSize < minIndexSize {
					minIndexSize = indexSize
				}
			}
		}
	} else {
		for _, localVersion := range localVersions {
			indexDir := filepath.Join(indexDbDir, localVersion)
			indexSize, err := GetIndexSize(indexDir, engineBuildResultFileName)
			if err != nil {
				log.Warn("get index size fail: %+v", err)
				indexSize = 0
			}
			log.Info("[%s]local index size: %s = %d", requestId, indexDir, indexSize)
			if minIndexSize == 0 || indexSize < minIndexSize {
				minIndexSize = indexSize
			}
		}
	}

	return minIndexSize, nil
}

// GetIndexSizeByBuildResult 获取指定目录中的索引大小
func GetIndexSizeByBuildResult(indexDir, buildResultFileName string) (int64, error) {
	// 读取目录中的check.json获取索引大小
	buildResultFilePath := filepath.Join(indexDir, buildResultFileName)
	if !util.IsPathExist(buildResultFilePath) {
		return 0, nil
	}
	log.Info("buildResultFilePath: %s", buildResultFilePath)
	// 将文件内容转为对象
	ber := &engine.BuildEngineResponse{}
	err := util.ReadFileTo(buildResultFilePath, ber)
	if err != nil {
		return 0, errors.WithMessagef(err, "read build result filee fail: %s", buildResultFilePath)
	}
	// 检查结果的响应头
	header := ber.Header
	if header == nil {
		return 0, errors.New("engine result file header is nil")
	}
	// 检查build-engine的构建结果
	if !header.IsSuccess() {
		return 0, errors.Errorf("engine build result fail: %s", header.ErrMsg())
	}
	buildResult := ber.BuildResult
	if buildResult == nil {
		return 0, errors.Errorf("engine build result is nil: %#v", ber)
	}
	return buildResult.IndexTotalBytes, nil
}

// CheckLocalIndexMemory 校验本地索引加载后内存是否足够
// Parameters:
//   - localIndexDir: 本地索引目录, 通常为gz目录
//   - indexDbPath: 索引db目录, 通常为cdb目录
//   - memoryInflateFactor: 索引加载到内存的膨胀比率, 默认5%
//   - memoryUsageThreshold: 内存使用阈值, 默认90%
//
// Returns:
//   - float64: 索引加载后的内存使用率
//   - error: 错误信息
func CheckLocalIndexMemory(requestId, localIndexDir, indexDbPath string, memoryInflateFactor, memoryUsageThreshold int) (float64, error) {
	// 获取索引目录大小, 排除磁盘索引文件
	localIndexSize, err := util.GetDirSizeWith(localIndexDir, func(path string) bool {
		return !strings.HasSuffix(path, ".dsk")
	})
	if err != nil {
		return 0, errors.WithMessagef(err, "get index dir size fail: %s", localIndexDir)
	}
	indexBytes := uint64(localIndexSize)

	// 获取当前已经使用内存大小
	memLimit, memUsed, err := GetMemoryUsage(requestId, indexDbPath)

	// 获取rss大小
	rssMemory := util.GetRSSMemory()
	// 计算索引加载到内存需要的大小: 索引加载到内存会膨胀约5%的大小
	indexMemorySize := indexBytes + indexBytes*uint64(memoryInflateFactor)/100

	var (
		afterIndexMemoryUsageRate float64
		memoryUsageInfo           string
	)

	afterIndexMemoryUsageRate = float64(memUsed+indexMemorySize+uint64(rssMemory)) * 100 / float64(memLimit)
	// 这里不需要减去老索引的大小, 因为本地cdb目录中的索引已经删除
	memoryUsageInfo = fmt.Sprintf("(memoryUsedSize[%s] + indexSize[%s] + rssMemory[%s]) / totalMemorySize[%s] = memoryUsageRate[%.2f%%] %s threshold[%d%%]",
		util.ByteToHumanReadable(int64(memUsed)),
		util.ByteToHumanReadable(int64(indexMemorySize)),
		util.ByteToHumanReadable(rssMemory),
		util.ByteToHumanReadable(int64(memLimit)),
		afterIndexMemoryUsageRate,
		func() string {
			if afterIndexMemoryUsageRate > float64(memoryUsageThreshold) {
				return ">"
			}
			return "<="
		}(),
		memoryUsageThreshold)

	log.Info("[%s]index memory check info: %s", requestId, memoryUsageInfo)

	if afterIndexMemoryUsageRate > float64(memoryUsageThreshold) {
		return afterIndexMemoryUsageRate, errors.Errorf("memory space not enough!!! %s", memoryUsageInfo)
	}
	return afterIndexMemoryUsageRate, nil
}

// GetMemoryUsage 获取当前已使用内存和总内存, 优先使用wss内存
func GetMemoryUsage(requestId, indexDbPath string) (memLimit uint64, memUsed uint64, err error) {
	memLimit, memUsed, err = util.MemoryUsage()
	if err != nil {
		return 0, 0, errors.WithMessage(err, "get memory usage fail")
	}
	usedWssMemory, totalWssMemory, err := util.GetWSSMemory(int64(memLimit))
	if err != nil {
		log.Warn("[%s]get wss memory fail: %s", requestId, err)
	} else {
		log.Info("[%s]get wss memory: used: %d, total: %d", requestId, usedWssMemory, totalWssMemory)
		if usedWssMemory >= 0 {
			memUsed = uint64(usedWssMemory)

			// 排除 disk 索引占用的空间
			diskIndexSize, err := calcDiskIndexSize(indexDbPath)
			if err != nil {
				log.Warn("[%s]calc disk index size fail: %s", requestId, err)
			} else {
				log.Info("[%s]total disk index size: %d", requestId, diskIndexSize)
				// note: 这里存在减法, 不能使用uint64, 否则可能会溢出
				//memUsed -= uint64(diskIndexSize)
				afterDiskIndexSize := int64(memUsed) - diskIndexSize
				if afterDiskIndexSize < 0 {
					log.Warn("[%s]after minus disk index size < 0. menUsed: %s, diskIndexSize: %s", requestId, util.ByteToHumanReadable(int64(memUsed)), util.ByteToHumanReadable(diskIndexSize))
					memUsed = 0
				} else {
					memUsed = uint64(afterDiskIndexSize)
				}
			}
		}
		if totalWssMemory > 0 && uint64(totalWssMemory) < memLimit {
			memLimit = uint64(totalWssMemory)
		}
	}
	return memLimit, memUsed, nil
}

// 计算磁盘索引文件占用的大小
// 计算 dbPath 下的索引的.dsk 的文件大小
func calcDiskIndexSize(dbPath string) (int64, error) {
	// 循环遍历 dpPath 下的索引的.dsk 文件, 计算文件大小
	var (
		indexSize int64
	)

	err := filepath.Walk(dbPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}
		if strings.HasSuffix(info.Name(), ".dsk") {
			indexSize += info.Size()
		}
		return nil
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return indexSize, nil
}

// checkIndexUnloadWithExternal 使用外部方法检查索引是否卸载完成
func checkIndexUnloadWithExternal(requestId, indexName, indexVersion string, timeout, sleep time.Duration) error {
	// 查询引擎中的索引状态
	start := time.Now()
	exist := false
	for time.Since(start) < timeout {
		engineIndexStatusResp, err := engine.NewIndexStatusRequest(requestId, nil).Send()
		if err != nil {
			err = errors.WithMessage(err, "call engine index status failed")
			log.Error("[%s]call engine index status failed: %v", requestId, err)
			// TODO 发送报警
			return err
		}
		engineIndexInfos := engineIndexStatusResp.IndexInfos
		if len(engineIndexInfos) > 0 {
			for _, engineIndexInfo := range engineIndexInfos {
				if engineIndexInfo.IndexName == indexName && engineIndexInfo.IndexVersion == indexVersion {
					exist = true
				}
			}
		}

		if !exist {
			return nil
		}
		log.Info("[%s][%s(%s)]index unload external check failed. retry after: %ds. timeout: %ds", requestId, indexName, indexVersion, int(sleep.Seconds()), int(timeout.Seconds()))
		time.Sleep(sleep)
	}
	if exist {
		return errors.Errorf("[%s(%s)]index unload external check timeout: %ds", indexName, indexVersion, int(timeout.Seconds()))
	}
	return nil
}

// CheckIndexUnload 检查索引卸载是否完成
// return: 是否需要sleep, 检查只有通过新接口才能完全确认索引已经卸载，才不需要后续的sleep来等待索引彻底卸载释放
func CheckIndexUnload(requestId, indexName, indexVersion string, timeout, sleep time.Duration) (needSleep bool, err error) {
	// 先用外部校验
	err = checkIndexUnloadWithExternal(requestId, indexName, indexVersion, timeout, sleep)
	if err != nil {
		return true, err
	}

	// 等待索引加入到延迟释放队列
	time.Sleep(sleep)

	// 由于Dgraph索引卸载是异步的，因此需要使用新的接口检查索引是否实际卸载完成
	// 尝试3次，每次间隔5秒
	retryCount := 3
	log.Info("[%s]Trying to check if index %s(%s) is unloaded using new API", requestId, indexName, indexVersion)
	unloadIndexCheckRequest := engine.NewUnloadIndexCheckRequest(requestId, indexName, indexVersion)
	for i := 0; i < retryCount; i++ {
		resp, err := unloadIndexCheckRequest.Send()
		if err != nil {
			// 可能集群还未发布校验代码，容忍
			log.Info("[%s]Index %s(%s) unload check failed and ignored: %v", requestId, indexName, indexVersion, err)
			return true, nil
		}

		success := resp.IsUnloaded != nil && *resp.IsUnloaded
		if success {
			log.Info("[%s]Index %s(%s) unload check successful using new API", requestId, indexName, indexVersion)
			return false, nil
		}

		log.Info("[%s]Index %s(%s) unload check attempt %d/%d failed, retrying in %s...", requestId, indexName, indexVersion, i+1, retryCount, sleep.String())
		time.Sleep(sleep)
	}

	// 尝试3次都失败，返回错误
	return true, errors.Errorf("index %s(%s) unload check failed after %d attempts", indexName, indexVersion, retryCount)
}

// UnloadIndex 调用引擎接口unload索引
func UnloadIndex(requestId, indexName, indexVersion string) error {
	// 先校验索引是否在追增量(Dgraph卸载正在追增量的索引会导致引擎服务挂掉)
	eof, err := IndexIncIsEOF(requestId, indexName, indexVersion, 120, 10*time.Minute, 5*time.Second)
	if err != nil {
		return err
	}
	if !eof {
		return errors.Errorf("index inc is not EOF, cannot unload index: %s(%s)", indexName, indexVersion)
	}

	// 调用引擎接口unload索引
	err = retry.DoRetry(func() error {
		_, err := engine.NewUnloadIndexRequest(requestId, indexName, indexVersion).Send()
		return err
	})
	if err != nil {
		err = errors.WithMessagef(err, "engine unload index fail, index: %s(%s)", indexName, indexVersion)
		return err
	}
	return nil
}

// IndexIncIsEOF 索引的追增量是否已经完成
func IndexIncIsEOF(requestId, indexName, indexVersion string, incOffsetThreshold int, timeout, sleepTime time.Duration) (bool, error) {
	start := time.Now()
	for {
		timeOffset, err := GetIndexTimeOffset(requestId, indexName, indexVersion)
		if err != nil {
			return false, err
		}
		log.Info("[%s]current index inc offset: %d, index: %s(%s)", requestId, timeOffset, indexName, indexVersion)
		if timeOffset < int64(incOffsetThreshold) {
			log.Info("[%s]index inc offset is less than threshold: %d < %d, index: %s(%s)", requestId, timeOffset, incOffsetThreshold, indexName, indexVersion)
			return true, nil
		}
		log.Info("[%s]wait inc offset: %d, retry after %ds. timeout: %ds", requestId, timeOffset, int(sleepTime.Seconds()), int(timeout.Seconds()))
		time.Sleep(sleepTime)

		if time.Since(start) > timeout {
			log.Error("[%s][%s(%s)]index wait inc timeout: current timeOffset: %d > threshold: %d. timeout threshold: %ds", requestId, indexName, indexVersion, timeOffset, incOffsetThreshold, int(timeout.Seconds()))
			return false, nil
		}
	}
}

// GetIndexTimeOffset 获取当前索引的追增量
func GetIndexTimeOffset(requestId, indexName, indexVersion string) (int64, error) {
	// 调用引擎接口获取索引的追增量信息
	infos, err := GetIndexIncStatus(requestId, &engine.BaseIndexInfo{
		IndexName:    indexName,
		IndexVersion: indexVersion,
	})
	if err != nil {
		return 0, err
	}
	if len(infos) == 0 {
		log.Info("[%s]call engine no index inc info found: %s(%s)", requestId, indexName, indexVersion)
		return 0, nil
	}

	// 获取当前索引的追增量信息
	for _, incInfo := range infos {
		if incInfo.IndexName == indexName {
			if incInfo.EOF {
				return 0, nil
			}
			if incInfo.IndexVersion == indexVersion && incInfo.IndexSource == "formal" {
				return 0, nil
			}
			// buffer and not EOF
			return incInfo.TimeOffset, nil
		}
	}

	return 0, errors.Errorf("call engine no index found in inc status: %s(%s)", indexName, indexVersion)
}

// GetIndexIncStatus 调用引擎接口获取索引的追增量信息
func GetIndexIncStatus(requestId string, indexInfo *engine.BaseIndexInfo) ([]engine.IndexIncInfo, error) {
	var ii []engine.BaseIndexInfo
	if indexInfo != nil {
		ii = []engine.BaseIndexInfo{
			{
				IndexName:    indexInfo.IndexName,
				IndexVersion: indexInfo.IndexVersion,
			},
		}
	} else {
		ii = []engine.BaseIndexInfo{}
	}
	er := engine.NewIncStatusRequest(requestId, ii)
	resp, err := er.Send()
	if err != nil {
		err = errors.WithMessagef(err, "failed to get index inc status: %s(%s)", indexInfo.IndexName, indexInfo.IndexVersion)
		return nil, err
	}

	return resp.IndexIncInfos, nil
}

// BackupLatestVersionIndex 备份最新的索引版本
func BackupLatestVersionIndex(dbDir, backupDir, indexName string, backupDiskUsageRatio, totalDiskUsageLimit int) (string, error) {
	// 读取localIndexDbPath目录下的所有文件夹, 作为索引版本
	indexDbDir := filepath.Join(dbDir, indexName)
	localVersions, err := util.GetSubdirectories(indexDbDir)
	if err != nil {
		return "", errors.WithMessagef(err, "get subdirectories failed. dir: %s", indexDbDir)
	}
	log.Info("local index versions: %s", localVersions)
	if len(localVersions) == 0 {
		log.Warn("local index dir is empty. skip backup. dir: %s", indexDbDir)
		return "", nil
	}
	// 获取最新的索引版本
	latestVersion := localVersions[len(localVersions)-1]
	return latestVersion, BackupIndex(dbDir, backupDir, indexName, latestVersion, backupDiskUsageRatio, totalDiskUsageLimit)
}

// BackupIndex 备份索引
// dbDir: 索引目录(cdb目录)
// backupDir: 备份目录
// indexName: 待备份的索引名称
// indexVersion: 待备份的索引版本
// buildResultFileName: 构建结果文件名, 通常为check.json
func BackupIndex(dbDir, backupDir, indexName, indexVersion string, backupDiskUsageRatio, totalDiskUsageLimit int) error {
	// 校验备份目录,不能是dbDir及其子目录
	if backupDir == dbDir {
		return errors.New("backup dir can not be cdb dir")
	}
	isSub, err := util.IsSubdirectory(dbDir, backupDir)
	if err != nil {
		return errors.WithMessagef(err, "check backup dir failed. dir: %s", backupDir)
	}
	if isSub {
		return errors.New("backup dir can not be subdirectory of cdb dir")
	}

	// 如果待备份的索引目录不存在, 则不备份
	srcIndexDir := filepath.Join(dbDir, indexName, indexVersion)
	if !util.IsPathExist(srcIndexDir) {
		log.Warn("index dir not exist. skip backup. dir: %s", srcIndexDir)
		return nil
	}

	// 如果待备份的索引是空目录则跳过
	srcExist := util.IsPathExist(srcIndexDir)
	if !srcExist {
		log.Warn("src index dir not exist. skip backup. dir: %s", srcIndexDir)
		return nil
	}
	isEmpty, err := util.IsEmptyDir(srcIndexDir)
	if err != nil {
		return errors.WithMessagef(err, "check empty dir failed. dir: %s", srcIndexDir)
	}
	if isEmpty {
		log.Warn("src index dir is empty, skip backup. dir: %s", srcIndexDir)
		return nil
	}

	// 如果备份目录不存在, 则创建
	if !util.IsPathExist(backupDir) {
		if err := os.MkdirAll(backupDir, 0755); err != nil {
			return errors.WithMessagef(err, "create backup dir failed. dir: %s", backupDir)
		}
		log.Info("create backup dir success. dir: %s", backupDir)
	}

	// 如果现在磁盘的空间就不够, 则不备份
	diskTotal, diskUsed, err := util.DiskUsage(backupDir)
	if err != nil {
		return errors.WithMessagef(err, "get disk usage fail: %s", backupDir)
	}
	// 判断当前备份磁盘使用率是否已超过阈值
	if diskUsed > (diskTotal * uint64(totalDiskUsageLimit) / 100) {
		return errors.Errorf("backupDir disk usage is over threshold: %d%%", totalDiskUsageLimit)
	}

	// 计算备份目录磁盘+当前要备份的索引目录大小, 是否超过阈值
	exceed, msg, err := util.CheckDiskUsageAfterMove(srcIndexDir, backupDir, backupDiskUsageRatio, totalDiskUsageLimit)
	if exceed {
		// 如果超过阈值, 则删除最旧的备份版本, 直到磁盘空间够用
		for {
			// 获取备份目录下的所有文件夹, 作为索引版本
			backupIndexs, err := util.GetSubdirectories(backupDir)
			if err != nil {
				return errors.WithMessagef(err, "get subdirectories failed. dir: %s", backupDir)
			}
			log.Info("backup indexs: %s", backupIndexs)
			if len(backupIndexs) > 0 {
				// 获取最旧的索引
				oldestBackupIndexName := backupIndexs[0]
				oldestBackupIndexDir := filepath.Join(backupDir, oldestBackupIndexName)
				// 删除最旧的备份索引
				if err = os.RemoveAll(oldestBackupIndexDir); err != nil {
					return errors.WithMessagef(err, "remove oldest backup index dir failed. dir: %s", oldestBackupIndexDir)
				}
				log.Info("remove oldest backup index dir success. dir: %s", oldestBackupIndexDir)
				// 重新计算备份目录磁盘使用率
				exceed, msg, err = util.CheckDiskUsageAfterMove(srcIndexDir, backupDir, backupDiskUsageRatio, totalDiskUsageLimit)
				if err != nil {
					return errors.WithMessage(err, "check disk usage after move failed")
				}
				if !exceed {
					break
				}
			} else {
				// 如果备份目录下没有索引了，磁盘仍旧不够用, 则报警
				return errors.Errorf("disk usage is over threshold: %s", msg)
			}
		}
	}

	// 创建索引备份目录
	backupIndexDir := filepath.Join(backupDir, indexName)
	err = util.CreateDir(backupIndexDir, true)
	if err != nil {
		return errors.WithMessagef(err, "create backup index dir failed. dir: %s", backupIndexDir)
	}

	// 将最新索引版本移动到备份目录
	if err := util.MoveDirByCmd(srcIndexDir, backupIndexDir); err != nil {
		return errors.WithMessagef(err, "move backup index dir failed. src: %s, dst: %s", srcIndexDir, backupIndexDir)
	}
	return nil
}

// GetUsedMemoryByEngineInfos 根据引擎中的索引信息和模型信息获取当前已经使用的内存
func GetUsedMemoryByEngineInfos(requestId string) (int64, error) {
	var usedMemory int64

	// 调用引擎接口获取索引信息
	engineIndexStatusResp, err := engine.NewIndexStatusRequest(requestId, nil).Send()
	if err != nil {
		return 0, err
	}
	engineIndexInfos := engineIndexStatusResp.IndexInfos
	for _, indexInfo := range engineIndexInfos {
		indexMemorySize := indexInfo.MemoryBytes
		usedMemory += indexMemorySize
	}

	// 调用引擎接口获取模型信息
	engineModelStatusResp, err := model.NewModelStatusRequest(requestId, nil).Send()
	if err != nil {
		return 0, err
	}
	modelInfos := engineModelStatusResp.ModelInfos
	for _, modelInfo := range modelInfos {
		modelMemorySize := modelInfo.MemoryBytes
		usedMemory += modelMemorySize
	}
	return usedMemory, nil
}

// CheckModelUnload 检查模型卸载是否完成
func CheckModelUnload(requestId, modelName, modelVersion string, timeout, sleep time.Duration) error {
	// 查询引擎中的模型状态
	start := time.Now()
	exist := false
	modelStatusRequest := model.NewModelStatusRequest(requestId, nil)
	for time.Since(start) < timeout {
		engineModelStatusResp, err := modelStatusRequest.Send()
		if err != nil {
			err = errors.WithMessage(err, "call engine model status failed")
			log.Error("[%s]call engine model status failed: %v", requestId, err)
			return err
		}
		modelInfos := engineModelStatusResp.ModelInfos
		if len(modelInfos) > 0 {
			for _, engineModelInfo := range modelInfos {
				if engineModelInfo.ModelName == modelName && engineModelInfo.ModelVersion == modelVersion {
					exist = true
				}
			}
		}

		if !exist {
			return nil
		}
		log.Info("[%s][%s(%s)]model unload external check failed. retry after: %ds. timeout: %ds", requestId, modelName, modelVersion, int(sleep.Seconds()), int(timeout.Seconds()))
		time.Sleep(sleep)
	}
	if exist {
		return errors.Errorf("[%s(%s)]model unload external check timeout: %ds", modelName, modelVersion, int(timeout.Seconds()))
	}
	return nil
}

// GetRemoveModelSize 获取需要删除的模型的大小
func GetRemoveModelSize(requestId, modelName string, isDoubleVersion bool, servingVersions []string) (int64, error) {
	// 获取最小的模型
	var minModelSize int64

	// 调用引擎接口获取模型信息
	engineModelStatusResp, err := model.NewModelStatusRequest(requestId, nil).Send()
	if err != nil {
		return 0, err
	}
	modelInfos := engineModelStatusResp.ModelInfos

	// 获取最小的模型
	if isDoubleVersion {
		for _, modelInfo := range modelInfos {
			if modelInfo.ModelName == modelName {
				if util.ContainsString(servingVersions, modelInfo.ModelVersion) {
					continue
				}
				modelMemorySize := modelInfo.MemoryBytes
				if minModelSize == 0 || modelMemorySize < minModelSize {
					minModelSize = modelMemorySize
				}
			}
		}
	} else {
		for _, modelInfo := range modelInfos {
			if modelInfo.ModelName == modelName {
				modelMemorySize := modelInfo.MemoryBytes
				if minModelSize == 0 || modelMemorySize < minModelSize {
					minModelSize = modelMemorySize
				}
			}
		}
	}

	return minModelSize, nil
}

// CheckTask 校验指定的任务是否属于当前集群
func CheckTask(requestId, taskId string) error {
	// 调用平台接口校验
	checkTaskResp, err := platform.NewCheckTaskRequest(requestId, taskId).SendWithRetry()
	if err != nil {
		return err
	}
	if !checkTaskResp.Success {
		return errors.Errorf("check task in cluster fail: %s", checkTaskResp.Reason)
	}
	return nil
}

// GetIndexVersions 获取指定索引在引擎中的版本
func GetIndexVersions(requestId, indexName string) ([]string, error) {
	indexStatusRequest := engine.NewIndexStatusRequest(requestId, nil)
	versions := make([]string, 0)
	engineIndexStatusResp, err := indexStatusRequest.SendWithRetry()
	if err != nil {
		return nil, err
	}

	engineIndexInfos := engineIndexStatusResp.IndexInfos
	for _, engineIndexInfo := range engineIndexInfos {
		if engineIndexInfo.IndexName == indexName {
			versions = append(versions, engineIndexInfo.IndexVersion)
		}
	}

	// 去重
	versions = util.RemoveDuplicate(versions)
	return versions, nil
}
