package dump

import (
	"dip-agent/pkg/consts"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/dump"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"strings"
	"time"
)

const (
	TypeName = "dump"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	IndexName          string                 `json:"indexName,omitempty" validate:"required"`
	IndexVersion       string                 `json:"indexVersion,omitempty" validate:"required"`
	DumpProcessorName  string                 `json:"dumpProcessorName" validate:"required"`                                // dump processor name
	SpecialDeal        string                 `json:"specialDeal,omitempty"`                                                // 字段特殊处理
	DataPath           string                 `json:"dataPath,omitempty" default:"/app/rel/data/tdata" validate:"required"` // 拉取的odps的数据存放目录
	DbPath             string                 `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`     // 引擎构建出的索引存放目录
	ConfigPath         string                 `json:"configPath,omitempty" default:"/app/rel/config" validate:"required"`   // 配置目录
	ShardNum           int32                  `json:"shardNum"`                                                             // 分片数量
	ShardID            int32                  `json:"shardId"`                                                              // 分片号
	IndexDef           *dto.IndexDef          `json:"indexDef" validate:"dive,required"`                                    // 索引定义
	Odps               *dto.Odps              `json:"odps,omitempty" validate:"required,dive"`                              // odps配置
	DumpResultFileName string                 `json:"dumpResultFileName,omitempty" default:"dump-result.json"`              // dump结果文件名称
	IndexExtraConfig   map[string]interface{} `json:"indexExtraConfig"`                                                     // 索引额外配置
	EnableEmptyData    *bool                  `json:"enableEmptyData,omitempty" default:"false"`                            // 是否允许空数据
	AgentConfig        cfg.CommonCfg          `json:"agentConfig" validate:"required"`                                      // agent相关配置

	// 这一期以下字段不使用
	SegmentNum int32 `json:"segmentNum"` // segment数量
	//SegmentFileCount []int32      `json:"segmentFileCount"`                                  // 每个分段的数据文件数量, 第i个表示第i段中的数据文件数量

	//DumpServiceConfigFileName string   `json:"dumpServiceConfigFileName,omitempty" default:"dump-request.json"` // dump service的配置文件名称
	//DumpServiceOssPath        string   `json:"dumpServiceOssPath,omitempty" validate:"required"`                // dump service的oss路径
	//DumpServiceLocalDir       string   `json:"dumpServiceLocalDir,omitempty" default:"/app/rel/dump"`           // dump service的本地目录
	//DumpServiceCmdPath        string   `json:"dumpServiceCmdPath,omitempty" default:"java" validate:"required"` // dump service的运行命令path
	//DumpServiceCmdArgs        []string `json:"dumpServiceCmdArgs,omitempty"`                                    // dump service的运行参数, 除了-jar /xxx/dump-service.jar之外的参数
}

type CheckJson struct {
	EmptyCount    int64  `json:"emptyCount"`      // 空的记录数
	DsCount       int64  `json:"dsCount"`         // 数据源记录数
	IncTimeOffset int64  `json:"inc_time_offset"` // 增量时间偏移量
	Size          int64  `json:"size"`            // 数据大小
	Count         int64  `json:"count"`           // 记录总数
	SyncTime      int64  `json:"sync_time"`       // 同步时间戳
	Version       string `json:"version"`         // 版本号
	FileSize      int64  `json:"file_size"`       // 文件大小
}

func (pr *ProcessRequest) toDumpInfo() *dump.Info {
	di := &dump.Info{
		IndexName:          pr.IndexName,
		IndexVersion:       pr.IndexVersion,
		IndexType:          pr.IndexDef.IndexType,
		DumpProcessorName:  pr.DumpProcessorName,
		SpecialDeal:        pr.SpecialDeal,
		Sql:                pr.Odps.Sql,
		Hints:              pr.Odps.Hints,
		DataPath:           pr.DataPath,
		OdpsProject:        pr.Odps.Project,
		OdpsEndpoint:       pr.Odps.Endpoint,
		OdpsAccessKey:      pr.Odps.AccessKey,
		OdpsSecretKey:      pr.Odps.SecretKey,
		TableName:          pr.Odps.TableName,
		TunnelEndpoint:     pr.Odps.TunnelEndpoint,
		KeyFieldName:       pr.IndexDef.KeyFieldName,
		KeyFieldType:       pr.IndexDef.KeyFieldType,
		ShardNum:           pr.ShardNum,
		ShardID:            pr.ShardID,
		PartitionField:     pr.Odps.PartitionField,
		PartitionCondition: pr.Odps.PartitionCondition,
		DumpMethod:         pr.Odps.DumpMethod,
		AgentConfig:        pr.AgentConfig,
	}

	// shard key 优先级: dump_hash_field > hash_field > key_field_name
	if len(pr.IndexExtraConfig) > 0 {
		if shardKeyObj, exist := pr.IndexExtraConfig["dump_hash_field"]; exist {
			if shardKey, ok := shardKeyObj.(string); ok && shardKey != "" {
				di.ShardKey = shardKey
			}
		}

		if di.ShardKey == "" {
			if shardKeyObj, exist := pr.IndexExtraConfig["hash_field"]; exist {
				if shardKey, ok := shardKeyObj.(string); ok && shardKey != "" {
					di.ShardKey = shardKey
				}
			}
		}

		if di.ShardKey == "" {
			di.ShardKey = pr.IndexDef.KeyFieldName
		}
	}

	// dump method
	if di.DumpMethod == "" && len(pr.IndexExtraConfig) > 0 {
		if indexDumpMethodObj, exist := pr.IndexExtraConfig["dumpMethod"]; exist {
			if indexDumpMethod, ok := indexDumpMethodObj.(string); ok && indexDumpMethod != "" {
				di.DumpMethod = indexDumpMethod
			}
		}
	}

	// TODO 目前推荐设置的是1,因为推荐只能针对一个文件构建. 为了保障拉取的性能, 拉取仍然多线程拉取写入多个文件,然后由agent合并为一个文件
	if pr.Odps.Concurrency > 1 {
		di.ThreadNum = pr.Odps.Concurrency
	}
	return di
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

// Process run dump-service
func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	start := time.Now()
	defer func() {
		// 耗时
		log.Info("[%s]dump table data cost time: %s", ctx.RequestId, util.DurationToHumanReadable(time.Since(start)))
	}()

	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.FailWithCode[string](api.BuildException_InvalidParam, err)
	}
	prContent, _ := json.Marshal(pr)
	log.Info("[%s]dump request: %s", ctx.RequestId, string(prContent))

	// 卡点校验: 当前任务是否属于当前集群
	err = common.CheckTask(ctx.RequestId, ctx.TaskId)
	if err != nil {
		return api.DumpFail[string](err)
	}

	// 校验磁盘是否可读
	checkDir := filepath.Dir(pr.DataPath)
	err = util.CheckDirRW(checkDir)
	if err != nil {
		return api.DumpFail[string](err)
	}

	// 清空索引的数据目录
	indexDataPath := filepath.Join(pr.DataPath, pr.IndexName, pr.IndexVersion)
	err = os.RemoveAll(indexDataPath)
	if err != nil {
		err = errors.Wrapf(err, "remove index data path fail: %s", indexDataPath)
		return api.DumpFail[string](err)
	}

	// 清空索引目录
	indexDbPath := filepath.Join(pr.DbPath, pr.IndexName, pr.IndexVersion)
	err = os.RemoveAll(indexDbPath)
	if err != nil {
		err = errors.Wrapf(err, "remove index db path fail: %s", indexDbPath)
		return api.BuildFail[string](err)
	}

	// run dump-service
	dumpServiceInfo := pr.toDumpInfo()
	// 构建dump的时候超时时间设置的长一点
	dumpServiceInfo.WaitOdpsSuccessTimeout = 2 * time.Hour
	// 构建dump的时候不设置超时
	//dumpServiceInfo.WaitOdpsSuccessTimeout = -1
	dumpServiceInitStart := time.Now()
	dumpService, err := dump.NewService(ctx.RequestId, dumpServiceInfo)
	dumpServiceInitCost := time.Since(dumpServiceInitStart)
	if err != nil {
		// 创建odps client超时
		if strings.Contains(err.Error(), "context deadline exceeded") {
			err = errors.WithMessage(err, "create dump service fail: odps client timeout")
			return api.FailWithCode[string](api.UpdateException_OdpsClientTimeout, err)
		}
		return api.DumpFail[string](errors.WithMessage(err, "create dump service fail"))
	}
	total := int64(dumpService.Total())
	if total == 0 {
		err = errors.Errorf("no data to dump: %s(%s)", pr.IndexName, pr.IndexVersion)
		return api.FailWithCode[string](api.DumpException_NoData, err)
	}
	tr, err := dumpService.SimpleDump()
	if err != nil {
		err = errors.WithMessage(err, "dump service fail")
		return api.DumpFail[string](err)
	}
	log.Info("[%s]dump table data success: %+v", ctx.RequestId, tr)
	// 写入dump-result.json
	dumpInfoContent, err := json.Marshal(dumpServiceInfo)
	if err != nil {
		err = errors.WithMessagef(err, "marshal dump request fail: %+v", dumpServiceInfo)
		return api.DumpFail[string](err)
	}
	// 将dump请求信息也写入dump-result.json
	tr.DumpInfo = string(dumpInfoContent)
	dumpResultFilePath := filepath.Join(pr.ConfigPath, pr.IndexName, pr.IndexVersion, pr.DumpResultFileName)
	err = util.WriteJsonToFile(dumpResultFilePath, tr, false)
	if err != nil {
		err = errors.WithMessagef(err, "write dump result file fail: %s", dumpResultFilePath)
		return api.DumpFail[string](err)
	}
	log.Info("[%s]write dump result file success: %s", ctx.RequestId, dumpResultFilePath)

	// 检查实际拉取的数据量
	// TODO 2024-11-18 存在为空数据的场景: 例如黑名单表
	//if tr.Count == 0 {
	//	err = errors.Errorf("no data to dump: emptyCount: %d. index: %s(%s)", tr.EmptyCount, pr.IndexName, pr.IndexVersion)
	//	return api.FailWithCode[string](api.DumpException_NoData, err)
	//}
	// 拉取的数据为0的时候, 创建0.done空文件以免后面构建索引失败
	if tr.Count == 0 {
		if *pr.EnableEmptyData {
			// TODO 发送告警
			emptyDataFilePath := filepath.Join(pr.DataPath, pr.IndexName, pr.IndexVersion, "0.done")
			// 如果文件不存在, 创建文件
			if !util.IsPathExist(emptyDataFilePath) {
				err = util.WriteRawToFile(emptyDataFilePath, []byte("{}"))
				//err = util.CreateFile(emptyDataFilePath)
				if err != nil {
					err = errors.WithMessagef(err, "create empty data file fail: %s", emptyDataFilePath)
					return api.DumpFail[string](err)
				}
				tr.FileCount = 1
			}
		} else {
			err = errors.Errorf("no data to dump: emptyCount: %d. index: %s(%s)", tr.EmptyCount, pr.IndexName, pr.IndexVersion)
			return api.FailWithCode[string](api.DumpException_NoData, err)
		}
	}

	// 检查实际拉取的数据量是否等于分区总数
	if !dumpService.IsHashFilter() && total > 0 && total != tr.Count+tr.EmptyCount {
		err = errors.Errorf("dump data count not equal to total: total(%d) != dump count(%d). index: %s(%s)", tr.Count+tr.EmptyCount, total, pr.IndexName, pr.IndexVersion)
		return api.DumpFail[string](err)
	}

	sdr := tr.ToSimpleDumpResult()
	sdr.DumpServiceInitCost = util.DurationToHumanReadable(dumpServiceInitCost)
	sdr.Sql = pr.Odps.Sql

	//  如果并行度为1,即需求1个数据文件,需要进行文件合并
	if pr.Odps.Concurrency == 1 && tr.FileCount > 1 {
		mergeStart := time.Now()
		log.Info("[%s]start merge files: %s(%s)", ctx.RequestId, pr.IndexName, pr.IndexVersion)
		// data dir
		dataDir := filepath.Join(pr.DataPath, pr.IndexName, pr.IndexVersion)
		// 校验当前磁盘空间是否足够. 合并期间数据文件会翻倍
		// 2025-02-07 合并修改为顺序合并并删除源文件, 不会翻倍
		//err := checkDisk(ctx.RequestId, pr.IndexName, pr.IndexVersion, dataDir)
		//if err != nil {
		//	log.Warn("[%s]check disk fail: %s", ctx.RequestId, err)
		//}
		//  合并文件
		err = util.MergeFilesByOrder(dataDir, tr.FileCount)
		if err != nil {
			err = errors.WithMessagef(err, "merge files fail: %s", dataDir)
			return api.DumpFail[string](err)
		}
		sdr.MergeCost = util.DurationToHumanReadable(time.Since(mergeStart))
	}

	// TODO 构建步骤中读取文件而非内存, 避免恢复操作会跳过此步骤
	ctx.Carrier.Put("fileCount", tr.FileCount)
	ctx.Carrier.Put("docNum", tr.Count)
	ctx.Carrier.Put(consts.ReportDumpCount, tr.Count)
	// dgraph需要写入到.schema
	ctx.Carrier.Put("fileSize", tr.Size)

	// 写入dump的check.json(兼容原有dgraph逻辑)
	checkJson := &CheckJson{
		EmptyCount: tr.EmptyCount,
		DsCount:    tr.QueryCount,
		Size:       tr.Size,
		Count:      tr.Count,
		Version:    pr.IndexDef.IndexVersion,
		FileSize:   tr.Size,
		SyncTime:   time.Now().UnixMilli(),
	}
	if *pr.IndexDef.IsInc {
		incConfig := pr.IndexDef.IncConfig
		if incConfig == nil {
			return api.DumpFail[string](errors.New("inc config is nil"))
		}
		checkJson.IncTimeOffset = incConfig.StartTimestamp
	}
	checkFilePath := filepath.Join(pr.DataPath, pr.IndexName, pr.IndexVersion, "check.json")
	err = util.WriteJsonToFile(checkFilePath, checkJson, false)
	if err != nil {
		err = errors.WithMessagef(err, "write check.json file fail: %s", checkFilePath)
		return api.DumpFail[string](err)
	}

	// 检查当前构建容器的内存规格是否满足索引构建的内存需求
	err = checkMemory(tr.Size, sdr)
	if err != nil {
		return api.DumpFail[string](err)
	}

	// 将dump结果写入result message
	var resultMessage string
	content, err := json.Marshal(sdr)
	if err == nil {
		resultMessage = string(content)
	}
	return api.OfSuccessWithMessage[string]("dump index table data success", resultMessage)
}

// 校验当前磁盘空间是否足够. 合并期间数据文件会翻倍
func checkDisk(requestId, indexName, indexVersion, dataDir string) error {
	diskTotal, diskUsed, err := util.DiskUsage(dataDir)
	if err != nil {
		log.Warn("[%s]get disk usage fail: %s", requestId, err)
	}
	log.Info("[%s]disk usage: total: %d, used: %d", requestId, diskTotal, diskUsed)
	needDisk := diskUsed * 2
	if diskTotal < needDisk {
		warnMsg := fmt.Sprintf("[%s][%s(%s)]index dump disk space is not enough: total: %d, need: %d", requestId, indexName, indexVersion, diskTotal, needDisk)
		log.Warn(warnMsg)
		// 发送报警
		_, err := platform.NewWarnRequestWithMsg(requestId, warnMsg).Send()
		if err != nil {
			log.Warn("[%s]send warn message fail: %s", requestId, err)
		}
	}
	return err
}

// 检查当前构建容器的内存规格是否满足索引构建的内存需求
func checkMemory(dumpIndexSize int64, sdr *dto.SimpleDumpResult) error {
	memLimit, _, err := util.MemoryUsage()
	if err != nil {
		return errors.WithMessage(err, "get memory usage fail")
	}
	if memLimit < uint64(dumpIndexSize) {
		errMsg := fmt.Sprintf("current memory limit is %d, less than dump index size %d", memLimit, dumpIndexSize)
		sdr.Warn = errMsg
		log.Warn(errMsg)
		//return errors.New(errMsg)
	}
	return nil
}
