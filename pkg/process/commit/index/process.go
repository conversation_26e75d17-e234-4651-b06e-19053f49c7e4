package index

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/retry"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"time"
)

const (
	TypeName = "commit"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	IndexName            string        `json:"indexName,omitempty" validate:"required"`
	IndexVersion         string        `json:"indexVersion,omitempty" validate:"required"`
	DbPath               string        `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`            // 引擎构建出的索引存放目录
	GzPath               string        `json:"gzPath,omitempty" default:"/app/rel/data/gz" validate:"required"`             // 临时存放gz文件的目录
	IsDoubleVersion      *bool         `json:"isDoubleVersion" validate:"required" default:"false"`                         // 是否双版本索引
	NeedCommit           *bool         `json:"needCommit" default:"true"`                                                   // 是否需要commit
	CommitSleepTime      int           `json:"commitSleepTime" default:"18"`                                                // commit后的等待时间, 单位秒.等待引擎延迟释放(dgraph的设定)
	ServingVersions      []string      `json:"servingVersions" validate:"required,min=1"`                                   // 索引最近更新成功的n个版本(即双版本索引应该保留的n个版本). 默认为n=2
	SleepTimeAfterUnload time.Duration `json:"sleepTimeAfterUnload" default:"18s"`                                          // 卸载索引后等待时间
	DiskUsageThreshold   int           `json:"diskUsageThreshold,omitempty" default:"93"`                                   // 磁盘使用阈值, 默认90%
	BackupDiskUsageRatio int           `json:"backupDiskUsageRatio"`                                                        // 集群索引备份磁盘使用率阈值
	BackupDir            string        `json:"backupIndexDir,omitempty" default:"/app/rel/data/backup" validate:"required"` // 备份索引的路径
	IsRollback           *bool         `json:"isRollback,omitempty"`                                        // 是否回滚
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	log.Info("[%s]engine index commit request: %+v", ctx.RequestId, pr)

	needCommit := pr.NeedCommit != nil && *pr.NeedCommit
	if needCommit {
		// 调用commit接口
		err := retry.DoRetry(func() error {
			_, err := engine.NewCommitRequest(ctx.RequestId, pr.IndexName, pr.IndexVersion).Send()
			return err
		})
		if err != nil {
			return api.UpdateFail[string](err)
		}

		// Dgraph的设定, commit后等待引擎延迟释放
		sleepTime := pr.SleepTimeAfterUnload
		log.Info("[%s]wait for index protection time end: sleep %s", ctx.RequestId, sleepTime.String())
		time.Sleep(sleepTime)
	}

	// 清除索引的老版本
	servingVersions := pr.ServingVersions
	indexDbDir := filepath.Join(pr.DbPath, pr.IndexName)
	outdatedVersions, err := util.GetOutdatedVersionWithServingVersions(pr.DbPath, pr.IndexName, servingVersions)
	if err != nil {
		return api.UpdateFail[string](err)
	}
	if len(outdatedVersions) > 0 {
		isRollback := pr.IsRollback != nil && *pr.IsRollback
		// 如果开启了索引备份且非回滚的情况下, 则备份最近的索引版本
		if pr.BackupDiskUsageRatio > 0 && !isRollback {
			backupIndexVersion := outdatedVersions[len(outdatedVersions)-1]
			err := common.BackupIndex(pr.DbPath, pr.BackupDir, pr.IndexName, backupIndexVersion, pr.BackupDiskUsageRatio, pr.DiskUsageThreshold)
			if err != nil {
				// 备份失败只发送报警
				log.Error("[%s]backup index failed: %s. err: %v", ctx.RequestId, pr.IndexName, err)
				go platform.NewWarnRequestWithMsg(ctx.RequestId, fmt.Sprintf("backup index failed: %s. err: %s", pr.IndexName, err)).Send()
			} else {
				backUpIndexPath := filepath.Join(pr.BackupDir, pr.IndexName, backupIndexVersion)
				log.Info("[%s]backup index success: %s", ctx.RequestId, backUpIndexPath)
			}
		}

		for _, outdatedVersion := range outdatedVersions {
			localIndexDbPath := filepath.Join(indexDbDir, outdatedVersion)
			err = os.RemoveAll(localIndexDbPath)
			if err != nil && !os.IsNotExist(err) {
				log.Warn("[%s]remove outdated index version fail: %s. err: %+v", ctx.RequestId, localIndexDbPath, err)
			} else {
				log.Info("[%s]remove outdated index version success: %s", ctx.RequestId, localIndexDbPath)
			}
		}
	}

	// 清理gz目录
	gzPath := filepath.Join(pr.GzPath, pr.IndexName)
	err = os.RemoveAll(gzPath)
	if err != nil && !os.IsNotExist(err) {
		log.Warn("[%s]remove gz path fail: %s. err: %+v", ctx.RequestId, gzPath, err)
	} else {
		log.Info("[%s]remove gz path success: %s", ctx.RequestId, gzPath)
	}

	return api.OfSuccess("engine commit success")
}
