package uploadindex

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/checksum"
	"dip-agent/pkg/util/gz"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/oss"
	"github.com/pkg/errors"
	"path/filepath"
	"strings"
	"time"
)

const (
	TypeName = "upload-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessage struct {
	RequestId            string `json:"requestId"`
	OssPath              string `json:"ossPath"`
	CopyConfigCost       string `json:"copyConfigCost"`
	GenerateChecksumCost string `json:"generateChecksumCost"`
	CompressCost         string `json:"compressCost"`
	UploadCost           string `json:"uploadCost"`
}

type ProcessRequest struct {
	IndexName                   string       `json:"indexName,omitempty" validate:"required"`
	IndexVersion                string       `json:"indexVersion,omitempty" validate:"required"`
	DataPath                    string       `json:"dataPath,omitempty" default:"/app/rel/data/tdata" validate:"required"`   // 拉取的odps的数据存放目录
	DbPath                      string       `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`       // 引擎构建出的索引存放目录
	LogPath                     string       `json:"logPath,omitempty" default:"/logs/engine" validate:"required"`           // 引擎构建日志存放目录
	ConfigPath                  string       `json:"configPath,omitempty" default:"/app/rel/config" validate:"required"`     // 配置目录
	IndexDef                    dto.IndexDef `json:"indexDef" validate:"dive,required"`                                      // 索引定义
	CleanDataDir                bool         `json:"cleanDataDir,omitempty" default:"true"`                                  // 是否清空dataPath目录, 不清理将断点续传拉取odps数据
	Oss                         dto.Oss      `json:"oss,omitempty" validate:"required,dive"`                                 // oss配置
	Compress                    bool         `json:"compress,omitempty" default:"false"`                                     // 是否压缩, 默认false
	Checksum                    *bool        `json:"checksum,omitempty" default:"true"`                                      // 是否进行文件完整性校验
	ChecksumFileName            string       `json:"checksumFileName,omitempty" default:"checksum.json"`                     // 校验和文件名称
	DumpResultFileName          string       `json:"dumpResultFileName,omitempty" default:"dump-result.json"`                // dump结果文件名称
	EngineBuildResultFileName   string       `json:"engineBuildResultFileName,omitempty" default:"check.json"`               // engine build结果文件名称
	EngineBuildConfigFileName   string       `json:"engineBuildConfigFileName,omitempty" default:"build-request.json"`       // engine build的配置文件名称
	UploadLog                   *bool        `json:"uploadLog,omitempty" default:"true"`                                     // 是否上传日志
	AgentConfigDirName          string       `json:"agentConfigDirName,omitempty" default:"agent_config"`                    // agent配置文件目录名称
	EngineBuildLogFileName      string       `json:"engineBuildLogFileName,omitempty" default:"engine-build.log"`            // engine build日志文件名称
	InnerEngineBuildLogFileName string       `json:"innerEngineBuildLogFileName,omitempty" default:"inner-engine-build.log"` // 内部engine build日志文件名称, 用来输出build-engin的标准输出
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

// Process run upload index
func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	requestId := ctx.RequestId
	start := time.Now()
	defer func() {
		// 耗时
		log.Info("[%s]upload index cost time: %s", requestId, util.DurationToHumanReadable(time.Since(start)))
	}()

	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.BuildFail[string](err)
	}

	rm := &ResultMessage{
		RequestId: requestId,
		OssPath:   pr.Oss.Dir,
	}

	// 本地索引目录
	localIndexDir := filepath.Join(pr.DbPath, pr.IndexName, pr.IndexVersion)
	// 配置文件目录
	localIndexConfigDir := filepath.Join(pr.ConfigPath, pr.IndexName, pr.IndexVersion, pr.AgentConfigDirName)
	// 创建目录
	err = util.CreateDir(localIndexConfigDir, false)
	if err != nil {
		err = errors.WithMessagef(err, "create config dir fail: %s", localIndexConfigDir)
		return api.BuildFail[string](err)
	}

	// 复制配置文件帮助排查问题
	err = copyConfigFiles(pr, localIndexConfigDir, rm)
	if err != nil {
		return api.BuildFail[string](err)
	}

	// 生成校验和文件
	if *pr.Checksum {
		err = generateCheckSum(pr, localIndexDir, rm)
		if err != nil {
			return api.BuildFail[string](err)
		}
	}

	// 压缩索引目录
	if pr.Compress {
		err = compress(pr, localIndexDir, rm)
		if err != nil {
			return api.BuildFail[string](err)
		}
	}

	// 上传索引目录
	// 校验oss dir格式是否合法
	if !strings.Contains(pr.Oss.Dir, pr.IndexName+"/"+pr.IndexVersion) {
		err = errors.Errorf("oss dir must contain indexName and indexVersion: %s, index: %s(%s)", pr.Oss.Dir, pr.IndexName, pr.IndexVersion)
		return api.BuildFail[string](err)
	}
	err = uploadDir(pr.Oss, localIndexDir, rm)
	if err != nil {
		return api.BuildFail[string](err)
	}

	// 上传索引配置目录
	indexConfigOss := pr.Oss
	indexConfigOss.Dir = strings.Replace(pr.Oss.Dir, "dip_new", "dip_config", 1)
	err = uploadDir(indexConfigOss, localIndexConfigDir, rm)
	if err != nil {
		return api.BuildFail[string](err)
	}

	// 返回结果
	var resultMessage string
	resultMessageContent, err := json.Marshal(rm)
	if err == nil {
		resultMessage = string(resultMessageContent)
	}
	return api.OfSuccessWithMessage[string]("upload index success", resultMessage)
}

// 将一些配置文件和日志文件复制到索引目录中
func copyConfigFiles(pr *ProcessRequest, localIndexConfigDir string, rm *ResultMessage) error {
	start := time.Now()
	defer func() {
		rm.CopyConfigCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]copy config files complete. cost: %s", rm.RequestId, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 复制一些配置文件帮忙排查问题
	// 复制agent日志
	if *pr.UploadLog {
		// 日志文件保存路径
		localLogDir := filepath.Join(localIndexConfigDir, "log")
		// agent日志文件
		agentLogFilePath := log.FilePath()
		// 复制引擎日志: 将pr.LogPath中的日志文件全部复制过去
		//err = util.CopyDir(pr.LogPath, localIndexConfigDir, func(fileName string) bool {
		//	return strings.HasSuffix(fileName, ".log")
		//}, true)
		// 引擎构建日志文件
		engineLogFilePath := filepath.Join(pr.LogPath, pr.EngineBuildLogFileName)
		// inner engine build log
		innerEngineBuildLogFilePath := filepath.Join(log.Dir(), pr.InnerEngineBuildLogFileName)

		// 复制文件
		err := util.CopyFilesToDir([]string{agentLogFilePath, engineLogFilePath, innerEngineBuildLogFilePath}, localLogDir)
		if err != nil {
			return errors.WithMessagef(err, "copy log file fail: %s", localIndexConfigDir)
		}

		// 默认压缩agent相关的日志文件, 即localIndexConfigDir目录中的.log文件
		err = gz.CompressGzipFiles(localLogDir, true, func(fileName string) bool {
			// 判断日志文件大小是否超过1M
			return strings.HasSuffix(fileName, ".log") && util.FileSize(filepath.Join(localLogDir, fileName)) > 1024*1024
		})
		// err = gz.CompressFilesInDir(localLogDir, nil, true)
		if err != nil {
			return errors.WithMessagef(err, "compress log file fail: %s", localIndexConfigDir)
		}
	}

	// 复制引擎构建的请求json
	engineConfigFilePath := filepath.Join(pr.ConfigPath, pr.IndexName, pr.IndexVersion, pr.EngineBuildConfigFileName)
	// 复制dump结果文件
	dumpResultFilePath := filepath.Join(pr.ConfigPath, pr.IndexName, pr.IndexVersion, pr.DumpResultFileName)
	// 复制文件
	err := util.CopyFilesToDir([]string{engineConfigFilePath, dumpResultFilePath}, localIndexConfigDir)
	if err != nil {
		return errors.WithMessagef(err, "copy file fail: %s", localIndexConfigDir)
	}

	return nil
}

// 生成校验和文件
func generateCheckSum(pr *ProcessRequest, localIndexDir string, rm *ResultMessage) error {
	start := time.Now()
	defer func() {
		rm.GenerateChecksumCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]generate checksum file complete: %s. cost: %s", rm.RequestId, pr.ChecksumFileName, util.DurationToHumanReadable(time.Since(start)))
	}()

	err := checksum.GenerateAndWriteToFile(localIndexDir, pr.ChecksumFileName, func(path string) bool {
		return !strings.HasSuffix(path, pr.ChecksumFileName) && !strings.HasSuffix(path, ".gz") && !strings.HasSuffix(path, ".log")
	})
	return errors.WithMessagef(err, "generate checksum file fail: %s", localIndexDir)
}

// 压缩目录中的文件
func compress(pr *ProcessRequest, localIndexDir string, rm *ResultMessage) error {
	start := time.Now()
	defer func() {
		rm.CompressCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]compress dbPath complete: %s. cost: %s", rm.RequestId, localIndexDir, util.DurationToHumanReadable(time.Since(start)))
	}()

	excludes := []string{
		pr.ChecksumFileName,
		pr.EngineBuildResultFileName,
		pr.DumpResultFileName,
		pr.AgentConfigDirName,
		"index.metadata",
		"schema.json",
		"_SUCCESS",
	}
	err := gz.CompressGzipFiles(localIndexDir, true, func(fileName string) bool {
		return !util.ContainsString(excludes, fileName)
	})
	if err != nil {
		return errors.WithMessagef(err, "compress dbPath fail: %s", localIndexDir)
	}
	return nil
}

// 上传索引目录
func uploadDir(ossConfig dto.Oss, localIndexDir string, rm *ResultMessage) error {
	start := time.Now()
	defer func() {
		rm.UploadCost = util.DurationToHumanReadable(time.Since(start))
		log.Info("[%s]upload index to oss complete: %s. cost: %s", rm.RequestId, ossConfig.Dir, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 初始化oss client
	c, err := oss.NewClient(ossConfig.Endpoint, ossConfig.Bucket, ossConfig.AccessKey, ossConfig.SecretKey)
	if err != nil {
		return errors.WithMessage(err, "create oss client fail")
	}

	// oss中如果存在该目录, 先删除
	exist, err := c.IsDirExist(ossConfig.Dir)
	if err != nil {
		return errors.WithMessagef(err, "check oss dir exist fail: %s", ossConfig.Dir)
	}
	if exist {
		err = c.DeleteDir(ossConfig.Dir)
		if err != nil {
			return errors.WithMessagef(err, "delete oss dir fail: %s", ossConfig.Dir)
		}
		log.Info("[%s]delete oss dir: %s", rm.RequestId, ossConfig.Dir)
	}
	// 创建oss目录
	err = c.CreateDir(ossConfig.Dir)
	if err != nil {
		return errors.WithMessagef(err, "create oss dir fail. oss dir: %s", ossConfig.Dir)
	}
	log.Info("[%s]create oss dir: %s", rm.RequestId, ossConfig.Dir)

	// 上传到oss
	err = c.UploadDir(localIndexDir, ossConfig.Dir)
	if err != nil {
		return errors.WithMessagef(err, "upload index to oss fail: %s", ossConfig.Dir)
	}
	return nil
}
