package scan

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/sonic"
	"dip-agent/pkg/util/yaml"
	"os"
	"path/filepath"
	"testing"
)

func TestTargetScanProcessor(t *testing.T) {
	// 初始化日志
	log.InitDefaultLogger()

	// 初始化JSON引擎
	json.SetDefaultEngine(sonic.Name)

	// 创建测试目录
	testDir := "./test_target_scan"
	defer os.RemoveAll(testDir)

	// 创建测试的target文件
	targetFile := filepath.Join(testDir, "target.yml")
	err := createTestTargetFile(targetFile)
	if err != nil {
		t.Fatalf("create test target file failed: %v", err)
	}

	// 创建测试的索引目录和构建结果文件
	indexDbPath := filepath.Join(testDir, "cdb")
	err = createTestIndexData(indexDbPath)
	if err != nil {
		t.Fatalf("create test index data failed: %v", err)
	}

	// 创建processor（使用新的函数版本）
	processor := process.NewFuncProcessor[ProcessRequest, ScanResult](
		"test",
		TypeName,
		RunTask,
	)

	// 准备请求参数
	request := ProcessRequest{
		TargetFilePath:  targetFile,
		IndexDbPath:     indexDbPath,
		BuildResultFile: "check.json",
	}

	carrier, err := cfg.NewCommonCfgWithJson(request)
	if err != nil {
		t.Fatalf("create carrier failed: %v", err)
	}

	// 创建处理上下文
	ctx := &api.ProcessContext{
		RequestId: "test-request-123",
		Carrier:   carrier,
	}

	// 执行处理
	result := processor.Process(ctx)

	// 检查结果
	if !result.Status().IsSuccess() {
		t.Fatalf("process failed: %v", result.Error())
	}

	scanResult := result.Data().(ScanResult)
	t.Logf("Scan result: %+v", scanResult)

	// 验证结果
	if scanResult.TotalIndexes != 2 {
		t.Errorf("expected 2 total indexes, got %d", scanResult.TotalIndexes)
	}

	if scanResult.UpdateIndexes == 0 && scanResult.SkippedIndexes == 0 {
		t.Error("expected some indexes to be processed")
	}

	// 检查更新列表
	for _, updateInfo := range scanResult.UpdateList {
		t.Logf("Update needed for %s: %v", updateInfo.IndexName, updateInfo.UpdateReason)
	}

	// 检查跳过列表
	for _, skipInfo := range scanResult.SkippedList {
		t.Logf("Skipped %s: %v", skipInfo.IndexName, skipInfo.UpdateReason)
	}
}

func createTestTargetFile(filePath string) error {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	err := os.MkdirAll(dir, 0755)
	if err != nil {
		return err
	}

	// 创建测试target数据
	target := &dto.Target{
		Id:         1,
		Env:        "test",
		MustFinish: false,
		Cluster: &dto.ClusterInfo{
			Id:         1,
			Name:       "test-cluster",
			Group:      "test-group",
			EngineType: "Dsearch",
		},
		TargetInfo: []*dto.TargetItem{
			{
				Name: "test-index-1",
				Version: []dto.Version{
					{
						DataVersion:  "20250128_120000",
						BuildVersion: "1001",
					},
					{
						DataVersion:  "20250128_120000",
						BuildVersion: "1002",
					},
				},
				Type:       dto.Index,
				UpdateType: dto.HotReload,
			},
			{
				Name: "test-index-2",
				Version: []dto.Version{
					{
						DataVersion:  "20250128_130000",
						BuildVersion: "2001",
					},
				},
				Type:       dto.Model,
				UpdateType: dto.RebootReload,
			},
		},
	}

	// 写入YAML文件
	yamlData, err := yaml.Marshal(target)
	if err != nil {
		return err
	}
	return os.WriteFile(filePath, yamlData, 0644)
}

func createTestIndexData(indexDbPath string) error {
	// 创建索引目录结构
	indexes := []struct {
		name     string
		version  string
		buildVer string
	}{
		{"test-index-1", "20250128_110000", "1000"}, // 版本不匹配，需要更新
		{"test-index-2", "20250128_130000", "2001"}, // 版本匹配，不需要更新
	}

	for _, idx := range indexes {
		indexDir := filepath.Join(indexDbPath, idx.name, idx.version)
		err := os.MkdirAll(indexDir, 0755)
		if err != nil {
			return err
		}

		// 创建构建结果文件
		buildResult := &engine.BuildEngineResponse{
			Header: &engine.BaseResponse{
				Code:    200, // 使用正确的成功状态码
				Message: "success",
			},
			BuildResult: &engine.BuildResult{
				IndexName:       idx.name,
				IndexVersion:    idx.version,
				IndexTotalBytes: 1024000,
				IndexTotalCount: 10000,
				BuildCostMs:     5000,
				BuildVersion:    idx.buildVer,
			},
		}

		buildResultPath := filepath.Join(indexDir, "check.json")
		buildResultData, err := json.Marshal(buildResult)
		if err != nil {
			return err
		}

		err = os.WriteFile(buildResultPath, buildResultData, 0644)
		if err != nil {
			return err
		}
	}

	return nil
}
