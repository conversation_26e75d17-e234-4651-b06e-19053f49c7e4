package scan

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"fmt"
	"github.com/pkg/errors"
	"path/filepath"
)

const (
	TypeName = "target-scan-index" // 扫描target文件并对比索引版本
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return process.NewFuncProcessor[ProcessRequest, ScanResult](
		info.PipelineName,
		TypeName,
		RunTask,
	)
}

// ProcessRequest 处理请求参数
type ProcessRequest struct {
	TargetFilePath  string      `json:"targetFilePath" validate:"required"`                          // 必填. target文件路径
	Target          *dto.Target `json:"target" validate:"required,dive"`                             // 必填. target信息
	IndexDbPath     string      `json:"indexDbPath" default:"/app/rel/data/cdb" validate:"required"` // 索引数据库路径
	BuildResultFile string      `json:"buildResultFile" default:"check.json" validate:"required"`    // 构建结果文件名
}

// IndexUpdateInfo 索引更新信息
type IndexUpdateInfo struct {
	IndexName      string         `json:"indexName"`           // 索引名称
	TargetVersion  []dto.Version  `json:"targetVersion"`       // 目标索引版本
	LocalVersion   []dto.Version  `json:"localVersion"`        // 本地版本
	CurrentVersion []string       `json:"currentVersion"`      // 当前版本, 双版本索引可能有多个版本在服务
	UpdateReason   string         `json:"updateReason"`        // 更新原因
	NeedUpdate     bool           `json:"needUpdate"`          // 是否需要更新
	Exception      string         `json:"exception,omitempty"` // 异常信息
	Type           dto.ItemType   `json:"type"`                // item类型
	UpdateType     dto.UpdateType `json:"updateType"`          // 更新类型
}

// ScanResult 扫描结果
type ScanResult struct {
	TotalIndexes   int                `json:"totalIndexes"`   // 总索引数量
	UpdateIndexes  int                `json:"updateIndexes"`  // 需要更新的索引数量
	UpdateList     []*IndexUpdateInfo `json:"updateList"`     // 需要更新的索引列表
	ExceptionCount int                `json:"exceptionCount"` // 异常的索引数量
	ExceptionList  []*IndexUpdateInfo `json:"exceptionList"`  // 异常的索引列表
	SkippedIndexes int                `json:"skippedIndexes"` // 跳过的索引数量
	SkippedList    []*IndexUpdateInfo `json:"skippedList"`    // 跳过的索引列表
}

// RunTask 处理target扫描请求的函数版本
func RunTask(requestId string, request ProcessRequest) (ScanResult, error) {
	// 执行扫描
	result, err := scanTargetAndCompareIndexes(requestId, &request)
	if err != nil {
		return ScanResult{}, err
	}

	log.Info("[%s]target scan completed. total: %d, need update: %d, skipped: %d",
		requestId, result.TotalIndexes, result.UpdateIndexes, result.SkippedIndexes)

	return *result, nil
}

// scanTargetAndCompareIndexes 扫描target文件并对比索引版本
// TODO 同时扫描本地存在但是target中不存在的索引，报警人工处理确认删除
func scanTargetAndCompareIndexes(requestId string, pr *ProcessRequest) (*ScanResult, error) {
	// 读取target文件
	target, err := readTargetFile(pr)
	if err != nil {
		return nil, errors.WithMessagef(err, "read target file failed: %s", pr.TargetFilePath)
	}

	log.Info("[%s]target file loaded, cluster: %s, indexes: %d",
		requestId, target.Cluster.Name, len(target.TargetInfo))

	result := &ScanResult{
		TotalIndexes:  len(target.TargetInfo),
		UpdateList:    make([]*IndexUpdateInfo, 0),
		ExceptionList: make([]*IndexUpdateInfo, 0),
		SkippedList:   make([]*IndexUpdateInfo, 0),
	}

	// 遍历target中的每个索引
	for _, targetItem := range target.TargetInfo {
		// 这里只处理索引
		if dto.Index != targetItem.Type {
			continue
		}

		// 检查索引版本
		updateInfo, err := compareIndexVersion(requestId, pr, targetItem)
		if err != nil {
			log.Error("[%s]compare index version failed for %s: %v", requestId, targetItem.Name, err)
			// 创建错误信息
			updateInfo = &IndexUpdateInfo{
				IndexName:     targetItem.Name,
				TargetVersion: targetItem.Version,
				Exception:     fmt.Sprintf("检查失败: %v", err),
				NeedUpdate:    false,
				Type:          targetItem.Type,
				UpdateType:    targetItem.UpdateType,
			}
		}

		if updateInfo.NeedUpdate {
			result.UpdateList = append(result.UpdateList, updateInfo)
			result.UpdateIndexes++
		} else if updateInfo.Exception != "" {
			result.ExceptionList = append(result.ExceptionList, updateInfo)
			result.ExceptionCount++
		} else {
			result.SkippedList = append(result.SkippedList, updateInfo)
			result.SkippedIndexes++
		}
	}

	return result, nil
}

// readTargetFile 读取target文件
func readTargetFile(pr *ProcessRequest) (*dto.Target, error) {
	if pr.Target != nil {
		return pr.Target, nil
	}
	return dto.BuildTargetFromFile(pr.TargetFilePath)
}

// compareIndexVersion 对比索引版本信息
func compareIndexVersion(requestId string, pr *ProcessRequest, targetItem *dto.TargetItem) (*IndexUpdateInfo, error) {
	indexName := targetItem.Name
	updateInfo := &IndexUpdateInfo{
		IndexName:     indexName,
		TargetVersion: targetItem.Version,
		Type:          targetItem.Type,
		UpdateType:    targetItem.UpdateType,
	}

	// 检查本地索引目录
	indexDir := filepath.Join(pr.IndexDbPath, indexName)
	if !util.IsPathExist(indexDir) {
		updateInfo.UpdateReason = "本地索引目录不存在"
		updateInfo.NeedUpdate = true
		return updateInfo, nil
	}

	// 获取本地索引版本列表
	localVersions, err := util.GetSubdirectories(indexDir)
	if err != nil {
		return nil, errors.WithMessagef(err, "get local index versions failed: %s", indexDir)
	}

	if len(localVersions) == 0 {
		updateInfo.UpdateReason = "本地索引版本目录为空"
		updateInfo.NeedUpdate = true
		return updateInfo, nil
	}

	// 读取本地版本信息
	localVersionList, err := readLocalVersion(requestId, indexName, pr, localVersions)
	if err != nil {
		log.Warn("[%s]read local index build version failed for %s: %v", requestId, indexName, err)
		updateInfo.Exception = fmt.Sprintf("无法读取索引本地版本信息: %s", err)
		updateInfo.NeedUpdate = false
		return updateInfo, nil
	}
	updateInfo.LocalVersion = localVersionList

	// 找到当前使用的版本
	currentVersion, err := getCurrentServingVersion(requestId, indexName)
	if err != nil {
		return nil, errors.WithMessagef(err, "find enging serving version failed for index: %s", indexName)
	}
	updateInfo.CurrentVersion = currentVersion
	log.Info("[%s][%s]index current version: %s", requestId, indexName, currentVersion)

	// 对比版本信息
	needUpdate, reason := compareVersions(targetItem, localVersionList)
	updateInfo.NeedUpdate = needUpdate
	updateInfo.UpdateReason = reason

	log.Info("[%s][%s]index comparison result: needUpdate=%t, reason=%s",
		requestId, indexName, needUpdate, reason)

	return updateInfo, nil
}

// getCurrentServingVersion 获取当前引擎服务的版本
func getCurrentServingVersion(requestId, indexName string) ([]string, error) {
	// 调用引擎接口获取索引的服务版本
	currentVersion, err := common.GetIndexVersions(requestId, indexName)
	if err != nil {
		return nil, err
	}

	return currentVersion, nil
}

// readLocalVersion 读取本地构建信息
func readLocalVersion(requestId, indexName string, pr *ProcessRequest, localDataVersions []string) ([]dto.Version, error) {
	localVersionList := make([]dto.Version, len(localDataVersions))
	for _, localDataVersion := range localDataVersions {
		localVersion, err := readLocalVersionByDataVersion(requestId, indexName, localDataVersion, pr)
		if err != nil {
			return nil, err
		}
		localVersionList = append(localVersionList, localVersion)
	}
	return localVersionList, nil
}

func readLocalVersionByDataVersion(requestId, indexName, localDataVersion string, pr *ProcessRequest) (dto.Version, error) {
	// 构建结果文件路径
	buildResultPath := filepath.Join(pr.IndexDbPath, indexName, localDataVersion, pr.BuildResultFile)

	if !util.IsPathExist(buildResultPath) {
		return dto.Version{}, errors.Errorf("build result file not exist: %s", buildResultPath)
	}

	// 读取构建结果文件
	buildResult, err := common.CheckBuildResult(buildResultPath)
	if err != nil {
		return dto.Version{}, err
	}

	// 提取版本信息
	localBuildInfo := dto.Version{
		DataVersion:  localDataVersion,
		BuildVersion: buildResult.BuildResult.BuildVersion,
	}

	log.Info("[%s]local index build info for %s/%s: dataVersion=%s, buildVersion=%s",
		requestId, indexName, localDataVersion, localBuildInfo.DataVersion, localBuildInfo.BuildVersion)

	return localBuildInfo, nil
}

// compareVersions 对比版本信息，返回是否需要更新和更新原因
// return: needUpdate, reason
// 逻辑：targetVersion.DataVersion是否存在在local中，如果不存在则需要更新；
//
//	如果存在但是BuildVersion不一样，也需要更新
func compareVersions(targetItem *dto.TargetItem, localVersions []dto.Version) (bool, string) {
	targetVersions := targetItem.Version

	// 为每个目标版本检查是否需要更新
	for _, targetVersion := range targetVersions {
		// 检查目标数据版本是否存在于本地
		dataVersionExists := false

		for _, localVersion := range localVersions {
			if targetVersion.DataVersion == localVersion.DataVersion {
				dataVersionExists = true

				// 进一步检查构建版本
				if targetVersion.BuildVersion != localVersion.BuildVersion {
					return true, fmt.Sprintf("构建版本不一致: 数据版本=%s, 目标构建版本=%s, 本地构建版本=%s",
						targetVersion.DataVersion, targetVersion.BuildVersion, localVersion.BuildVersion)
				}
			}
		}

		if !dataVersionExists {
			return true, fmt.Sprintf("数据版本不存在: 目标=%s, 本地版本=%v",
				targetVersion.DataVersion, getDataVersions(localVersions))
		}
	}

	// 所有目标版本都匹配，无需更新
	return false, ""
}

// getDataVersions 提取本地版本的数据版本列表，用于错误信息
func getDataVersions(versions []dto.Version) []string {
	dataVersions := make([]string, len(versions))
	for i, v := range versions {
		dataVersions[i] = v.DataVersion
	}
	return dataVersions
}
