package index

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"fmt"
	"os"
	"path/filepath"

	"github.com/pkg/errors"
)

const (
	TypeName = "rmdb" // 删除指定索引本地CDB目录
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	IndexName            string   `json:"indexName,omitempty" validate:"required"`
	DbPath               string   `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`            // 引擎构建出的索引存放目录
	ServingVersions      []string `json:"servingVersions" validate:"required,min=1"`                                   // 索引最近更新成功的n个版本(双版本索引应该保留的n个版本). 默认为n=2
	DiskUsageThreshold   int      `json:"diskUsageThreshold,omitempty" default:"93"`                                   // 磁盘使用阈值, 默认90%
	BackupDiskUsageRatio int      `json:"backupDiskUsageRatio"`                                                        // 集群索引备份磁盘使用率阈值
	BackupDir            string   `json:"backupIndexDir,omitempty" default:"/app/rel/data/backup" validate:"required"` // 备份索引的路径
	IsRollback           *bool    `json:"isRollback,omitempty"`                                                        // 是否回滚
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	log.Info("[%s]remove index db request: %+v", ctx.RequestId, pr)

	// 删除过期的索引
	err = RunTask(ctx.RequestId, pr)
	if err != nil {
		return api.UpdateFail[string](errors.WithMessage(err, "unload outdated index failed"))
	}

	return api.OfSuccess[string]("remove index db dir success")
}

// RunTask 删除过期的索引
func RunTask(requestId string, pr *ProcessRequest) error {
	// 获取本地过期的版本
	outdatedVersions, err := util.GetOutdatedVersionWithServingVersions(pr.DbPath, pr.IndexName, pr.ServingVersions)
	if err != nil {
		err = errors.WithMessage(err, "get outdated version fail")
		return err
	}
	log.Info("[%s][%s]outdated versions: %v. server versions: %v", requestId, pr.IndexName, outdatedVersions, pr.ServingVersions)

	if len(outdatedVersions) > 0 {
		isRollback := pr.IsRollback != nil && *pr.IsRollback
		// 如果开启了索引备份且非回滚的情况下, 则备份最近的索引版本
		if pr.BackupDiskUsageRatio > 0 && !isRollback {
			backupVersion := outdatedVersions[len(outdatedVersions)-1]
			err := common.BackupIndex(pr.DbPath, pr.BackupDir, pr.IndexName, backupVersion, pr.BackupDiskUsageRatio, pr.DiskUsageThreshold)
			if err != nil {
				// 备份失败只发送报警
				log.Error("[%s]backup index failed: %s. err: %v", requestId, pr.IndexName, err)
				go platform.NewWarnRequestWithMsg(requestId, fmt.Sprintf("backup index failed: %s. err: %s", pr.IndexName, err)).Send()
			} else if backupVersion != "" {
				log.Info("[%s]backup index success: %s", requestId, filepath.Join(pr.BackupDir, pr.IndexName, backupVersion))
			}
		}

		// 卸载过期的索引
		for _, version := range outdatedVersions {
			indexDbDir := filepath.Join(pr.DbPath, pr.IndexName, version)
			if util.IsPathExist(indexDbDir) {
				err = os.RemoveAll(indexDbDir)
				if err != nil {
					return errors.WithMessagef(err, "remove index db dir failed. dir: %s", indexDbDir)
				}
				log.Info("[%s]remove index success: %s", requestId, indexDbDir)
			}
		}
	}

	return nil
}
