package end

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/pipeline"
)

const (
	TypeName = "end"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

// Processor 只用来占位的processor
func makeProcessor(info pipeline.Info) api.Component {
	return process.NewFuncProcessor[ProcessRequest, string](
		info.PipelineName,
		TypeName,
		RunTask,
	)
}

type ProcessRequest struct {
}

func RunTask(requestId string, pr ProcessRequest) (string, error) {
	return "success", nil
}
