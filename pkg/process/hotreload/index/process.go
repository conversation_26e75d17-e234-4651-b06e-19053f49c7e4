package index

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/retry"
	"github.com/pkg/errors"
	"strings"
	"time"
)

const (
	TypeName = "hot-reload"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	IndexName           string   `json:"indexName,omitempty"`           // 索引名称
	LoadIndexVersion    string   `json:"loadIndexVersion,omitempty"`    // 加载的索引版本
	ServingIndexVersion []string `json:"servingIndexVersion,omitempty"` // 服务中的索引版本
	UnloadIndexVersion  []string `json:"unloadIndexVersion,omitempty"`  // 卸载的索引版本
}

type ProcessRequest struct {
	IndexName            string        `json:"indexName,omitempty" validate:"required"`
	IndexVersion         string        `json:"indexVersion,omitempty" validate:"required"`
	DbPath               string        `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"` // 引擎构建出的索引存放目录
	IsDoubleVersion      *bool         `json:"isDoubleVersion" validate:"required" default:"false"`              // 是否双版本索引
	ServingVersions      []string      `json:"servingVersions" validate:"required,min=1"`                        // 索引最近更新成功的n个版本(双版本索引应该保留的n个版本). 默认为n=2
	LoadType             string        `json:"loadType"`                                                         // 加载类型
	IsReboot             *bool         `json:"isReboot,omitempty" default:"false"`                               // 是否重启更新
	NeedCommit           *bool         `json:"needCommit" default:"true"`                                        // 是否需要commit
	UnloadCheckWaitTime  time.Duration `json:"unloadCheckWaitTime" default:"5s"`                                 // 卸载索引检查等待时间
	UnloadCheckTimeout   time.Duration `json:"unloadCheckTimeout" default:"5m"`                                  // 卸载索引检查超时时间
	SleepTimeAfterUnload time.Duration `json:"sleepTimeAfterUnload" default:"18s"`                               // 卸载索引后等待时间
}

type Processor struct {
	process.Abstract
	httpClient *util.HttpClient
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
		httpClient: util.NewHttpClientWithSuccessCode(30*time.Second, []string{"0"}),
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	log.Info("[%s]engine hot-reload request: %+v", ctx.RequestId, pr)

	rmi := &ResultMessageInfo{
		IndexName:           pr.IndexName,
		LoadIndexVersion:    pr.IndexVersion,
		ServingIndexVersion: pr.ServingVersions,
		UnloadIndexVersion:  make([]string, 0),
	}

	// 只有推荐需要commit, 以此区分是否推荐
	isDgraph := pr.NeedCommit != nil && *pr.NeedCommit

	// 如果是双版本, 先卸载过时的版本
	isDoubleVersion := pr.IsDoubleVersion != nil && *pr.IsDoubleVersion
	if isDoubleVersion {
		err := unloadOutdatedVersion(ctx.RequestId, isDgraph, pr, rmi)
		if err != nil {
			return api.UpdateFail[string](err)
		}
	}

	// 调用引擎的热加载接口
	reloadRequest := engine.NewHotReloadRequest(pr.IndexName, pr.IndexVersion, ctx.RequestId, isDoubleVersion)
	err = retry.DoRetryWithMaxRetries(func() error {
		_, innerErr := reloadRequest.Send()
		if innerErr != nil {
			innerErr = errors.WithMessagef(innerErr, "call engine hot-reload index fail, index: %s(%s)", pr.IndexName, pr.IndexVersion)
			return innerErr
		}
		return nil
	}, 1, 18*time.Second, ctx.RequestId)
	if err != nil {
		return api.UpdateFail[string](err)
	}

	// json result
	resultMessageContent, _ := json.MarshalToString(rmi)
	return api.OfSuccessWithMessage[string]("engine hot-reload success", resultMessageContent)
}

// 卸载过期的双版本索引
func unloadOutdatedVersion(requestId string, isDgraph bool, pr *ProcessRequest, rmi *ResultMessageInfo) error {
	outdatedVersions, err := util.GetOutdatedVersionWithServingVersions(pr.DbPath, pr.IndexName, pr.ServingVersions)
	if err != nil {
		err = errors.WithMessage(err, "get outdated version fail")
		return err
	}
	log.Info("[%s]outdated versions: %v. server versions: %v", requestId, outdatedVersions, pr.ServingVersions)

	// 如果过期的版本为空, 则不需要卸载
	if len(outdatedVersions) == 0 {
		return nil
	}

	// 如果热更新的时候, 过期的版本超过1, 则报错. 说明目前索引版本处于非正常状态, 需要人工介入.
	// WARNING!!!: 不能贸然全部卸载, 因为可能将线上服务的2个版本全部卸载导致上游查询失败
	if len(outdatedVersions) > 1 && !*pr.IsReboot {
		return errors.Errorf("outdated version of normal task can not greater than 1, please check: %s(%v)", pr.IndexName, outdatedVersions)
	}

	// 卸载过时的版本
	if len(outdatedVersions) > 0 {
		var needSleep bool
		for _, outdatedVersion := range outdatedVersions {
			_, err := engine.NewUnloadIndexRequest(requestId, pr.IndexName, outdatedVersion).Send()
			if err != nil {
				errMsg := err.Error()
				if strings.Contains(errMsg, "table or version is not exist") {
					log.Warn("[%s]index unload fail: %s(%s), table or version is not exist", requestId, pr.IndexName, outdatedVersion)
					continue
				}
				err = errors.WithMessagef(err, "call engine unload index fail, index: %s(%s)", pr.IndexName, outdatedVersion)
				return err
			}
			rmi.UnloadIndexVersion = append(rmi.UnloadIndexVersion, outdatedVersion)

			// 校验索引是否完成卸载
			ns, err := common.CheckIndexUnload(requestId, pr.IndexName, outdatedVersion, pr.UnloadCheckTimeout, pr.UnloadCheckWaitTime)
			if err != nil {
				return err
			}
			needSleep = needSleep || ns
			log.Info("[%s]index unload success: %s(%s)", requestId, pr.IndexName, outdatedVersion)
		}

		if isDgraph && needSleep {
			// 等待索引的保护时间结束(索引attach以及延迟释放)
			log.Info("[%s]wait for index protection time end: sleep %s", requestId, pr.SleepTimeAfterUnload.String())
			time.Sleep(pr.SleepTimeAfterUnload)
		}
	}
	return nil
}

// 热加载check
// WARNING: 2024-12-18 Dsearch将校验集成到引擎内部, 本方法将废弃
// deprecated: 2024-12-18
func hotReloadCheck(requestId string, timeout, sleep time.Duration) error {
	h := engine.NewHotReloadCheckRequest(requestId)
	start := time.Now()
	for time.Since(start) < timeout {
		resp, err := h.Send()
		if err != nil {
			return err
		}
		tasks := resp.Tasks
		if tasks > 0 {
			log.Info("hot-reload check fail: remaining hot-reload tasks: %d", tasks)
		} else {
			log.Info("hot-reload check success")
			return nil
		}

		time.Sleep(sleep)
	}
	return errors.Errorf("hot-reload check timeout: %s", timeout)
}
