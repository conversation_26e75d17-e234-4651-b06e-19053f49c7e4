package buildindex

import (
	"dip-agent/pkg/consts"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"fmt"
	"github.com/pkg/errors"
	"path/filepath"
	"time"
)

const (
	TypeName = "build-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ProcessRequest struct {
	IndexName    string        `json:"indexName,omitempty" validate:"required"`
	IndexVersion string        `json:"indexVersion,omitempty" validate:"required"`
	ClusterCode  string        `json:"clusterCode,omitempty" validate:"required"`                            // 集群code
	ClusterGroup string        `json:"clusterGroup,omitempty" validate:"required"`                           // 集群分组
	DataPath     string        `json:"dataPath,omitempty" default:"/app/rel/data/tdata" validate:"required"` // 拉取的odps的数据存放目录
	DbPath       string        `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`     // 引擎构建出的索引存放目录
	ConfigPath   string        `json:"configPath,omitempty" default:"/app/rel/config" validate:"required"`   // 配置目录
	LogPath      string        `json:"logPath,omitempty" default:"/logs/engine" validate:"required"`         // 引擎构建日志存放目录
	TempDataPath string        `json:"tempDataPath,omitempty" default:"/app/rel/data/tmp/tdata"`             // 临时数据目录
	MetadataPath string        `json:"metadataPath" default:"/app/rel/config/.metadata" validate:"required"` // 元数据文件路径
	SchemaPath   string        `json:"schemaPath" default:"/app/rel/config/.schema" validate:"required"`     // schema文件路径
	ShardNum     int32         `json:"shardNum"`                                                             // 分片数量
	ShardID      int32         `json:"shardId"`                                                              // 分片号
	MaxFailRate  int32         `json:"maxFailRate" default:"10" validate:"gte=0,lte=100"`                    // 最大失败比例，默认10，表示10%
	IndexDef     *dto.IndexDef `json:"indexDef" validate:"dive,required"`                                    // 索引定义
	CleanDataDir *bool         `json:"cleanDataDir,omitempty" default:"false"`                               // 是否清空dataPath目录, 不清理将断点续传拉取odps数据
	Oss          *dto.Oss      `json:"oss,omitempty" validate:"required,dive"`                               // oss配置
	Odps         *dto.Odps     `json:"odps,omitempty" validate:"required,dive"`                              // odps配置

	EngineBuildCmdPath          string   `json:"engineBuildCmdPath,omitempty" default:"/app/rel/bin/build-engin"`        // engine build的二进制文件路径
	EngineBuildCmdArgs          []string `json:"engineBuildCmdArgs,omitempty"`                                           // engine build的运行参数, 除了-c /xxx/build-request.json之外的参数
	EngineBuildConfigFileName   string   `json:"engineBuildConfigFileName,omitempty" default:"build-request.json"`       // engine build的配置文件名称
	EngineBuildResultFileName   string   `json:"engineBuildResultFileName,omitempty" default:"check.json"`               // engine build结果文件名称
	DumpResultFileName          string   `json:"dumpResultFileName,omitempty" default:"dump-result.json"`                // dump结果文件名称
	ChecksumFileName            string   `json:"checksumFileName,omitempty" default:"checksum.json"`                     // 校验和文件名称
	EngineBuildLogFileName      string   `json:"engineBuildLogFileName,omitempty" default:"engine-build.log"`            // engine build日志文件名称
	InnerEngineBuildLogFileName string   `json:"innerEngineBuildLogFileName,omitempty" default:"inner-engine-build.log"` // 内部engine build日志文件名称, 用来输出build-engin的标准输出

	// 由dump-service写入carrier
	FileCount int   `json:"fileCount"` // 文件数量
	DocNum    int64 `json:"docNum"`    // 文档数量

	// 这一期以下字段不使用
	//SegmentNum int32 `json:"segmentNum"` // segment数量
	//SegmentFileCount []int32      `json:"segmentFileCount"`                                  // 每个分段的数据文件数量, 第i个表示第i段中的数据文件数量

	//DumpServiceConfigFileName string   `json:"dumpServiceConfigFileName,omitempty" default:"dump-request.json"` // dump service的配置文件名称
	//DumpServiceOssPath        string   `json:"dumpServiceOssPath,omitempty" validate:"required"`                // dump service的oss路径
	//DumpServiceLocakDir       string   `json:"dumpServiceLocalDir,omitempty" default:"/app/rel/dump"`           // dump service的本地目录
	//DumpServiceCmdPath        string   `json:"dumpServiceCmdPath,omitempty" default:"java" validate:"required"` // dump service的运行命令path
	//DumpServiceCmdArgs        []string `json:"dumpServiceCmdArgs,omitempty"`                                    // dump service的运行参数, 除了-jar /xxx/dump-service.jar之外的参数
}

func (pr *ProcessRequest) toEngineBuildIndexReq(engineBuildLogFilePath string) *engine.BuildIndexRequest {
	return &engine.BuildIndexRequest{
		IndexName:    pr.IndexName,
		IndexVersion: pr.IndexVersion,
		DataPath:     filepath.Join(pr.DataPath, pr.IndexName, pr.IndexVersion), // 给引擎的是到索引版本级别的数据目录
		LogPath:      engineBuildLogFilePath,
		DBPath:       pr.DbPath,
		ShardNum:     pr.ShardNum,
		ShardID:      pr.ShardID,
		MaxFailRate:  pr.MaxFailRate,
		IndexDef:     engine.GenerateEnginIndexDef(pr.IndexDef),
		FileCount:    pr.FileCount,
		MetadataPath: pr.MetadataPath,
		SchemaPath:   pr.SchemaPath,
		DocNum:       pr.DocNum,

		//SegmentNum: pr.SegmentNum,
		// TODO 计算得出
		//SegmentFileCount: pr.SegmentFileCount,
	}
}

// TODO
func (pr *ProcessRequest) toIndexDumpReq() *IndexDumpRequest {
	return &IndexDumpRequest{}
}

// IndexDumpRequest 拉取索引odps表数据的请求
type IndexDumpRequest struct {
	IndexName        string            `json:"indexName" validate:"required"`    // 索引名称
	IndexVersion     string            `json:"indexVersion" validate:"required"` // 索引版本
	IndexType        string            `json:"indexType" validate:"required"`    // 索引类型
	Fields           []dto.FieldSchema `json:"fields" validate:"required,dive"`  // 索引字段
	SegmentNum       int32             `json:"segmentNum"`                       // segment数量
	SegmentFileCount []int32           `json:"segmentFileCount"`                 // 每个分段的数据文件数量, 第i个表示第i段中的数据文件数量
	SQL              string            `json:"sql"`                              // 拉取odps的sql
	DataPath         string            `json:"dataPath"`                         // 数据目录
	TmpDataPath      string            `json:"tmpDataPath"`                      // 临时数据目录
	DBPath           string            `json:"dbPath"`                           // 索引目录

	// 目前未使用
	Parallel    int32             `json:"parallel"`    // 拉取并行度
	MaxFileSize int32             `json:"maxFileSize"` // 单个数据文件的最大字节数
	Odps        *dto.Odps         `json:"odps"`        // odps相关配置
	ExtraConfig map[string]string `json:"extraConfig"` // 索引额外配置
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

// Process just run build-engine
func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	start := time.Now()
	defer func() {
		// 耗时
		log.Info("[%s]build index cost time: %s", ctx.RequestId, util.DurationToHumanReadable(time.Since(start)))
	}()

	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.BuildFail[string](err)
	}

	// 创建目录, 在dump阶段已经创建, 无需重复执行
	//paths := []string{pr.DataPath, pr.DbPath, pr.LogPath, pr.DumpServiceLocalDir}
	//err = util.CreateDirs(paths, false)
	//if err != nil {
	//	return api.OfFailWithErr[string](err)
	//}
	// 确保dbPath目录存在
	err = util.EnsureDir(pr.DbPath)
	if err != nil {
		err = errors.WithMessagef(err, "ensure dbPath dir fail: %s", pr.DbPath)
		return api.BuildFail[string](err)
	}

	// 清理目录
	err = cleanDir(pr)
	if err != nil {
		return api.BuildFail[string](err)
	}

	// 创建引擎日志文件
	engineLogFilePath := filepath.Join(pr.LogPath, pr.EngineBuildLogFileName)
	log.Info("[%s]engine build log file path: %s", ctx.RequestId, engineLogFilePath)
	exist := util.IsPathExist(engineLogFilePath)
	if !exist {
		err = util.CreateFile(engineLogFilePath)
		if err != nil {
			err = errors.WithMessagef(err, "create engine build log file fail: %s", engineLogFilePath)
			return api.BuildFail[string](err)
		}
		log.Info("[%s]create engine build log file success", ctx.RequestId)
	}

	// run build-engine
	engineConfigFilePath := filepath.Join(pr.ConfigPath, pr.IndexName, pr.IndexVersion, pr.EngineBuildConfigFileName)
	log.Info("[%s]engine build config file path: %s", ctx.RequestId, engineConfigFilePath)
	// 写入配置文件
	bir := pr.toEngineBuildIndexReq(engineLogFilePath)
	err = util.WriteJsonToFile(engineConfigFilePath, bir, true)
	if err != nil {
		err = errors.WithMessagef(err, "write engine build config file fail: %s", engineConfigFilePath)
		return api.BuildFail[string](err)
	}
	// build engine的运行参数
	buildCmdArgs := pr.EngineBuildCmdArgs
	if len(buildCmdArgs) == 0 {
		buildCmdArgs = make([]string, 0)
	}
	buildCmdArgs = append(buildCmdArgs, "-c", engineConfigFilePath)
	// 执行引擎构建
	innerEngineBuildLogFilePath := filepath.Join(log.Dir(), pr.InnerEngineBuildLogFileName)
	log.Info("[%s]inner engine build log file path: %s", ctx.RequestId, innerEngineBuildLogFilePath)
	execCmd, err := util.ExecuteScriptToFile(fmt.Sprintf("[%s]engine build", ctx.RequestId), pr.EngineBuildCmdPath, buildCmdArgs, innerEngineBuildLogFilePath)
	if err != nil {
		err = errors.WithMessagef(err, "engine build index fail. run build-engine fail: %s", execCmd)
		return api.BuildFail[string](err)
	}

	// 检测是否写入了check.json, 以及check.json的内容和构建结果
	buildResultFilePath := filepath.Join(pr.DbPath, pr.IndexName, pr.IndexVersion, pr.EngineBuildResultFileName)
	ber, err := common.CheckBuildResult(buildResultFilePath)
	if err != nil {
		return api.BuildFail[string](err)
	}
	buildResult := ber.BuildResult
	ctx.Carrier.Put(consts.ReportIndexSize, buildResult.IndexTotalBytes)

	// TODO 检查构建结果是否与dump结果相差太大: 数据条数差异小于10%

	// 构建resultMessage, 内容为构建结果的json string
	var resultMessage string
	resultMessageContent, err := json.Marshal(buildResult)
	if err == nil {
		resultMessage = string(resultMessageContent)
	} else {
		log.Warn("[%s]marshal build-engine result fail: %s", ctx.RequestId, err)
	}
	return api.OfSuccessWithMessage[string]("engine build index success", resultMessage)
}

// dump-service 与 build-engine 并发进行的版本
//func (p *Processor) Process(ctx api.ProcessContext) api.Result {
//	start := time.Now()
//	pr := &ProcessRequest{}
//	err := ctx.Carrier.UnpackToWithJson(pr)
//	if err != nil {
//		return api.OfFailWithErrf[string]("invalid param: %w", err)
//	}
//
//	// 创建目录
//	paths := []string{pr.DataPath, pr.DbPath, pr.LogPath, pr.DumpServiceLocalDir}
//	err = util.CreatePaths(paths)
//	if err != nil {
//		return api.OfFailWithErr[string](err)
//	}
//
//	// 清理目录
//	err = cleanDir(pr)
//	if err != nil {
//		return api.OfFailWithErrf[string]("clear dir fail: %w", err)
//	}
//
//	// init oss client
//	c, err := oss.NewClient(pr.OssEndPoint, pr.OssBucket, pr.OssAccessKey, pr.OssSecretKey)
//	if err != nil {
//		return api.OfSystemFailf[string]("create oss client fail: %s", err)
//	}
//
//	// download dump-service
//	dumpServiceFile := filepath.Join(pr.DumpServiceLocalDir, filepath.Base(pr.DumpServiceOssPath))
//	log.Info("[%s]dump-service local file: %s", ctx.RequestId, dumpServiceFile)
//	err = c.DownloadBigFile(pr.DumpServiceOssPath, dumpServiceFile)
//	if err != nil {
//		return api.OfSystemFailf[string]("download dump-service fail: %s", err)
//	}
//	log.Info("[%s]download dump-service success, cost: %s", ctx.RequestId, util.DurationToHumanReadable(time.Since(start)))
//
//	// 并发执行控制属性
//	// 创建上下文
//	innerCtx, cancel := context.WithCancel(context.Background())
//	defer cancel() // 确保在函数退出时取消上下文
//	// 等待组
//	var countDown sync.WaitGroup
//	// 错误队列
//	errChan := make(chan error, 2)
//	// 原子的存储err
//	var firstErr atomic.Value
//
//	// 错误监听
//	go func() {
//		err, ok := <-errChan
//		if !ok {
//			return
//		}
//		if firstErr.Load() == nil {
//			log.Info("[%s]Cancelling index build due to error: %+v", ctx.RequestId, err)
//			firstErr.Store(err)
//			cancel()
//		}
//	}()
//
//	// 配置文件目录
//	configDir := filepath.Join(pr.DbPath, "config")
//
//	// run dump-service
//	dumpServiceConfigFilePath := filepath.Join(configDir, pr.DumpServiceConfigFileName)
//	log.Info("[%s]dump-service config file path: %s", ctx.RequestId, dumpServiceConfigFilePath)
//	// 写入配置文件
//	idr := pr.toIndexDumpReq()
//	err = util.WriteJsonToFile(dumpServiceConfigFilePath, idr)
//	if err != nil {
//		return api.OfFailWithErrf[string]("write dump-service config file fail: %w", err)
//	}
//	dumpArgs := pr.DumpServiceCmdArgs
//	if len(dumpArgs) == 0 {
//		dumpArgs = make([]string, 0)
//	}
//	// dump-service的运行参数
//	dumpArgs = append(dumpArgs, "-jar", dumpServiceFile, dumpServiceConfigFilePath)
//	dumpCmd := exec.Command(pr.DumpServiceCmdPath, dumpArgs...)
//	log.Info("[%s]dump-service command: %s", ctx.RequestId, dumpCmd.String())
//	// 启动dump-service
//	countDown.Add(1)
//	go func() {
//		defer countDown.Done()
//
//		cmdName := "dump-service"
//		p.runCmd(innerCtx, ctx.RequestId, cmdName, dumpCmd, errChan)
//	}()
//
//	// run engine build
//	engineConfigFilePath := filepath.Join(configDir, pr.EngineBuildConfigFileName)
//	log.Info("[%s]engine build config file path: %s", ctx.RequestId, engineConfigFilePath)
//	// 写入配置文件
//	bir := pr.toEngineBuildIndexReq()
//	err = util.WriteJsonToFile(engineConfigFilePath, bir)
//	if err != nil {
//		return api.OfFailWithErrf[string]("write engine build config file fail: %w", err)
//	}
//	// 启动engine build
//	//dumpArgs := []string{"/bin/build-engine", "-c", engineConfigFilePath}
//	//engineCmd := exec.Command("sh", dumpArgs...)
//	// build engine的运行参数
//	buildCmdArgs := pr.EngineBuildCmdArgs
//	if len(buildCmdArgs) == 0 {
//		buildCmdArgs = make([]string, 0)
//	}
//	buildCmdArgs = append(buildCmdArgs, "-c", engineConfigFilePath)
//	buildCmd := exec.Command(pr.EngineBuildCmdPath, "-c", engineConfigFilePath)
//	log.Info("[%s]engine build command: %s", ctx.RequestId, buildCmd.String())
//	countDown.Add(1)
//	go func() {
//		defer countDown.Done()
//
//		cmdName := "build-engine"
//		p.runCmd(innerCtx, ctx.RequestId, cmdName, buildCmd, errChan)
//	}()
//
//	// 等待所有进程结束
//	countDown.Wait()
//
//	// 耗时
//	log.Info("[%s]build index cost: %s", ctx.RequestId, util.DurationToHumanReadable(time.Since(start)))
//
//	// 返回第一个错误
//	if firstErr.Load() != nil {
//		err = firstErr.Load().(error)
//		return api.OfFailWithErr[string](err)
//	}
//	return api.OfSuccess[string]("build index success")
//}

// 异步运行dump-service
//func (p *Processor) asyncRunDumpService(ctx context.Context,
//	countDown *sync.WaitGroup,
//	errChan chan error,
//	pr *ProcessRequest,
//	requestId, dumpServiceFile, configDir string) error {
//
//	dumpServiceConfigFilePath := filepath.Join(configDir, pr.DumpServiceConfigFileName)
//	log.Info("[%s]dump-service config file path: %s", requestId, dumpServiceConfigFilePath)
//	// 写入配置文件
//	idr := pr.toIndexDumpReq()
//	err := util.WriteJsonToFile(dumpServiceConfigFilePath, idr)
//	if err != nil {
//		return fmt.Errorf("write dump-service config file fail: %w", err)
//	}
//	dumpArgs := pr.DumpServiceCmdArgs
//	if len(dumpArgs) == 0 {
//		dumpArgs = make([]string, 0)
//	}
//	// dump-service的运行参数
//	dumpArgs = append(dumpArgs, "-jar", dumpServiceFile, dumpServiceConfigFilePath)
//	dumpCmd := exec.Command(pr.DumpServiceCmdPath, dumpArgs...)
//	log.Info("[%s]dump-service command: %s", requestId, dumpCmd.String())
//	// 启动dump-service
//	go func() {
//		countDown.Add(1)
//		defer countDown.Done()
//
//		cmdName := "dump-service"
//		p.runCmd(ctx, requestId, cmdName, dumpCmd, errChan)
//	}()
//	return nil
//}

// 清理目录
func cleanDir(pr *ProcessRequest) error {
	localIndexDir := filepath.Join(pr.DbPath, pr.IndexName, pr.IndexVersion)
	err := util.RemoveAllExcept(localIndexDir, nil)
	if err != nil {
		return errors.WithMessagef(err, "remove local index dir fail: %s", localIndexDir)
	}
	if *pr.CleanDataDir {
		err = util.RemoveAllExcept(pr.DataPath, nil)
		if err != nil {
			return errors.WithMessagef(err, "remove data path fail: %s", pr.DataPath)
		}
	}
	return nil
}
