package dump

import (
	"bufio"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/util"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/aliyun/aliyun-odps-go-sdk/odps"
	"github.com/aliyun/aliyun-odps-go-sdk/odps/account"
	"github.com/aliyun/aliyun-odps-go-sdk/odps/data"
	"github.com/aliyun/aliyun-odps-go-sdk/odps/tableschema"
	"github.com/pkg/errors"
)

type DumpConfig struct {
	AutoAddKeyToJsonData bool `json:"autoAddKeyToJsonData" default:"true"` // 是否开启json_data自动添加key
}

type Info struct {
	IndexName          string            `json:"indexName" validate:"required"`
	IndexVersion       string            `json:"indexVersion" validate:"required"`
	IndexType          string            `json:"indexType" validate:"required"`
	Sql                string            `json:"sql" validate:"required"`
	Hints              map[string]string `json:"hints"`                                 // odps sql hints
	DumpProcessorName  string            `json:"dumpProcessorName" validate:"required"` // dump processor name
	SpecialDeal        string            `json:"specialDeal,omitempty"`                 // 字段特殊处理
	DumpMethod         string            `json:"dumpMethod"`                            // dump method: 目前仅仅table_tunnel和instance_tunnel, 默认为instance_tunnel
	TableName          string            `json:"tableName" validate:"required"`         // 表名, 用于table_tunnel
	KeyFieldName       string            `json:"keyFieldName" validate:"required"`      // 主键字段名称
	KeyFieldType       string            `json:"keyFieldType" validate:"required"`      // 主键字段类型
	ShardNum           int32             `json:"shardNum"`                              // 分片数量
	ShardID            int32             `json:"shardId"`                               // 分片号
	ShardKey           string            `json:"shardKey"`                              // 分片键
	PartitionField     string            `json:"partitionField"`                        // 分区字段
	PartitionCondition string            `json:"partitionCondition"`                    // 分区条件

	DataPath    string `json:"dataPath" validate:"required"` // 数据目录
	TmpDataPath string `json:"tmpDataPath"`                  // 临时数据目录
	ThreadNum   int    `json:"threadNum"`                    // 线程数
	BufferSize  int    `json:"bufferSize" default:"1048576"` // 缓冲区大小, 默认1M, 实际测试1MB相比4MB性能提升不大

	OdpsAccessKey          string        `json:"odpsAccessKey" validate:"required"`    // odps ak
	OdpsSecretKey          string        `json:"odpsSecretKey" validate:"required"`    // odps sk
	OdpsProject            string        `json:"odpsProject" validate:"required"`      // odps项目
	OdpsEndpoint           string        `json:"odpsEndpoint" validate:"required"`     // odps endpoint
	TunnelEndpoint         string        `json:"tunnelEndpoint,omitempty"`             // odps tunnel endpoint
	WaitOdpsSuccessTimeout time.Duration `json:"waitOdpsSuccessTimeout" default:"10m"` // 等待odps任务成功的超时时间, 默认10分钟

	// TODO 这一期暂时不分段, 以下参数未使用
	SegmentNum        int   `json:"segmentNum"`                             // 分段数
	SegmentAnchorSize int64 `json:"segmentAnchorSize" default:"4294967296"` // 每个分段的锚定大小, 默认4G
	MaxSegmentCount   int   `json:"maxSegmentCount" default:"8"`            // 最大分段数, 默认8个
	FileSize          int   `json:"fileSize"`                               // 文件大小
	OdpsBatchCount    int   `json:"odpsBatchCount" default:"10000"`         // odps批次数

	UseAvro  bool   `json:"useAvro"`  // 是否使用avro序列化
	JsonName string `json:"jsonName"` // json序列化使用的库名. 目前仅仅调试使用!!!

	AgentConfig cfg.CommonCfg `json:"agentConfig"` // agent相关配置
}

type Service struct {
	requestId  string
	info       *Info
	dumpConfig *DumpConfig

	odpsIns *odps.Odps
	session RecordDownloadSession

	columnNames []string
	columns     []tableschema.Column

	done                  chan struct{}
	stop                  sync.Once
	errChan               chan error
	countDown             sync.WaitGroup
	fistErr               atomic.Value
	threadNum             int
	total                 int
	specialDealProcessors []SpecialFieldProcessor
	processor             IndexProcessor
	isSimpleProcessor     bool
}

func NewService(requestId string, info *Info) (*Service, error) {
	s := &Service{
		requestId: requestId,
		info:      info,
		done:      make(chan struct{}),
		errChan:   make(chan error, 1),
	}

	// 初始化dump config
	dumpConfig := &DumpConfig{}
	if info.AgentConfig != nil {
		err := info.AgentConfig.UnpackToWithJson(dumpConfig)
		if err != nil {
			return nil, errors.WithMessage(err, "unpack dump config error")
		}
		log.Info("[%s]dump config: %+v", requestId, dumpConfig)
	}
	s.dumpConfig = dumpConfig

	// init
	err := s.init()
	return s, err
}

func (s *Service) init() error {
	start := time.Now()
	initDoneChan := make(chan struct{})
	defer func() {
		close(initDoneChan)
		log.Info("[%s]init dump-service cost time: %s", s.requestId, util.DurationToHumanReadable(time.Since(start)))
	}()

	// 初始化odps
	aliAccount := account.NewAliyunAccount(s.info.OdpsAccessKey, s.info.OdpsSecretKey)
	odpsIns := odps.NewOdps(aliAccount, s.info.OdpsEndpoint)
	odpsIns.SetDefaultProjectName(s.info.OdpsProject)
	odpsIns.SetHttpTimeout(60 * time.Second)
	s.odpsIns = odpsIns

	// 获取数据下载session
	dumpMethod := s.info.DumpMethod
	// note!!!: table tunnel类型下载目的是为了dump下载下来的数据有序. 前提是odps表类型需要为range cluster表
	if dumpMethod == "table_tunnel" {
		log.Info("[%s]use table tunnel", s.requestId)
		session, err := NewTableTunnelRecordDownloadSession(s.requestId, s.info, s.odpsIns)
		if err != nil {
			return err
		}
		s.session = session
	} else {
		log.Info("[%s]use instance tunnel", s.requestId)
		session, err := NewInstanceRecordDownloadSession(s.requestId, s.info, s.odpsIns)
		if err != nil {
			return err
		}
		s.session = session
	}

	// 列信息
	schema := s.session.Schema()
	columns := schema.Columns
	columnNames := make([]string, 0, len(columns))
	for _, c := range columns {
		columnNames = append(columnNames, c.Name)
	}
	log.Info("[%s]index schema column names[%d]: %s", s.requestId, len(columnNames), strings.Join(columnNames, ", "))
	s.columnNames = columnNames
	s.columns = columns

	// 分区字段
	//partitionColumns := schema.PartitionColumns

	// 总记录数
	total := s.session.RecordCount()
	s.total = total
	log.Info("[%s]total record count: %d", s.requestId, total)

	// 计算线程数
	threadNum := runtime.GOMAXPROCS(-1) * 2
	if s.info.ThreadNum >= 1 && s.info.ThreadNum < threadNum {
		threadNum = s.info.ThreadNum
		log.Info("[%s]pullTask thread num set by config: %d", s.requestId, threadNum)
	}
	s.threadNum = threadNum
	log.Info("[%s]pullTask consumer thread num: %d", s.requestId, s.threadNum)

	// 初始化特殊字段processor
	specialDeal := s.info.SpecialDeal
	if specialDeal != "" {
		specialDealProcessors, err := AnalysisSpecialDeal(specialDeal)
		if err != nil {
			return errors.WithMessage(err, "analysis special deal error")
		}
		s.specialDealProcessors = specialDealProcessors
	}

	// 获取数据处理器
	p, ok := processorMap[s.info.DumpProcessorName]
	if !ok {
		return errors.Errorf("dump processor not found: %s", s.info.DumpProcessorName)
	}
	s.processor = p
	// 进一步判断是否可以使用SimpleProcessor
	if s.info.SpecialDeal == "" {
		if !util.ContainsString(s.columnNames, "json_data") && !util.ContainsString(s.columnNames, "docs") {
			s.processor = &SimpleIndexProcessor{}
			s.isSimpleProcessor = true
		}
	}
	log.Info("[%s]index processor: %s", s.requestId, s.processor.Name())

	return nil
}

// SimpleDump 不分段, 直接写多个小文件
func (s *Service) SimpleDump() (*dto.DumpResult, error) {
	startTime := time.Now()
	tr := &dto.DumpResult{
		RequestId:  s.requestId,
		SessionId:  s.session.SessionId(),
		LogViewUrl: s.session.LogViewUrl(),
	}
	defer func() {
		cost := util.DurationToHumanReadable(time.Since(startTime))
		tr.DumpCost = cost
		log.Info("[%s]index simple dump cost time: %s", s.requestId, cost)
	}()

	// 数据量为0直接返回
	if s.total == 0 {
		return &dto.DumpResult{RequestId: s.requestId}, nil
	}

	// 文件数量
	fileNum := s.threadNum
	if s.total < 20000 {
		log.Info("[%s]total record count less than 20000, use one file", s.requestId)
		// 如果数据量小于20000, 则只使用一个文件
		fileNum = 1
	}
	rc := make(chan *dto.DumpResult, fileNum)
	// 计算每个文件的记录数
	baseFileRecordCount := s.total / fileNum
	for i := 0; i < fileNum; i++ {
		count := baseFileRecordCount
		if i == fileNum-1 {
			count = s.total - baseFileRecordCount*i
		}
		start := i * baseFileRecordCount

		index := i
		s.countDown.Add(1)
		go func() {
			log.Info("[%s]simple dump file-[%d] start", s.requestId, index)
			defer func() {
				// recover
				if r := recover(); r != nil {
					// 增加堆栈日志信息
					log.Error("dump service panic: %v\n%s", r, string(debug.Stack()))
					err := errors.Errorf("dump service panic: %v", r)
					s.fistErr.Store(err)
				}

				s.countDown.Done()
				log.Info("[%s]simple dump file-[%d] stop", s.requestId, index)
			}()

			dr, err := s.syncFileRead(int32(index), start, count)
			if err != nil {
				log.Error("simple dump file-[%d] error: %+v", index, err)
				s.fistErr.Store(err)
			}
			rc <- dr
		}()
	}

	s.countDown.Wait()
	s.stop.Do(func() {
		close(s.done)
	})
	close(rc)

	errVal := s.fistErr.Load()
	if errVal != nil {
		if e, ok := errVal.(error); ok {
			return nil, e
		}
	}

	// 返回统计结果
	for r := range rc {
		tr.Count += r.Count
		tr.Size += r.Size
		tr.QueryCount += r.QueryCount
		tr.EmptyCount += r.EmptyCount
		tr.FileCount += r.FileCount
	}

	return tr, nil
}

// 数据文件读取任务
// start: 从第几条开始读取
// batchCount: 单批次读取多少条
// total: 一共需要读几条
func (s *Service) syncFileRead(fileIndex int32, start, total int) (*dto.DumpResult, error) {
	log.Info("sync file read start, fileIndex: %d, start: %d, total: %d", fileIndex, start, total)
	var (
		file *os.File
		err  error
	)

	// 创建本地数据文件
	dataFile := filepath.Join(s.info.DataPath, s.info.IndexName, s.info.IndexVersion, strconv.Itoa(int(fileIndex))+".done")
	log.Info("[%d ~ %d]start pull data and write to file: %s", start, start+total, dataFile)
	// 确保目录存在
	err = util.EnsureDir(dataFile)
	if err != nil {
		return nil, err
	}

	// 打开本地数据文件
	file, err = os.OpenFile(dataFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return nil, errors.Wrapf(err, "open file fail: %s", dataFile)
	}
	defer func() {
		file.Close()
		log.Info("stop pull data and write to file: %s", dataFile)
	}()

	// 创建一个带缓冲的Writer
	bufferSize := 1024 * 1024 // 默认1MB, 实际测试4MB相比1MB性能提升不大
	bufferSize = util.MaxInt(bufferSize, s.info.BufferSize)
	log.Info("buffer size: %s", util.ByteToHumanReadable(int64(bufferSize)))
	writer := bufio.NewWriterSize(file, bufferSize)
	defer writer.Flush() // 确保所有缓冲的数据都写入到底层文件

	ctx := Context{
		Writer: writer,
		//Record:                 dataMap,
		Columns:                s.columns,
		SpecialFieldProcessors: s.specialDealProcessors,
		KeyFieldName:           s.info.KeyFieldName,
		dumpConfig:             s.dumpConfig,
	}

	dumpResult := &dto.DumpResult{
		QueryCount: int64(total),
		FileCount:  1,
	}

	retry := 5
	readCount := 0
	for total > 0 && retry > 0 {
		// 调用odps接口拉取数据
		readCount, err = s.pullAndProcess(start, total, ctx, dumpResult)
		if err != nil {
			return nil, err
		}
		log.Info("pull data and write to file: %s, start: %d, total: %d, readCount: %d", dataFile, start, total, readCount)

		start = start + readCount
		total = total - readCount
		retry--
	}

	if total > 0 {
		// note: 特殊case可能会存在. 例如Dsearch要求是用table_tunnel+本地分片过滤的数据拉取方式
		log.Warn("pull data and write to file error: %s, start: %d, total: %d, readCount: %d", dataFile, start, total, readCount)
	}

	return dumpResult, nil
}

func (s *Service) pullAndProcess(start, total int, ctx Context, dumpResult *dto.DumpResult) (int, error) {
	var (
		err       error
		readCount int64
	)
	// 拉取并处理数据: 实际读取的record数量不一定等于total, 要实际计算一下读出的record数量
	recordIterator := s.session.OpenRecordReader(start, total)

	// 读取数据
	readCount, err = recordIterator(func(record data.Record) error {
		// 数据record map
		dataMap := s.convertRecord(record)

		// 空数据记录
		if len(dataMap) == 0 {
			dumpResult.EmptyCount++
			return nil
		}

		// 处理数据
		ctx.Record = dataMap
		rs, innerErr := s.processor.Process(ctx)
		if innerErr != nil {
			err = errors.WithMessage(innerErr, "process record error")
			return err
		}

		if rs > 0 {
			dumpResult.Count++
			dumpResult.Size += int64(rs)
		} else {
			dumpResult.EmptyCount++
		}

		return nil
	})
	return int(readCount), err
}

func (s *Service) convertRecord(record data.Record) map[string]interface{} {
	l := len(s.columnNames)
	m := make(map[string]interface{}, l)
	for i, d := range record {
		if d == nil {
			continue
		}
		v := convertColumnValue(d)
		if v != nil {
			m[s.columnNames[i]] = v
		}
	}
	return m
}

func (s *Service) ThreadNum() int {
	return s.threadNum
}

func (s *Service) Total() int {
	return s.total
}

func (s *Service) IsHashFilter() bool {
	return s.session.IsHashFilter()
}
