package dump

import (
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"encoding/binary"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/aliyun/aliyun-odps-go-sdk/odps"
	"github.com/aliyun/aliyun-odps-go-sdk/odps/data"
	"github.com/aliyun/aliyun-odps-go-sdk/odps/tableschema"
	"github.com/aliyun/aliyun-odps-go-sdk/odps/tunnel"
	"github.com/pkg/errors"
)

const (
	M64 int64 = -4132994306676758123
	R64 int64 = 47
)

// RecordConsumer 记录消费者
type RecordConsumer func(record data.Record) error

// RecordIterator 记录迭代器
type RecordIterator func(consumer RecordConsumer) (readCount int64, err error)

// RecordDownloadSession 记录下载会话
type RecordDownloadSession interface {
	SessionId() string
	LogViewUrl() string
	IsHashFilter() bool
	RecordCount() int
	Schema() tableschema.TableSchema
	OpenRecordReader(start, count int) RecordIterator
}

type TableTunnelRecordDownloadSession struct {
	requestId  string
	odpsIns    *odps.Odps
	session    *tunnel.DownloadSession
	logViewUrl string

	ShouldFilterByShard bool // 是否需要按照分片过滤数据
	ShardKeyIndex       int  // 分片键在columns中的索引

	ShardNum int32  `json:"shardNum"` // 分片数量
	ShardID  int32  `json:"shardId"`  // 分片号
	ShardKey string `json:"shardKey"` // 分片键

	hashFunc func(id string, shardNum int) int
}

func NewTableTunnelRecordDownloadSession(requestId string, info *Info, odpsIns *odps.Odps) (*TableTunnelRecordDownloadSession, error) {
	// 校验
	tableName := info.TableName
	if tableName == "" {
		return nil, errors.New("create table tunnel fail: tableName is empty")
	}
	partitionField := info.PartitionField
	if partitionField == "" {
		return nil, errors.New("create table tunnel fail: partitionField is empty")
	}

	log.Info("[%s]init odps table tunnel download session, table: %s, shardNum: %d, shardId: %d, shardKey: %s", requestId, tableName, info.ShardNum, info.ShardID, info.ShardKey)

	var err error
	project := odpsIns.DefaultProject()
	// tunnel endpoint
	tunnelEndpoint := info.TunnelEndpoint
	if tunnelEndpoint == "" {
		tunnelEndpoint, err = project.GetTunnelEndpoint()
		if err != nil {
			return nil, errors.Wrap(err, "odps get table tunnel endpoint error")
		}
	}
	log.Info("[%s]odps table tunnel endpoint: %s", requestId, tunnelEndpoint)
	tunnelIns := tunnel.NewTunnel(odpsIns, tunnelEndpoint)
	// 解析出Partition Condition
	var pkCondition strings.Builder
	pkCondition.WriteString(fmt.Sprintf("%s=%s", partitionField, info.IndexVersion))
	partitionCondition := info.PartitionCondition
	if partitionCondition != "" {
		pkCondition.WriteString(fmt.Sprintf(",%s", partitionCondition))
	}
	log.Info("[%s]odps table tunnel partition key: %s", requestId, pkCondition.String())
	session, err := tunnelIns.CreateDownloadSession(project.Name(), tableName,
		tunnel.SessionCfg.WithPartitionKey(pkCondition.String()))
	if err != nil {
		return nil, errors.Wrap(err, "odps create table tunnel download session error")
	}
	log.Info("[%s]odps table tunnel download session created. sessionId: %s", requestId, session.Id)

	// 根据索引类型区分Dsearch/Dgraph
	indexType := info.IndexType
	var hashFunc func(id string, shardNum int) int
	log.Info("[%s]odps table tunnel index type: %s", requestId, indexType)
	if indexType == "kv" || indexType == "kvv" || indexType == "invert" || indexType == "dense-kv" || indexType == "disk-kv" {
		hashFunc = DgraphShard
		log.Info("[%s]odps table tunnel engine type is Dgraph, use hashFunc: DgraphShard()", requestId)
	} else {
		hashFunc = DsearchShard
		log.Info("[%s]odps table tunnel engine type is Dsearch, use hashFunc: DsearchShard()", requestId)
	}

	schema := session.Schema()
	columns := schema.Columns
	// 是否需要按照分片过滤数据
	shouldFilterByShard := info.ShardNum > 0 && info.ShardID >= 0 && info.ShardKey != ""
	// 分片键在columns中的索引
	shardKeyIndex := -1
	if shouldFilterByShard {
		for i, c := range columns {
			if c.Name == info.ShardKey {
				shardKeyIndex = i
				break
			}
		}
	}
	// 分片键不存在
	if shardKeyIndex == -1 {
		shouldFilterByShard = false
	}
	log.Info("[%s]odps table tunnel download session isHashFilter: %v, shardKeyIndex: %d, shardKey: %s", requestId, shouldFilterByShard, shardKeyIndex, info.ShardKey)

	return &TableTunnelRecordDownloadSession{
		requestId:           requestId,
		odpsIns:             odpsIns,
		session:             session,
		ShouldFilterByShard: shouldFilterByShard,
		ShardKeyIndex:       shardKeyIndex,
		ShardNum:            info.ShardNum,
		ShardID:             info.ShardID,
		ShardKey:            info.ShardKey,
		hashFunc:            hashFunc,
	}, nil
}

func (t *TableTunnelRecordDownloadSession) SessionId() string {
	return t.session.Id
}

func (t *TableTunnelRecordDownloadSession) LogViewUrl() string {
	return t.logViewUrl
}

func (t *TableTunnelRecordDownloadSession) IsHashFilter() bool {
	return t.ShouldFilterByShard
}

func (t *TableTunnelRecordDownloadSession) RecordCount() int {
	return t.session.RecordCount()
}

func (t *TableTunnelRecordDownloadSession) Schema() tableschema.TableSchema {
	return t.session.Schema()
}

func (t *TableTunnelRecordDownloadSession) OpenRecordReader(start, count int) RecordIterator {
	f := func(consumer RecordConsumer) (readCount int64, err error) {
		reader, err := t.session.OpenRecordReader(start, count, nil)
		if err != nil {
			return 0, errors.Wrapf(err, "open record reader error. start: %d, total: %d", start, count)
		}
		defer reader.Close()

		// 读取数据
		var record data.Record
		for {
			record, err = reader.Read()

			// 读取结束
			isEOF := errors.Is(err, io.EOF)
			if isEOF {
				return readCount, nil
			}

			// 读取odps数据失败
			if err != nil {
				return readCount, errors.WithMessage(err, "table tunnel read record error")
			}

			// 读取计数
			readCount++

			// 按照分片过滤数据
			if t.ShouldFilterByShard {
				shardVal := record.Get(t.ShardKeyIndex)
				v := convertColumnValue(shardVal)
				var vs string
				// 将v转为字符串
				if v == nil {
					vs = ""
				} else {
					vs = fmt.Sprintf("%v", v)
				}
				// 计算分片
				//shardId := DsearchShard(vs, int(t.ShardNum))
				shardId := t.hashFunc(vs, int(t.ShardNum))
				if shardId != int(t.ShardID) {
					continue
				}
			}

			// 消费数据
			err = consumer(record)
			if err != nil {
				return readCount, errors.WithMessagef(err, "consume record error: %+v", record)
			}
		}
	}
	return f
}

// getLittleEndianLong 从指定索引开始的8个字节中获取小端序的长整型数。
func getLittleEndianLong(data []byte, index int) int64 {
	return int64(binary.LittleEndian.Uint64(data[index:]))
}

func logicalRightShift(value int64, shift int64) int64 {
	return int64(uint(value) >> shift) // 将有符号整数转为无符号整数，逻辑右移，然后转回有符号
}

// hash64 calculates a 64-bit hash value
func hash64(data []byte, length int, seed int) int64 {
	h := (int64(seed) & 0xffffffff) ^ (int64(length) * M64)
	nblocks := length >> 3

	for i := 0; i < nblocks; i++ {
		index := i << 3
		k := getLittleEndianLong(data, index)

		k *= M64
		//k ^= k >> R64
		k ^= logicalRightShift(k, R64)
		k *= M64

		h ^= k
		h *= M64
	}

	index := nblocks << 3
	tail := length - index
	switch tail {
	case 7:
		h ^= int64(data[index+6]) << 48
		fallthrough
	case 6:
		h ^= int64(data[index+5]) << 40
		fallthrough
	case 5:
		h ^= int64(data[index+4]) << 32
		fallthrough
	case 4:
		h ^= int64(data[index+3]) << 24
		fallthrough
	case 3:
		h ^= int64(data[index+2]) << 16
		fallthrough
	case 2:
		h ^= int64(data[index+1]) << 8
		fallthrough
	case 1:
		h ^= int64(data[index])
		h *= M64
	}

	h ^= logicalRightShift(h, R64)
	h *= M64
	h ^= logicalRightShift(h, R64)

	return h
}

// hash64FromString hashes a string input
func hash64FromString(data string) int64 {
	bytes := []byte(data)
	return hash64(bytes, len(bytes), 42)
}

// remainderUnsigned computes the remainder of division of two unsigned longs.
func remainderUnsigned(dividend, divisor uint64) uint64 {
	if divisor == 0 {
		return dividend // mimic Java's behavior; though in Go, this would panic if not checked
	}
	return dividend % divisor
}

// DsearchShard calculates a DsearchShard number from an ID string
// 翻译自: https://pkg.poizon.com/algorithm/dsearch-dump/-/blob/master/src/main/java/com/shizhuang/duapp/ds/dump/DSearchHash.java#L75 DSearchHash#shard
func DsearchShard(id string, shardNum int) int {
	if id == "" {
		return 0
	}

	hash := hash64FromString(id)
	return int(remainderUnsigned(uint64(hash), uint64(shardNum)))
}

// DgraphShard calculates a DgraphShard number from an ID string
// 翻译自: https://pkg.poizon.com/algorithm/dsearch-dump/-/blob/master/src/main/java/com/shizhuang/duapp/ds/dump/DSearchHash.java#L75 DSearchHash#shard
func DgraphShard(id string, shardNum int) int {
	bytes := []byte(id)
	hr := dgraphHash64(bytes, len(bytes), 42)
	return int(remainderUnsigned(uint64(hr), uint64(shardNum)))
}

func dgraphHash64(key []byte, length int, seed int64) int64 {
	const (
		m int64 = -4132994306676758123
		r       = 47
	)

	h := seed ^ (int64(length) * m)

	// 处理每个 8 字节块
	nblocks := length / 8
	for i := 0; i < nblocks; i++ {
		index := i * 8
		// 读取小端序的 8 字节并转换为 int64（兼容 Java 的 getLittleEndianLong）
		k := int64(binary.LittleEndian.Uint64(key[index : index+8]))
		k *= m
		k ^= k >> r
		k *= m
		h ^= k
		h *= m
	}

	// 处理剩余字节（非完整 8 字节的部分）
	index := nblocks * 8
	switch length & 7 {
	case 7:
		h ^= int64(key[index+6]&0xff) << 48
		fallthrough
	case 6:
		h ^= int64(key[index+5]&0xff) << 40
		fallthrough
	case 5:
		h ^= int64(key[index+4]&0xff) << 32
		fallthrough
	case 4:
		h ^= int64(key[index+3]&0xff) << 24
		fallthrough
	case 3:
		h ^= int64(key[index+2]&0xff) << 16
		fallthrough
	case 2:
		h ^= int64(key[index+1]&0xff) << 8
		fallthrough
	case 1:
		h ^= int64(key[index] & 0xff)
		h *= m
	}

	// 最终混合步骤
	h ^= h >> r
	h *= m
	h ^= h >> r

	// 返回绝对值（兼容 Java 的 `return h < 0 ? -h : h`）
	if h < 0 {
		return -h
	}
	return h
}

type InstanceRecordDownloadSession struct {
	requestId  string
	odpsIns    *odps.Odps
	session    *tunnel.InstanceResultDownloadSession
	logViewUrl string
}

// NewInstanceRecordDownloadSession creates a new instance record download session.
// It initializes the session, executes the provided SQL with hints, waits for the task to complete,
// and generates a log view URL. If a timeout is specified, it monitors the task progress asynchronously.
//
// Parameters:
//   - requestId: A unique identifier for the request.
//   - info: An Info struct containing SQL and hints for execution.
//   - odpsIns: An instance of the odps.Odps client.
//
// Returns:
//   - *InstanceRecordDownloadSession: A pointer to the created InstanceRecordDownloadSession.
//   - error: An error if any occurs during the process.
func NewInstanceRecordDownloadSession(requestId string, info *Info, odpsIns *odps.Odps) (*InstanceRecordDownloadSession, error) {
	start := time.Now()
	initDoneChan := make(chan struct{})
	defer func() {
		close(initDoneChan)
		log.Info("[%s]init instance record session cost time: %s", requestId, util.DurationToHumanReadable(time.Since(start)))
	}()

	hints := info.Hints
	if hints == nil {
		hints = make(map[string]string)
	}
	sql := info.Sql
	if !strings.HasSuffix(sql, ";") {
		sql += ";"
	}
	// 优先级设置, 在执行 sql 前加上
	// set odps.instance.priority=1;

	log.Info("[%s]dump odps sql: %s", requestId, sql)
	ins, err := odpsIns.ExecSQlWithHints(sql, hints)
	if err != nil {
		return nil, errors.Wrap(err, "odps exec sql error")
	}
	log.Info("[%s]odps sql task submit success: %s", requestId, ins.Id())

	// 增加异步超时取消机制
	if info.WaitOdpsSuccessTimeout > 0 {
		go monitorTaskProgress(requestId, initDoneChan, info.WaitOdpsSuccessTimeout, ins)
	}
	// 等待odps sql任务完成
	err = ins.WaitForSuccess()
	if err != nil {
		return nil, errors.Wrap(err, "odps wait for success error")
	}
	log.Info("[%s]odps sql task instance wait success: %s", requestId, ins.Id())

	// build log view
	lv := odpsIns.LogView()
	url, err := lv.GenerateLogView(ins, 1)
	if err != nil {
		return nil, errors.Wrap(err, "odps generate log view error")
	}
	log.Info("[%s]odps log view url: %s", requestId, url)

	// build session
	project := odpsIns.DefaultProject()
	// tunnel endpoint
	tunnelEndpoint := info.TunnelEndpoint
	if tunnelEndpoint == "" {
		tunnelEndpoint, err = project.GetTunnelEndpoint()
		if err != nil {
			return nil, errors.Wrap(err, "odps get tunnel endpoint error")
		}
	}
	log.Info("[%s]odps instance tunnel endpoint: %s", requestId, tunnelEndpoint)
	tunnelIns := tunnel.NewTunnel(odpsIns, tunnelEndpoint)
	session, err := tunnelIns.CreateInstanceResultDownloadSession(project.Name(), ins.Id())
	//session, err := tunnelIns.CreateDownloadSession(project.Name(), s.ins.Id())
	if err != nil {
		return nil, errors.Wrap(err, "odps create instance download session error")
	}
	log.Info("[%s]odps instance tunnel download session created. sessionId: %s", requestId, session.Id)
	return &InstanceRecordDownloadSession{
		requestId:  requestId,
		odpsIns:    odpsIns,
		session:    session,
		logViewUrl: url,
	}, nil
}

// 监控odps任务进度,并且在超时的时候取消任务
func monitorTaskProgress(requestId string, done chan struct{}, waitTimeout time.Duration, ins *odps.Instance) {
	// 等待超时时间
	if waitTimeout <= 0 {
		waitTimeout = 30 * time.Minute
	}
	log.Info("[%s]odps task wait timeout threshold: %s", requestId, waitTimeout)
	timeoutTimer := time.NewTimer(waitTimeout)
	defer timeoutTimer.Stop()
	// 获取任务执行进度的间隔
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-done:
			return
		case <-timeoutTimer.C:
			log.Warn("[%s]odps task timeout, terminate it", requestId)
			err := ins.Terminate()
			if err != nil {
				log.Warn("[%s]odps terminate error: %+v", requestId, err)
			}
			return
		case <-ticker.C:
			// 获取任务状态
			tasks, err := ins.GetTasks()
			if err != nil {
				log.Warn("[%s]odps get tasks error: %+v", requestId, err)
			}
			if len(tasks) > 0 {
				taskName := tasks[0].Name
				progress, err := ins.GetTaskProgress(taskName)
				if err != nil {
					log.Warn("[%s]odps get task progress error: %+v", requestId, err)
				} else {
					// json 输出
					progressContent, _ := json.Marshal(progress)
					log.Info("[%s]odps task progress: %s", requestId, progressContent)
				}
			}
		}
	}
}

func (i *InstanceRecordDownloadSession) SessionId() string {
	return i.session.Id
}

func (i *InstanceRecordDownloadSession) LogViewUrl() string {
	return i.logViewUrl
}

func (i *InstanceRecordDownloadSession) IsHashFilter() bool {
	return false
}

func (i *InstanceRecordDownloadSession) RecordCount() int {
	return i.session.RecordCount()
}

func (i *InstanceRecordDownloadSession) Schema() tableschema.TableSchema {
	return i.session.Schema()
}

func (i *InstanceRecordDownloadSession) OpenRecordReader(start, count int) RecordIterator {
	f := func(consumer RecordConsumer) (readCount int64, err error) {
		reader, err := i.session.OpenRecordReader(start, count, 0, nil)
		if err != nil {
			return readCount, errors.Wrapf(err, "open record reader error. start: %d, total: %d", start, count)
		}
		defer reader.Close()

		// 读取数据
		var record data.Record
		for {
			record, err = reader.Read()

			// 读取结束
			isEOF := errors.Is(err, io.EOF)
			if isEOF {
				return readCount, nil
			}

			// 读取odps数据失败
			if err != nil {
				return readCount, errors.WithMessage(err, "read record error")
			}

			// 读取计数
			readCount++

			// 消费数据
			err = consumer(record)
			if err != nil {
				return readCount, errors.WithMessagef(err, "consume record error: %+v", record)
			}
		}

		//reader.Iterator(func(record data.Record, innerErr error) {
		//	if innerErr != nil {
		//		err = errors.WithMessage(innerErr, "read record error")
		//		return
		//	}
		//	err = consumer(record)
		//})
		//return nil
	}
	return f
}
