package dump

import (
	"bufio"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/crypto/base64"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"dip-agent/pkg/util/json/sonic"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"testing"
	"time"
)

func TestService_SimpleDump(t *testing.T) {
	tests := []struct {
		enable    bool
		name      string
		requestId string
		fields    Info
		want      *dto.DumpResult
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			enable:    false,
			name:      "simple-dump-int64",
			requestId: "test1",
			fields: Info{
				IndexName:         "jiuwu_goods_rec",
				IndexVersion:      "202503151700",
				Sql:               `select json_data from du_algo_3.dwd_prd_jiuwu_rec where ds='202504171300' and get_json_object(json_data,'$.id') in (60425840629909246);`,
				Hints:             map[string]string{},
				DumpProcessorName: "faiss",
				//DumpProcessorName: "forward",
				SpecialDeal:   "[]",
				DataPath:      "/tmp/dump",
				ThreadNum:     8,
				BufferSize:    1048576,
				OdpsAccessKey: "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey: "****************************************",
				OdpsProject:   "du_algo_3",
				OdpsEndpoint:  "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      1000,
				Size:       1000,
				QueryCount: 1000,
				EmptyCount: 0,
				RequestId:  "test1",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			// content_type_form_v2_id 没有,case itemid:234987650
			enable:    false,
			name:      "simple-dump-spec-deal-int16",
			requestId: "test2",
			fields: Info{
				IndexName:    "dpp_community_unite_item_fill_info_gqw",
				IndexVersion: "20250416",
				Sql: `SELECT
  item_id,  item_type,  user_id,  create_time,  title,  videoduration,  videoduration as video_time,  topic_id,  circle_id,  mark_status,  first_mark_time,  not_recommend,  quality_marks,  quality_names,  cast(sex as string) as sex,  cast(sexy as string) as sexy,  tag_id,  tag_ids,  tag_mark_tag_ids,  cspu_id,  cast(group_lv1_code_time as string) as cspu_ids,  cast(group_lv1_code_target as string) as spu_ids,  mark_content_type,  sell_date,  down_right,  timeliness_time,  third_mark_time,  high_not_recommend,  selected_status,  community_brand,  commercial_status,  producttag_id,  is_new,  timeliness,  isdel,  rcmd_status,  rec_first_distribute_time,  ext_map,  update_time,  gather_tag_ids,  flow_level,  support_type,  tag_ids_v2,  topic_ids,  cluster_id,  report_status,  is_porter_account,  is_revert,  algo_content_aware_quality_score,  category_id,  category_names,  category_id_v2,  category_names_v2,  exposure_uv,  content_level,  candi_ids,  rcmd_tag_ids,  contentmark_tag_ids,  img_count,  bole_rank,  image_feat,  item_vec,  brand_id,  fit_id,  auth_price,  series_ids,  color,  base_color,  luxury_level,  age,  age_range,  gender,  register_time,  is_star,  is_kol,  last_release_time,  cspu_level1_category_ids,  cspu_category_ids,  item_content_len,  cspu_luxury_level,  item_have_face,  cluster_id_v2,  topic_ids_v2,category_id_v24,  case when item_type = 0 then 'trend' when item_type = 1 then 'video' else 'trend' end as it,  cast(user_id as string) as uid,  cast(cspu_id as string) as pid,  cast(group_lv1_code_time as string) as pids,  cast(category_id as string) as ncid,  cast(tag_id as string) as tag,  cast(tag_ids as string) as tags,  case when item_type in (0, 1) then concat('t', item_id) when item_type = 2 then concat('p', item_id) else item_id end as item_str,  cast(item_id as string) as ii,  ds
FROM du_algo_1.community_rcmd_pool_sensors_all_buquan_v2
WHERE ds = '20250416' and create_time > UNIX_TIMESTAMP(TO_DATE('************','yyyymmddhhmi')) - 3600 * 24 * 30 * 5 and ext_map like '%content_type_form_v2_id%' limit 100;`,
				Hints:             map[string]string{},
				DumpProcessorName: "faiss",
				//DumpProcessorName: "forward",
				SpecialDeal:   "[{\"dealContent\":\"{\\\"aggregateField\\\":\\\"ext_map\\\",\\\"extFields\\\":[{\\\"fieldName\\\":\\\"image_emb\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"video_duration\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"gather_tag_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"lvl4_tag_generated\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"size_spu_avlbl\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"ad_result\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"exp_content_pool_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_yelp\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"content_pool_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_gc\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"strategy_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"yinli\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"history_expo_num\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"image_count\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_screenshot\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_task_limit\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"high_expo_control_info\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"tmp_str\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_fan_acquisition\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"cutSign\\\":\\\",\\\",\\\"fieldName\\\":\\\"lv4_tag_v3\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":1},{\\\"fieldName\\\":\\\"first_cspu_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_spu_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_category_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_auth_price\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"user_product_list\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"ad_result_v2\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"candi_words\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"cid_v2\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_talent_support\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"actual_price\\\",\\\"fieldType\\\":\\\"float\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"industry\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_end\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_start\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_achieve_quota\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_target_quota\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_taolun\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"aesthetic_score\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"info_amount_score\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"content_type_form_v2_id\\\",\\\"fieldType\\\":\\\"int16\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"no_price_st\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"no_product_st\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0}]}\",\"dealType\":1}]",
				DataPath:      "/tmp/dump",
				ThreadNum:     8,
				BufferSize:    1048576,
				OdpsAccessKey: "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey: "****************************************",
				OdpsProject:   "du_algo_3",
				OdpsEndpoint:  "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      1,
				Size:       1000,
				QueryCount: 1,
				EmptyCount: 0,
				RequestId:  "test2",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			// brand_roi字段为decimal(38,18)，重点校验
			enable:    false,
			name:      "simple-dump-double",
			requestId: "test3",
			fields: Info{
				IndexName:         "supply_coupon_algo",
				IndexVersion:      "2025032615",
				Sql:               `SELECT * FROM du_algo_8.supply_coupon_algo WHERE pt = 2025032615 and biz_type=3 limit 100;`,
				Hints:             map[string]string{},
				DumpProcessorName: "faiss",
				DataPath:          "/tmp/dump",
				ThreadNum:         8,
				BufferSize:        1048576,
				OdpsAccessKey:     "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey:     "****************************************",
				OdpsProject:       "du_algo_8",
				OdpsEndpoint:      "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      1,
				Size:       1000,
				QueryCount: 1,
				EmptyCount: 0,
				RequestId:  "test3",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			enable:    false,
			name:      "simple-dump-all-type",
			requestId: "test4",
			fields: Info{
				IndexName:         "dip_all_type_field_gqw",
				IndexVersion:      "2025-04-08",
				Sql:               `select * from du_algo_3_dev.dip_all_type_field_2 where ds='2025-04-08';`,
				Hints:             map[string]string{},
				DumpProcessorName: "forward",
				//SpecialDeal:   "[{\"dealContent\":\"{\\\"aggregateField\\\":\\\"ext_map\\\",\\\"extFields\\\":[{\\\"fieldName\\\":\\\"image_emb\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"video_duration\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"gather_tag_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"lvl4_tag_generated\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"size_spu_avlbl\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"ad_result\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"exp_content_pool_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_yelp\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"content_pool_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_gc\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"strategy_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"yinli\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"history_expo_num\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"image_count\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_screenshot\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_task_limit\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"high_expo_control_info\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"tmp_str\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_fan_acquisition\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"cutSign\\\":\\\",\\\",\\\"fieldName\\\":\\\"lv4_tag_v3\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":1},{\\\"fieldName\\\":\\\"first_cspu_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_spu_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_category_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_auth_price\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"user_product_list\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"ad_result_v2\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"candi_words\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"cid_v2\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_talent_support\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"actual_price\\\",\\\"fieldType\\\":\\\"float\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"industry\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_end\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_start\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_achieve_quota\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_target_quota\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_taolun\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"aesthetic_score\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"info_amount_score\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"content_type_form_v2_id\\\",\\\"fieldType\\\":\\\"int16\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"no_price_st\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"no_product_st\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0}]}\",\"dealType\":1}]",
				DataPath:      "/tmp/dump",
				ThreadNum:     8,
				BufferSize:    1048576,
				OdpsAccessKey: "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey: "****************************************",
				OdpsProject:   "du_algo_3_dev",
				OdpsEndpoint:  "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      1,
				Size:       1000,
				QueryCount: 1,
				EmptyCount: 0,
				RequestId:  "test4",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			enable:    false,
			name:      "simple-dump-spec-deal-struct",
			requestId: "test5",
			fields: Info{
				IndexName:    "jiuwu_multitype_score_brand",
				IndexVersion: "202504142000",
				Sql: `select goods_id as key, 
    COLLECT_LIST(NAMED_STRUCT('star_id',star_id, "star_type" , star_type, "score", score  )) as docs
from du_algo_12.jiuwu_multitype_score_brand
where ht = 202504142000
group by goods_id limit 1000;`,
				Hints:             map[string]string{},
				DumpProcessorName: "ivt",
				SpecialDeal:       "[{\"dealContent\":\"{\\\"aggregateField\\\":\\\"ext_map\\\",\\\"extFields\\\":[{\\\"fieldName\\\":\\\"image_emb\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"video_duration\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"gather_tag_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"lvl4_tag_generated\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"size_spu_avlbl\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"ad_result\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"exp_content_pool_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_yelp\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"content_pool_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_gc\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"strategy_ids\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"yinli\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"history_expo_num\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"image_count\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_screenshot\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_task_limit\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"high_expo_control_info\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"tmp_str\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_fan_acquisition\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"cutSign\\\":\\\",\\\",\\\"fieldName\\\":\\\"lv4_tag_v3\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":1},{\\\"fieldName\\\":\\\"first_cspu_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_spu_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_category_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"first_auth_price\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"user_product_list\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"ad_result_v2\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"candi_words\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"cid_v2\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_talent_support\\\",\\\"fieldType\\\":\\\"int64\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"actual_price\\\",\\\"fieldType\\\":\\\"float\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"industry\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_id\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_end\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_task_start\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_achieve_quota\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"fop_target_quota\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"is_taolun\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"aesthetic_score\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"info_amount_score\\\",\\\"fieldType\\\":\\\"string\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"content_type_form_v2_id\\\",\\\"fieldType\\\":\\\"int16\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"no_price_st\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0},{\\\"fieldName\\\":\\\"no_product_st\\\",\\\"fieldType\\\":\\\"int32\\\",\\\"isArray\\\":0}]}\",\"dealType\":1}]",
				DataPath:          "/tmp/dump",
				ThreadNum:         8,
				BufferSize:        1048576,
				OdpsAccessKey:     "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey:     "****************************************",
				OdpsProject:       "du_algo_12",
				OdpsEndpoint:      "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      1,
				Size:       1000,
				QueryCount: 1,
				EmptyCount: 0,
				RequestId:  "test5",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			enable:    false,
			name:      "simple-dump-map-value",
			requestId: "test6",
			fields: Info{
				IndexName:         "deal_rec_user_brand_cross_feature_2fs",
				IndexVersion:      "20250422",
				Sql:               `select * from du_algo_6.deal_rec_user_brand_cross_feature_2fs where ds='20250422' and user_id=21597 limit 1;`,
				Hints:             map[string]string{},
				DumpProcessorName: "forward",
				DataPath:          "/tmp/dump",
				ThreadNum:         8,
				BufferSize:        1048576,
				OdpsAccessKey:     "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey:     "****************************************",
				OdpsProject:       "du_algo_6",
				OdpsEndpoint:      "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      1,
				Size:       1000,
				QueryCount: 1,
				EmptyCount: 0,
				RequestId:  "test6",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			enable:    false,
			name:      "simple-dump-map-jsondata",
			requestId: "test7",
			fields: Info{
				IndexName:         "deal_tab_struct_features",
				IndexVersion:      "20250422",
				Sql:               `select * from du_algo_1.deal_tab_struct_features where ds='20250422' and tab_key='tab:1000000' limit 1;`,
				Hints:             map[string]string{},
				DumpProcessorName: "forward",
				DataPath:          "/tmp/dump",
				ThreadNum:         8,
				BufferSize:        1048576,
				OdpsAccessKey:     "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey:     "****************************************",
				OdpsProject:       "du_algo_1",
				OdpsEndpoint:      "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      1,
				Size:       1000,
				QueryCount: 1,
				EmptyCount: 0,
				RequestId:  "test7",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			enable:    false,
			name:      "dip_all_type_field_gqw2",
			requestId: "test8",
			fields: Info{
				IndexName:         "dip_all_type_field_gqw2",
				IndexVersion:      "2025-05-08",
				Sql:               `select id,to_json(f17) as json_data from du_algo_3_dev.dip_all_type_field_2 where ds='2025-05-08';`,
				Hints:             map[string]string{},
				DumpProcessorName: "forward",
				DataPath:          "/tmp/dump",
				ThreadNum:         8,
				BufferSize:        1048576,
				OdpsAccessKey:     "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey:     "****************************************",
				OdpsProject:       "du_algo_1",
				OdpsEndpoint:      "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      9,
				Size:       132,
				QueryCount: 10,
				EmptyCount: 1,
				RequestId:  "test8",
				FileCount:  1,
			},
			wantErr: false,
		},
		{
			enable:    true,
			name:      "commodity_cspu_feature_new",
			requestId: "test9",
			fields: Info{
				IndexName:         "commodity_cspu_feature_new",
				IndexVersion:      "202507021200",
				Sql:               `select json_data from du_algo_3.dwd_commodity_cspu_feature_prd where ds=202507021200 and id in (1570854274,1585837247,1565993335);`,
				Hints:             map[string]string{},
				DumpProcessorName: "forward",
				DataPath:          "/tmp/dump",
				ThreadNum:         8,
				BufferSize:        1048576,
				OdpsAccessKey:     "TFRBSTV0SDczR1FhTmEzeHdVTmc5THJv",
				OdpsSecretKey:     "****************************************",
				OdpsProject:       "du_algo_3",
				OdpsEndpoint:      "http://service.odps.aliyun.com/api",
			},
			want: &dto.DumpResult{
				Count:      9,
				Size:       132,
				QueryCount: 10,
				EmptyCount: 1,
				RequestId:  "test9",
				FileCount:  1,
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)
	//json.SetDefaultEngine(jsoniter.Name)
	for _, tt := range tests {
		if !tt.enable {
			t.Logf("skip test: %s", tt.name)
			continue
		}
		t.Run(tt.name, func(t *testing.T) {
			// 清空索引的数据目录
			indexDataPath := filepath.Join(tt.fields.DataPath, tt.fields.IndexName, tt.fields.IndexVersion)
			err := os.RemoveAll(indexDataPath)
			if err != nil {
				err = errors.Wrapf(err, "remove index data path fail: %s", indexDataPath)
				t.Errorf("remove index data path fail: %s", err)
				return
			}

			// 先解密
			if tt.fields.OdpsAccessKey != "" {
				tt.fields.OdpsAccessKey, err = base64.Decrypt(tt.fields.OdpsAccessKey)
				if err != nil {
					t.Errorf("decrypt odps access key fail: %v", err)
					return
				}
				tt.fields.OdpsSecretKey, err = base64.Decrypt(tt.fields.OdpsSecretKey)
				if err != nil {
					t.Errorf("decrypt odps secret key fail: %v", err)
					return
				}
			}

			//s:= NewService("test1", &tt.fields)
			s, err := NewService(tt.requestId, &tt.fields)
			if err != nil {
				t.Errorf("NewService() error = %v", err)
				return
			}
			got, err := s.SimpleDump()
			if (err != nil) != tt.wantErr {
				t.Errorf("SimpleDump() error = %+v, wantErr %+v", err, tt.wantErr)
				return
			}

			// 打印dump文件内容
			//dumpFilePath := filepath.Join(indexDataPath, "0.done")
			//err = printDumpFileContent(dumpFilePath)
			//if err != nil {
			//	t.Errorf("printDumpFileContent() error = %v", err)
			//	return
			//}

			tt.want.DumpCost = got.DumpCost
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SimpleDump() got = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func TestOdpsSimpleDump(t *testing.T) {
	start := time.Now()
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)
	info := &Info{
		IndexName:    "outfits_trend_engine_v2",
		IndexVersion: "202409221200",
		Sql:          "select * from du_algo_3.outfits_trend_engine_v2_pre where pt='202409221200' and product_id=77320 and trend_id=38044733",
		Hints: map[string]string{
			"odps.sql.type.system.odps2": "true",
			"odps.sql.submit.mode":       "script",
			"odps.task.wlm.quota":        "algo_dip",
		},
		//DumpProcessorName: "ivt",
		DumpProcessorName: "faiss",
		//DataPath:          "/app/rel/data/tdata",
		DataPath:      "/tmp/dump/",
		ThreadNum:     8,
		BufferSize:    1048576,
		OdpsAccessKey: "TFRBSTV0N29uU3VSU3Rnc1R5OWhWUGhp",
		OdpsSecretKey: "****************************************",
		OdpsProject:   "du_algo_3",
		//OdpsEndpoint:  "http://service.cn-hangzhou.maxcompute.aliyun-inc.com/api",
		OdpsEndpoint: "http://service.odps.aliyun.com/api",
		JsonName:     "sonic",
	}
	content, err := json.Marshal(info)
	if err == nil {
		log.Info("request body: %s", string(content))
	} else {
		log.Error("request body: %#v", info)
		return
	}
	// 先解密
	if info.OdpsAccessKey != "" {
		info.OdpsAccessKey, err = base64.Decrypt(info.OdpsAccessKey)
		if err != nil {
			t.Errorf("decrypt odps access key fail: %v", err)
			return
		}
		info.OdpsSecretKey, err = base64.Decrypt(info.OdpsSecretKey)
		if err != nil {
			t.Errorf("decrypt odps secret key fail: %v", err)
			return
		}
	}
	s, err := NewService("test1", info)
	if err != nil {
		t.Errorf("NewService() error = %+v", err)
		return
	}
	got, err := s.SimpleDump()
	if err != nil {
		t.Errorf("SimpleDump() error = %v", err)
		return
	}
	t.Logf("SimpleDump() got = %v", got)
	log.Info("cost time: %s", util.DurationToHumanReadable(time.Since(start)))
}

func Test2(t *testing.T) {
	data := map[string]interface{}{
		"actp":                    "0",
		"adjustswitch":            0,
		"adjustswitchv2":          0,
		"an":                      "T3212401L",
		"bclr":                    "银",
		"bi":                      1012319,
		"brand_level":             "D",
		"category_lv2_type":       1,
		"ci":                      "38",
		"clr":                     "太空银(涂鸦)",
		"ctr":                     0.02,
		"ds":                      "20240927130000",
		"fi":                      1,
		"filter_type":             "0",
		"fit_season":              "",
		"fit_season_n":            "春,夏,秋,冬",
		"has_discount":            0,
		"ics":                     "0.0575,0.18,0.0225,0.1362,1.0",
		"ii":                      1576888094,
		"inventory":               0,
		"is_95p":                  0,
		"is_blocked":              0,
		"is_brand_price":          1,
		"is_hexpo_lcvs":           0,
		"is_neg":                  0,
		"is_new_prod_promote":     0,
		"is_offseason":            0,
		"is_picfilter":            0,
		"is_sensitive":            0,
		"isb":                     true,
		"isn":                     false,
		"it":                      "product",
		"l1ci":                    "29",
		"l2ci":                    "35",
		"ll":                      1,
		"lln":                     1,
		"low_inventory":           0,
		"low_price":               0,
		"low_type":                0,
		"max_sale_cspu_id":        "1569867641",
		"maxsaleprice":            "0",
		"mspid":                   421056448,
		"new_adjust_type":         5,
		"new_exp_tag":             -1,
		"new_filter_type":         "0",
		"new_spug":                0,
		"on_sale_size":            "35,36,37,38,39,40,41,42,43,44,45",
		"pid":                     487155794,
		"pool_type":               "",
		"price":                   19900,
		"price_level":             "2",
		"price_white_flag":        1,
		"priceadvantage":          0,
		"promflag":                "0",
		"recommend_increase_rate": -0.08464151294619116,
		"sale_labels":             "3721",
		"sc":                      0.30021,
		"sc2":                     0.30021,
		"score_map": map[interface{}]interface{}{
			"marketput_click_pv_rate":      0.14143094841930118,
			"marketput_order_rate":         0.15384615384615385,
			"marketput_prd_access_pv_rate": 0.19707467282525018,
			"recommend_increase_rate":      -0.08464151294619116,
		},
		"sensitive_type":  0,
		"si":              0,
		"sim_group_id":    0,
		"soldnum":         "46",
		"spug":            309056,
		"spuid":           "6827355",
		"ssi":             0,
		"suit_spu":        "",
		"t_spu_hot_level": 0,
		"title":           "TCELLARS 空军一号 秋季出游 英伦百搭休闲通勤厚底小白鞋 限定礼盒 防滑防水减震耐磨轻便透气增高 低帮 板鞋 男女同款",
		"ue":              0,
		"url":             "https://cdn.poizon.com/pro-img/origin-img/20240809/c11566ff564f4b7185b17c6c684bdf32.jpg",
		"weighted":        1,
		"whtype":          0,
	}

	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	log.Info("data len: %d", len(data))

	content, err := json.Marshal(data)
	if err != nil {
		log.Error("marshal error: %+v", err)
	}
	log.Info("content: %s", string(content))
}

func printDumpFileContent(dumpFilePath string) error {
	// 获取写入到文件的数据，并反序列化
	// 读取文件
	file, err := os.Open(dumpFilePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 按行读取文件内容
	lines := make([]string, 0)
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		log.Info("dump result line: %s", line)
		lines = append(lines, line)
	}
	if err := scanner.Err(); err != nil {
		return err
	}

	// 遍历每一行
	for i, l := range lines {
		log.Info("dump result line %d: %s", i, l)
		// 反序列化为 map[string]interface{}
		var data map[string]interface{}
		err = json.Unmarshal([]byte(l), &data)
		if err != nil {
			return err
		}
		log.Info("dump data size: %d", len(data))
		// map中的 key 排序打印
		keys := make([]string, 0, len(data))
		for k := range data {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		for _, k := range keys {
			log.Info("[%d]%s: %v", i, k, data[k])
		}
	}
	return nil
}

func Test1(t *testing.T) {
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	json_data:="{\"item_id\":1077,\"img_emb\":[0.000135,0.034223,0.023418,0.034857,0.055646,0.045715,0.013839,0.016712]}"
	dataMap:=make(map[string]interface{})
	err := json.Unmarshal([]byte(json_data), &dataMap)
	if err != nil {
		log.Error("unmarshal error: %+v", err)
		return
	}
	content, err := json.MarshalToString(dataMap)
	if err != nil {
		log.Error("marshal error: %+v", err)
		return
	}
	log.Info("dataMap: %s", content)
}
