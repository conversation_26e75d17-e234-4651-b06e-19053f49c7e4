package mock

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/exec"
	"dip-agent/pkg/util"
	"net/http"
)

const echoPath = "/api/mock/build/echo"

func init() {
	web.RegisterHttpHandler[string](&EchoHttpHandler{})
}

type EchoHttpHandler struct {
}

func (h *EchoHttpHandler) Path() string {
	return echoPath
}

func (h *EchoHttpHandler) Method() string {
	return http.MethodGet
}

func (h *EchoHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	// 获取请求的query string
	query := r.URL.Query()
	// 将query转为map[string]interface{}
	carrier := make(map[string]interface{})
	for k, v := range query {
		carrier[k] = v[0]
	}
	// 获取执行任务的名称
	taskName := carrier["taskName"].(string)
	// 从header中获取traceId
	requestId, r := util.GetRequestId(r)
	log.Info("echo requestId: %s", requestId)

	//// 执行
	//ctx := api.ProcessContext{
	//	RequestId: requestId,
	//	Carrier:   carrier,
	//}
	//result := task.Process(taskName, ctx)

	// 随机生成taskId
	taskId := util.GenerateUUID()
	log.Info("echo taskId: %s", taskId)

	var apiResp *api.Response[string]

	//t, ed := exec.SubmitTaskWith(requestId, taskId, taskName, make(cfg.CommonCfg))
	t := exec.NewTask(requestId, taskId, taskName, carrier)
	t.NoCallback = true
	err := exec.SubmitTask(t)
	if err != nil {
		apiResp = api.SystemFail[string](err)
		apiResp.RequestId = requestId
	} else {
		tr := <-t.Result()
		apiResp = api.FromResult[string](tr)
	}
	apiResp.ResponseData = taskId
	return apiResp
}
