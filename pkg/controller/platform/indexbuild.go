package platform

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/exec"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
	"net/http"
)

type IndexBuildRequest struct {
	TaskId                      string                 `json:"taskId" validate:"required"`                                             // 必填. 任务ID
	TemplateName                string                 `json:"templateName" validate:"required" default:"index-build"`                 // 必填. 模板名称
	StartStepName               string                 `json:"startStepName"`                                                          // 非必填. 开始步骤名称. 不传则从头开始
	DataPath                    string                 `json:"dataPath,omitempty" default:"/app/rel/data/tdata" validate:"required"`   // 拉取的odps的数据存放目录
	DbPath                      string                 `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`       // 引擎构建出的索引存放目录
	LogPath                     string                 `json:"logPath,omitempty" default:"/logs/engine" validate:"required"`           // 引擎构建日志存放目录
	MetadataPath                string                 `json:"metadataPath" default:"/app/rel/config/.metadata" validate:"required"`   // 元数据文件路径
	SchemaPath                  string                 `json:"schemaPath" default:"/app/rel/config/.schema" validate:"required"`       // schema文件路径
	ConfigPath                  string                 `json:"configPath,omitempty" default:"/app/rel/config" validate:"required"`     // 配置目录
	TempDataPath                string                 `json:"tempDataPath,omitempty" default:"/app/rel/data/tmp/tdata"`               // 临时数据目录
	Compress                    *bool                  `json:"compress,omitempty" default:"false"`                                     // 是否压缩, 默认false
	ShardNum                    int                    `json:"shardNum"`                                                               // 分片数
	ShardId                     int                    `json:"shardId"`                                                                // 分片ID
	MaxFailRate                 int                    `json:"maxFailRate" validate:"required" default:"10"`                           // 最大失败比例。默认10，表示10%
	IndexDef                    *dto.IndexDef          `json:"indexDef" validate:"dive,required"`                                      // 索引定义
	ReportUrl                   string                 `json:"reportUrl" validate:"required,url"`                                      // 必填. 状态上报url
	CallbackUrl                 string                 `json:"callbackUrl" validate:"required,url"`                                    // 必填. 执行结果回调url
	ExtraConfig                 map[string]interface{} `json:"extraConfig,omitempty"`                                                  // 额外配置
	Datasource                  *dto.Datasource        `json:"datasource,omitempty" validate:"required,dive"`                          // 拉取的表数据源信息
	DataTarget                  *dto.Datasource        `json:"dataTarget,omitempty" validate:"required,dive"`                          // 构建的索引存储目标数据源信息
	EngineBuildCmdPath          string                 `json:"engineBuildCmdPath,omitempty" default:"/bin/build-engine"`               // engine build的二进制文件路径
	EngineBuildCmdArgs          []string               `json:"engineBuildCmdArgs,omitempty"`                                           // engine build的运行参数, 除了-c /xxx/build-request.json之外的参数
	EngineBuildConfigFileName   string                 `json:"engineBuildConfigFileName,omitempty" default:"build-request.json"`       // engine build的配置文件名称
	DumpResultFileName          string                 `json:"dumpResultFileName,omitempty" default:"dump-result.json"`                // dump结果文件名称
	ChecksumFileName            string                 `json:"checksumFileName,omitempty" default:"checksum.json"`                     // 校验和文件名称
	Checksum                    *bool                  `json:"checksum,omitempty" default:"true"`                                      // 是否进行文件完整性校验
	DumpProcessorName           string                 `json:"dumpProcessorName" validate:"required"`                                  // dump processor name
	SpecialDeal                 string                 `json:"specialDeal,omitempty"`                                                  // 字段特殊处理
	ClusterCode                 string                 `json:"clusterCode,omitempty" validate:"required"`                              // 集群code
	ClusterGroup                string                 `json:"clusterGroup,omitempty" validate:"required"`                             // 集群分组
	Sync                        *bool                  `json:"sync" default:"false"`                                                   // 可选. 是否同步返回结果, 默认false
	CleanDataDir                *bool                  `json:"cleanDataDir,omitempty" default:"false"`                                 // 是否清空dataPath目录, 不清理将断点续传拉取odps数据
	UploadLog                   *bool                  `json:"uploadLog,omitempty" default:"true"`                                     // 是否上传日志
	EngineBuildLogFileName      string                 `json:"engineBuildLogFileName,omitempty" default:"engine-build.log"`            // engine build日志文件名称
	InnerEngineBuildLogFileName string                 `json:"innerEngineBuildLogFileName,omitempty" default:"inner-engine-build.log"` // 内部engine build日志文件名称, 用来输出build-engin的标准输出
	EnableEmptyData             *bool                  `json:"enableEmptyData,omitempty" default:"false"`                              // 是否允许空数据
}

type IndexBuildTaskHttpHandler struct {
}

func NewIndexBuildTaskHttpHandler() *IndexBuildTaskHttpHandler {
	return &IndexBuildTaskHttpHandler{}
}

func (h *IndexBuildTaskHttpHandler) Path() string {
	return "/api/v1/platform/start-build-task"
}

func (h *IndexBuildTaskHttpHandler) Method() string {
	return http.MethodPost
}

func (h *IndexBuildTaskHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	var request IndexBuildRequest
	requestId, r := util.GetRequestId(r)
	err := cfg.UnpackReaderWithJson(r.Body, &request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(request)
	log.Info("[%s]start index build task request: %s", requestId, string(requestJson))

	// 执行任务pipeline
	return runBuildIndexTask(requestId, &request)
}

func runBuildIndexTask(requestId string, ibr *IndexBuildRequest) *api.Response[string] {
	// 执行任务pipeline
	carrier, err := cfg.NewCommonCfgWithJson(ibr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	// carry index info
	carrier.Put("indexName", ibr.IndexDef.IndexName)
	carrier.Put("indexVersion", ibr.IndexDef.IndexVersion)
	carrier.Put("indexExtraConfig", ibr.IndexDef.ExtraConfig)
	// carry odps
	odps, err := ibr.Datasource.GetOdps()
	if err != nil {
		err = errors.WithMessage(err, "invalid param: get odps info fail")
		return api.ParamFail[string](err)
	}
	carrier.Put("odps", odps)
	// carry oss
	oss, err := ibr.DataTarget.GetOss()
	if err != nil {
		err = errors.WithMessage(err, "invalid param: get oss info fail")
		return api.ParamFail[string](err)
	}
	carrier.Put("oss", oss)

	// carry agentConfig
	// 调用平台接口获取
	agentConfig, err := platform.NewGetClusterConfigRequest(requestId).SendWithRetry()
	if err != nil {
		err = errors.WithMessage(err, "get cluster agent config fail")
		return api.SystemFail[string](err)
	}
	carrier.Put("agentConfig", agentConfig)

	// 执行任务
	t, err := exec.SubmitTaskStartFrom(requestId, ibr.TaskId, ibr.TemplateName, carrier, ibr.StartStepName)
	if err != nil {
		err = errors.WithMessagef(err, "submit task fail, task id: %s", ibr.TaskId)
		return api.SystemFail[string](err)
	}

	// 是否需要同步返回结果
	if *ibr.Sync {
		tr := <-t.Result()
		return api.FromResult[string](tr)
	}
	return api.OfSuccess[string]("start index build task success")
}
