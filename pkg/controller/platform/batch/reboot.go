package batch

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/exec"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
	"net/http"
	"time"
)

func init() {
	web.RegisterHttpHandler[string](NewRebootTaskHttpHandler())
}

type RebootTaskRequest struct {
	TaskId                 string        `json:"taskId" validate:"required"`                                     // 必填. 任务ID
	TemplateName           string        `json:"templateName" validate:"required" default:"batch-reboot-reload"` // 必填. 模板名称
	StartStepName          string        `json:"startStepName"`                                                  // 非必填. 开始步骤名称. 不传则从头开始
	ReportUrl              string        `json:"reportUrl" validate:"required,url"`                              // 必填. 状态上报url
	CallbackUrl            string        `json:"callbackUrl" validate:"required,url"`                            // 必填. 执行结果回调url
	Tasks                  []*RebootTask `json:"tasks" validate:"required,min=1,dive"`                           // 必填. 任务列表
	Sync                   *bool         `json:"sync" default:"false"`                                           // 可选. 是否同步返回结果, 默认false
	IsSkipWaitInc          *bool         `json:"isSkipWaitInc,omitempty" default:"false"`                        // 是否跳过等待追增量
	WaitServerStartTimeout time.Duration `json:"waitServerStartTimeout,omitempty" default:"10m"`                 // 等待服务启动的超时时间
}

type RebootTask struct {
	*dto.UpdateIndexInfo

	Datasource *dto.Datasource `json:"datasource" validate:"required,dive"` // 必填. 数据源, 目前为oss
}

type RebootTaskHttpHandler struct {
}

func NewRebootTaskHttpHandler() *RebootTaskHttpHandler {
	return &RebootTaskHttpHandler{}
}

func (h *RebootTaskHttpHandler) Path() string {
	return "/api/v1/platform/start-batch-restart-update-task"
}

func (h *RebootTaskHttpHandler) Method() string {
	return http.MethodPost
}

func (h *RebootTaskHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	var request RebootTaskRequest
	requestId, r := util.GetRequestId(r)
	err := cfg.UnpackReaderWithJson(r.Body, &request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(request)
	log.Info("[%s]start reboot reload task request: %s", requestId, string(requestJson))

	// 执行任务pipeline
	return RunRebootTask(&request, requestId)
}

func RunRebootTask(trr *RebootTaskRequest, requestId string) *api.Response[string] {
	// 预设oss信息
	tasks := trr.Tasks
	for _, task := range tasks {
		task.CanUpdate = true

		oss, err := task.Datasource.GetOss()
		if err != nil {
			err = errors.WithMessage(err, "invalid param: get oss info fail")
			return api.ParamFail[string](err)
		}
		task.Oss = oss
		task.ReportUrl = trr.ReportUrl
		task.CallbackUrl = trr.CallbackUrl
	}

	// 执行pipeline上下文
	carrier, err := cfg.NewCommonCfgWithJson(trr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	carrier.Put("isReboot", true)
	carrier.Put("callbackUrl", tasks[0].CallbackUrl)
	carrier.Put("reportUrl", tasks[0].ReportUrl)

	// stop&start server 相关参数
	carrier.Put("engineServerShellCmdPath", tasks[0].EngineServerShellCmdPath)
	carrier.Put("engineShellPath", tasks[0].EngineShellPath)
	carrier.Put("engineServerStopCmdArgs", tasks[0].EngineServerStopCmdArgs)
	carrier.Put("engineServerStopCheckCmdArgs", tasks[0].EngineServerStopCheckCmdArgs)
	carrier.Put("engineServerStartCmdArgs", tasks[0].EngineServerStartCmdArgs)

	// 执行任务
	t, err := exec.SubmitTaskStartFrom(requestId, trr.TaskId, trr.TemplateName, carrier, trr.StartStepName)
	if err != nil {
		err = errors.WithMessagef(err, "submit task fail: %s", trr.TaskId)
		return api.SystemFail[string](err)
	}

	// 是否需要同步返回结果
	if *trr.Sync {
		tr := <-t.Result()
		return api.FromResult[string](tr)
	}
	return api.OfSuccess[string]("start reboot reload task success")
}
