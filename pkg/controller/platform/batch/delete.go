package batch

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/exec"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
	"net/http"
)

func init() {
	web.RegisterHttpHandler[string](NewDeleteTaskHttpHandler())
}

type DeleteTaskRequest struct {
	TaskId        string                 `json:"taskId" validate:"required"`                                              // 必填. 任务ID
	TemplateName  string                 `json:"templateName" validate:"required" default:"batch-hotreload-delete-index"` // 必填. 模板名称
	StartStepName string                 `json:"startStepName"`                                                           // 非必填. 开始步骤名称. 不传则从头开始
	ReportUrl     string                 `json:"reportUrl" validate:"required,url"`                                       // 必填. 状态上报url
	CallbackUrl   string                 `json:"callbackUrl" validate:"required,url"`                                     // 必填. 执行结果回调url
	Tasks         []*dto.DeleteIndexInfo `json:"tasks" validate:"required,min=1,dive"`                                    // 必填. 任务列表
	Sync          *bool                  `json:"sync" default:"false"`                                                    // 可选. 是否同步返回结果, 默认false
}

type DeleteTaskHttpHandler struct {
}

func NewDeleteTaskHttpHandler() *DeleteTaskHttpHandler {
	return &DeleteTaskHttpHandler{}
}

func (h *DeleteTaskHttpHandler) Path() string {
	return "/api/v1/platform/start-batch-index-delete-task"
}

func (h *DeleteTaskHttpHandler) Method() string {
	return http.MethodPost
}

func (h *DeleteTaskHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	var request DeleteTaskRequest
	requestId, r := util.GetRequestId(r)
	err := cfg.UnpackReaderWithJson(r.Body, &request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(request)
	log.Info("[%s]start reboot reload task request: %s", requestId, string(requestJson))

	// 执行任务pipeline
	return RunDeleteTask(&request, requestId)
}

func RunDeleteTask(trr *DeleteTaskRequest, requestId string) *api.Response[string] {
	// 执行pipeline上下文
	carrier, err := cfg.NewCommonCfgWithJson(trr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	carrier.Put("isReboot", false)

	// 执行任务
	t, err := exec.SubmitTaskStartFrom(requestId, trr.TaskId, trr.TemplateName, carrier, trr.StartStepName)
	if err != nil {
		err = errors.WithMessagef(err, "submit task fail: %s", trr.TaskId)
		return api.SystemFail[string](err)
	}

	// 是否需要同步返回结果
	if *trr.Sync {
		tr := <-t.Result()
		return api.FromResult[string](tr)
	}
	return api.OfSuccess[string]("start reboot reload task success")
}
