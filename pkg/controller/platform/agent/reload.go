package agent

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/monitor"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/oss"
	"dip-agent/pkg/util/retry"
	"dip-agent/script"
	"fmt"
	"github.com/pkg/errors"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
)

const tmpScriptPrefix = "agent-restart-script-"

func init() {
	web.RegisterHttpHandler[string](NewReloadHttpHandler())
}

type ReloadRequest struct {
	Datasource     *dto.Datasource `json:"datasource,omitempty" validate:"required,dive"`                         // 拉取的agent的数据源
	LocalAgentPath string          `json:"localAgentPath,omitempty" validate:"required" default:"/app/rel/agent"` // 必填. 本地agent路径
	Version        string          `json:"version,omitempty" validate:"required"`                                 // 必填. 版本号
	// OssFilePath string `json:"ossFilePath,omitempty" validate:"required"` // 必填. oss文件路径
}

type ReloadHttpHandler struct {
}

func NewReloadHttpHandler() *ReloadHttpHandler {
	return &ReloadHttpHandler{}
}

func (h *ReloadHttpHandler) Path() string {
	return "/api/v1/agent/hot-reload"
}

func (h *ReloadHttpHandler) Method() string {
	return http.MethodPost
}

func (h *ReloadHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	var rr ReloadRequest
	requestId, r := util.GetRequestId(r)
	err := cfg.UnpackReaderWithJson(r.Body, &rr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(rr)
	log.Info("[%s]start reload agent(%s) request: %s", requestId, rr.Version, string(requestJson))

	ossConfig, err := rr.Datasource.GetOss()
	if err != nil {
		err = errors.WithMessage(err, "invalid param: get oss config fail")
		return api.ParamFail[string](err)
	}

	// 下载agent文件
	client, err := oss.NewClient(ossConfig.Endpoint, ossConfig.Bucket, ossConfig.AccessKey, ossConfig.SecretKey)
	if err != nil {
		err = errors.WithMessage(err, "create oss client fail")
		return api.SystemFail[string](err)
	}
	// localAgentPath:=filepath.Join(rr.LocalAgentPath, fmt.Sprintf("agent-%s", rr.Version))
	localAgentPath := filepath.Join(rr.LocalAgentPath, api.AgentName)
	err = retry.DoRetryWithClue(func() error {
		return client.DownloadFile(ossConfig.Dir, localAgentPath)
	}, requestId)
	if err != nil {
		err = errors.WithMessagef(err, "download agent file fail: ossPath: %s, localPath: %s", ossConfig.Dir, localAgentPath)
		return api.SystemFail[string](err)
	}
	// 设置可执行权限
	err = util.MakeFileExecutable(localAgentPath)
	if err != nil {
		err = errors.WithMessage(err, "make agent file executable fail")
		return api.SystemFail[string](err)
	}
	// 重新设置软链
	// 执行supervisorctl restart agent
	// cmdArgs := []string{"supervisorctl", "restart", "agent"}

	// 异步执行重启agent的脚本
	// clue := fmt.Sprintf("[%s]reload agent", requestId)
	// cmdArgs := []string{"nohup", localAgentPath, "-log.enableFile=true", "-log.noColor=true", "-log.dir=" + rr.LogDir, "&"}
	// util.ExecuteScript(clue, "nohup")

	// 将嵌入的 Python 脚本写入临时文件
	// 创建临时脚本文件
	tempFile, err := os.CreateTemp("", tmpScriptPrefix+"*.py")
	if err != nil {
		err = errors.WithMessagef(err, "create temp file fail: %s", tempFile.Name())
		return api.SystemFail[string](err)
	}

	// 写入临时文件
	pyContent, err := script.GetRestartScript()
	if err != nil {
		err = errors.WithMessage(err, "get Python script fail")
		return api.SystemFail[string](err)
	}
	_, err = tempFile.Write(pyContent)
	if err != nil {
		err = errors.WithMessage(err, "write Python script to temp file fail")
		return api.SystemFail[string](err)
	}
	tempFile.Close()

	// 先停掉agent-monitor
	monitor.StopMonitor()

	// Python命令和脚本的路径
	pythonCommand := "python"
	scriptPath := tempFile.Name()
	// cmd := exec.Command(pythonCommand, scriptPath)
	// log.Info("Run Python script: %s", cmd.String())
	// 执行脚本
	execCmdArgs := []string{scriptPath, "-p", strconv.Itoa(api.Pid)}
	agentLogFilePath := log.FilePath()
	clue := fmt.Sprintf("[%s]reload agent: %s", requestId, rr.Version)
	execCmd, err := util.ExecuteScriptToFileAsync(clue, pythonCommand, execCmdArgs, agentLogFilePath)
	if err != nil {
		err = errors.WithMessagef(err, "execute Python script fail: %s", execCmd)
		return api.SystemFail[string](err)
	}

	return api.OfSuccess("agent reload started...")
}
