package target

import (
	"dip-agent/pkg/core/log"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestProcessTargetFileUpload(t *testing.T) {
	// 初始化日志
	log.InitDefaultLogger()

	// 创建临时目录作为target目录
	targetDir, err := os.MkdirTemp("", "target_test_*")
	if err != nil {
		t.Fatalf("failed to create temp target dir: %v", err)
	}
	defer os.RemoveAll(targetDir)

	// 创建临时测试文件
	testFile, err := os.CreateTemp("", "test_upload_*.yaml")
	if err != nil {
		t.Fatalf("failed to create temp test file: %v", err)
	}
	defer os.Remove(testFile.Name())

	// 写入有效的target YAML内容
	testContent := `env: test
id: 1
mustFinish: false
cluster:
  id: 1
  name: test-cluster
  group: test-group
targetInfo:
  - name: test-index
    dataVersion: ["20250128"]
    buildVersion: [1001]
    type: index
    updateType: hot`

	if _, err := testFile.WriteString(testContent); err != nil {
		t.Fatalf("failed to write test content: %v", err)
	}
	testFile.Close()

	// 重新打开文件用于读取
	file, err := os.Open(testFile.Name())
	if err != nil {
		t.Fatalf("failed to open test file: %v", err)
	}
	defer file.Close()

	testFileName := filepath.Base(testFile.Name())
	requestId := "test-request-123"
	fileSize := int64(len(testContent))

	// 执行上传处理
	response, err := processTargetFileUpload(requestId, testFileName, targetDir, file)

	// 检查结果
	if err != nil {
		t.Fatalf("processTargetFileUpload failed: %v", err)
	}

	if response == nil {
		t.Fatal("response is nil")
	}

	// 验证响应字段
	if response.FileName != testFileName {
		t.Errorf("expected FileName %s, got %s", testFileName, response.FileName)
	}

	if response.FileSize != fileSize {
		t.Errorf("expected FileSize %d, got %d", fileSize, response.FileSize)
	}

	// 检查文件是否真的被创建
	expectedPath := filepath.Join(targetDir, testFileName)
	if _, err := os.Stat(expectedPath); os.IsNotExist(err) {
		t.Errorf("uploaded file does not exist at %s", expectedPath)
	} else {
		// 验证文件内容
		content, err := os.ReadFile(expectedPath)
		if err != nil {
			t.Errorf("failed to read uploaded file: %v", err)
		} else if string(content) != testContent {
			t.Errorf("file content mismatch. expected: %s, got: %s", testContent, string(content))
		}
	}

	// 验证target解析是否成功
	if response.target == nil {
		t.Error("target should be parsed successfully")
	} else {
		if response.target.Env != "test" {
			t.Errorf("expected target env 'test', got '%s'", response.target.Env)
		}
		if len(response.target.TargetInfo) != 1 {
			t.Errorf("expected 1 target info, got %d", len(response.target.TargetInfo))
		}
	}

	t.Logf("Test passed. Response: %+v", response)
}

func TestProcessTargetFileUploadReplacement(t *testing.T) {
	// 初始化日志
	log.InitDefaultLogger()

	testFileName := "test-replace.txt"
	requestId := "test-request-replace"
	targetDir := "/tmp/target"
	targetPath := filepath.Join(targetDir, testFileName)

	// 确保target目录存在
	os.MkdirAll(targetDir, 0755)

	// 先创建一个已存在的文件
	existingContent := "existing content"
	err := os.WriteFile(targetPath, []byte(existingContent), 0644)
	if err != nil {
		t.Fatalf("failed to create existing file: %v", err)
	}

	// 新的文件内容
	newContent := "new content that should replace the existing one"
	fileReader := strings.NewReader(newContent)

	// 执行上传处理
	response, err := processTargetFileUpload(requestId, testFileName, targetDir, fileReader)

	// 检查结果
	if err != nil {
		t.Fatalf("processTargetFileUpload failed: %v", err)
	}

	// 验证IsReplaced字段
	if !response.IsReplaced {
		t.Error("expected IsReplaced to be true")
	}

	// 验证文件内容被替换
	content, err := os.ReadFile(targetPath)
	if err != nil {
		t.Errorf("failed to read replaced file: %v", err)
	} else if string(content) != newContent {
		t.Errorf("file content not replaced. expected: %s, got: %s", newContent, string(content))
	}

	// 清理测试文件
	os.Remove(targetPath)

	t.Logf("Replacement test passed. Response: %+v", response)
}
