package platform

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/exec"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/retry"
	"github.com/pkg/errors"
	"net/http"
	"time"
)

func init() {
	web.RegisterHttpHandler[string](NewStartHotUpdateTaskHttpHandler())
}

type StartHotUpdateTaskRequest struct {
	TaskId                        string                 `json:"taskId" validate:"required"`                                                  // 必填. 任务ID
	TemplateName                  string                 `json:"templateName" validate:"required" default:"hot-reload"`                       // 必填. 模板名称
	StartStepName                 string                 `json:"startStepName"`                                                               // 非必填. 开始步骤名称. 不传则从头开始
	IndexName                     string                 `json:"indexName" validate:"required"`                                               // 必填. 索引名称
	IndexVersion                  string                 `json:"indexVersion" validate:"required"`                                            // 必填. 索引版本
	DbPath                        string                 `json:"dbPath,omitempty" default:"/app/rel/data/cdb" validate:"required"`            // 引擎构建出的索引存放目录
	GzPath                        string                 `json:"gzPath,omitempty" default:"/app/rel/data/gz" validate:"required"`             // 临时存放gz文件的目录
	Datasource                    dto.Datasource         `json:"datasource" validate:"required,dive"`                                         // 必填. 数据源, 目前为oss
	Compress                      bool                   `json:"compress" default:"false"`                                                    // 可选. 是否压缩, 默认false
	CheckBuildResult              *bool                  `json:"checkBuildResult,omitempty" default:"true"`                                   // 是否进行索引构建结果校验
	Checksum                      *bool                  `json:"checksum,omitempty" default:"true"`                                           // 是否进行文件完整性校验
	ChecksumFileName              string                 `json:"checksumFileName,omitempty" default:"checksum.json"`                          // 校验和文件名称
	IncWaitTimeoutSeconds         int32                  `json:"incWaitTimeoutSeconds" default:"1800"`                                        // 可选. 等待追增量的超时时间, 默认1800s
	IncOffsetThreshold            int64                  `json:"incOffsetThreshold" default:"120" validate:"gte=1"`                           // 可选. 追增量的offset阈值, 默认120
	WaitIncCount                  int                    `json:"waitIncCount,omitempty" default:"60"`                                         // 等待追增量的最大重试次数
	ExtraConfig                   map[string]interface{} `json:"extraConfig"`                                                                 // 可选. 额外配置
	ReportUrl                     string                 `json:"reportUrl" validate:"required,url"`                                           // 必填. 状态上报url
	CallbackUrl                   string                 `json:"callbackUrl" validate:"required,url"`                                         // 必填. 执行结果回调url
	Sync                          *bool                  `json:"sync" default:"false"`                                                        // 可选. 是否同步返回结果, 默认false
	DiskUsageThreshold            int                    `json:"diskUsageThreshold,omitempty" default:"93"`                                   // 磁盘使用阈值, 默认93%
	MemoryUsageThreshold          int                    `json:"memoryUsageThreshold,omitempty" default:"93"`                                 // 内存使用阈值, 默认93%
	MemoryInflateFactor           int                    `json:"memoryInflateFactor,omitempty" default:"5"`                                   // 索引加载到内存的膨胀比率, 默认5%
	IsInc                         *bool                  `json:"isInc" default:"true"`                                                        // 是否增量索引
	IsDoubleVersion               *bool                  `json:"isDoubleVersion" validate:"required" default:"false"`                         // 是否双版本索引
	NeedCommit                    *bool                  `json:"needCommit" default:"true"`                                                   // 是否需要commit
	IsVersionEqualCover           *bool                  `json:"isVersionEqualCover,omitempty" default:"true"`                                // 本地存在相同版本, 是否覆盖下载(只针对热更新生效, 重启更新肯定覆盖)
	ServingVersions               []string               `json:"servingVersions"`                                                             // 索引最近更新成功的n个版本(即双版本索引应该保留的n个版本). 默认为n=2
	LoadType                      string                 `json:"loadType"`                                                                    // 加载类型
	HotReloadCheckWaitTime        time.Duration          `json:"hotReloadCheckWaitTime" default:"5s"`                                         // 热更新check的等待时间
	HotReloadCheckTimeout         time.Duration          `json:"hotReloadCheckTimeout" default:"5m"`                                          // 热更新check的超时时间
	UnloadCheckWaitTime           time.Duration          `json:"unloadCheckWaitTime" default:"5s"`                                            // 卸载索引检查等待时间
	UnloadCheckTimeout            time.Duration          `json:"unloadCheckTimeout" default:"5m"`                                             // 卸载索引检查超时时间
	MoveWithRsync                 *bool                  `json:"moveWithRsync,omitempty"`                                                     // 是否使用rsync移动索引
	IsSkipWaitInc                 *bool                  `json:"isSkipWaitInc,omitempty" default:"false"`                                     // 是否跳过等待追增量
	SleepTimeAfterUnload          time.Duration          `json:"sleepTimeAfterUnload" default:"18s"`                                          // 卸载索引后等待时间
	BackupDiskUsageRatio          int                    `json:"backupDiskUsageRatio"`                                                        // 集群索引备份磁盘使用率阈值
	BackupDir                     string                 `json:"backupIndexDir,omitempty" default:"/app/rel/data/backup" validate:"required"` // 备份索引的路径
	IsRollback                    *bool                  `json:"isRollback,omitempty"`                                                        // 是否回滚
	HotReloadRsyncMemoryThreshold int                    `json:"hotReloadRsyncMemoryThreshold" default:"83"`                                  // 热更新时, 如果内存使用率超过该阈值, 则使用rsync移动索引
	DiskIndexMemoryThreshold      float64                `json:"diskIndexMemoryThreshold"`                                                    // 磁盘索引保留的内存阈值
}

type StartHotUpdateTaskHttpHandler struct {
}

func NewStartHotUpdateTaskHttpHandler() *StartHotUpdateTaskHttpHandler {
	return &StartHotUpdateTaskHttpHandler{}
}

func (h *StartHotUpdateTaskHttpHandler) Path() string {
	return "/api/v1/platform/start-hot-update-task"
}

func (h *StartHotUpdateTaskHttpHandler) Method() string {
	return http.MethodPost
}

func (h *StartHotUpdateTaskHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	var request StartHotUpdateTaskRequest
	//if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
	//	return api.OfParamFailf[string]("invalid request param: %s", err)
	//}
	requestId, r := util.GetRequestId(r)
	err := cfg.UnpackReaderWithJson(r.Body, &request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(request)
	log.Info("[%s]start hot-reload task request: %#v", requestId, string(requestJson))

	// 执行任务pipeline
	return runHotReloadTask(&request, requestId)
}

func runHotReloadTask(trr *StartHotUpdateTaskRequest, requestId string) *api.Response[string] {
	var (
		err       error
		execParam *dto.ExecParam
	)

	// 回调平台获取执行参数
	err = retry.DoRetry(func() error {
		execParam, err = util.GetExecParam(requestId, trr.TaskId)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		err = errors.WithMessage(err, "get serving versions fail")
		return api.ParamFail[string](err)
	}

	// init service versions
	if len(trr.ServingVersions) == 0 {
		// 平台只有双版本才会返回ServingVersions, 因此后续需要再次判断是否为空
		trr.ServingVersions = execParam.ServingVersions
		// 单版本索引的情况, 需要主动重设
		if len(trr.ServingVersions) == 0 {
			trr.ServingVersions = []string{trr.IndexVersion}
		}
	}

	// init loadType
	if trr.LoadType == "" {
		trr.LoadType = execParam.LoadType
	}

	// is rollback
	if trr.IsRollback == nil {
		isRollback := trr.LoadType == "rollback"
		trr.IsRollback = &isRollback
	}

	carrier, err := cfg.NewCommonCfgWithJson(trr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}

	// carry oss
	oss, err := trr.Datasource.GetOss()
	if err != nil {
		err = errors.WithMessage(err, "invalid param: get oss config fail")
		return api.ParamFail[string](err)
	}
	carrier.Put("oss", oss)
	carrier.Put("isReboot", false)

	// 执行任务
	t, err := exec.SubmitTaskStartFrom(requestId, trr.TaskId, trr.TemplateName, carrier, trr.StartStepName)
	if err != nil {
		err = errors.WithMessagef(err, "submit task fail: %s", trr.TaskId)
		return api.SystemFail[string](err)
	}

	// 是否需要同步返回结果
	if *trr.Sync {
		tr := <-t.Result()
		return api.FromResult[string](tr)
	}
	return api.OfSuccess[string]("start hot reload task success")
}
