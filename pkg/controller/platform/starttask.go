package platform

import (
	"context"
	b "dip-agent/pkg/controller/platform/batch"
	"dip-agent/pkg/controller/platform/engine"
	"dip-agent/pkg/controller/platform/model"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
	"net/http"
)

const (
	Build             = TaskType(1) // 构建任务
	HotReload         = TaskType(2) // 热更新
	RebootReload      = TaskType(3) // 重启更新
	BatchRebootReload = TaskType(4) // 批量重启更新
	IndexDelete       = TaskType(5) // 索引删除任务
	ModelRebootReload = TaskType(6) // 模型重启更新
	ModelHotReload    = TaskType(7) // 模型重启更新
	EngineReboot      = TaskType(8) // 引擎重启
	BatchIndexDelete  = TaskType(9) // 批量索引删除
)

func init() {
	web.RegisterHttpHandler[string](NewStartTaskWrapperHttpHandler())
}

type TaskType int

type StartTaskWrapperRequest struct {
	Type     TaskType `json:"type" validate:"required"`     // 必填. 任务类型
	TaskInfo string   `json:"taskInfo" validate:"required"` // 必填. 任务信息
}

type StartTaskWrapperHttpHandler struct {
}

func NewStartTaskWrapperHttpHandler() *StartTaskWrapperHttpHandler {
	return &StartTaskWrapperHttpHandler{}
}

func (h *StartTaskWrapperHttpHandler) Path() string {
	return "/api/v1/platform/start-task"
}

func (h *StartTaskWrapperHttpHandler) Method() string {
	return http.MethodPost
}

func (h *StartTaskWrapperHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	var request StartTaskWrapperRequest
	requestId, r := util.GetRequestId(r)
	err := cfg.UnpackReaderWithJson(r.Body, &request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	log.Info("[%s]start task wrapper request: %#v", requestId, request)

	// 代理给实际的任务接口
	switch request.Type {
	case Build:
		return h.startBuildTask(requestId, request.TaskInfo)
	case HotReload:
		return h.startHotReloadTask(requestId, request.TaskInfo)
	case RebootReload:
		return h.startRebootReloadTask(requestId, request.TaskInfo)
	case BatchRebootReload:
		return h.startBatchRebootReloadTask(requestId, request.TaskInfo)
	case IndexDelete:
		return h.startIndexDeleteTask(requestId, request.TaskInfo)
	case ModelRebootReload:
		return h.startModelRebootReloadTask(requestId, request.TaskInfo)
	case ModelHotReload:
		return h.startModelHotReloadTask(requestId, request.TaskInfo)
	case EngineReboot:
		return h.startEngineRebootReloadTask(requestId, request.TaskInfo)
	case BatchIndexDelete:
		return h.startBatchIndexDeleteTask(requestId, request.TaskInfo)
	}
	return api.SystemFail[string](errors.Errorf("invalid task type: %d", request.Type))
}

func (h *StartTaskWrapperHttpHandler) startBuildTask(requestId string, info string) *api.Response[string] {
	var tr IndexBuildRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid index build request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start index build task with request: %s", requestId, string(requestJson))
	return runBuildIndexTask(requestId, &tr)
}

func (h *StartTaskWrapperHttpHandler) startHotReloadTask(requestId string, info string) *api.Response[string] {
	var tr StartHotUpdateTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid hot reload request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start hot reload task with request: %#v", requestId, string(requestJson))
	return runHotReloadTask(&tr, requestId)
}

func (h *StartTaskWrapperHttpHandler) startRebootReloadTask(requestId string, info string) *api.Response[string] {
	var tr RebootTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid reboot request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start reboot task with request: %#v", requestId, string(requestJson))
	return runRebootTask(&tr, requestId)
}

func (h *StartTaskWrapperHttpHandler) startBatchRebootReloadTask(requestId string, info string) *api.Response[string] {
	var tr b.RebootTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid batch reboot request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start batch reboot task with request: %#v", requestId, string(requestJson))
	return b.RunRebootTask(&tr, requestId)
}

func (h *StartTaskWrapperHttpHandler) startIndexDeleteTask(requestId string, info string) *api.Response[string] {
	var tr StartIndexDeleteTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid index delete request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start index delete task with request: %s", requestId, string(requestJson))
	return runIndexDeleteTask(&tr, requestId)
}

func (h *StartTaskWrapperHttpHandler) startModelRebootReloadTask(requestId string, info string) *api.Response[string] {
	var tr model.RebootTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid model reboot request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start model reboot task with request: %#v", requestId, string(requestJson))
	return model.RunRebootTask(&tr, requestId)
}

func (h *StartTaskWrapperHttpHandler) startModelHotReloadTask(requestId string, info string) *api.Response[string] {
	var tr model.StartHotUpdateTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid model hot reload request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start model hot reload task with request: %#v", requestId, string(requestJson))
	return model.RunHotReloadTask(&tr, requestId)
}

func (h *StartTaskWrapperHttpHandler) startEngineRebootReloadTask(requestId string, info string) *api.Response[string] {
	var tr engine.RebootTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid engine reboot request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start engine reboot task with request: %#v", requestId, string(requestJson))
	return engine.RunRebootTask(&tr, requestId)
}

func (h *StartTaskWrapperHttpHandler) startBatchIndexDeleteTask(id string, info string) *api.Response[string] {
	var tr b.DeleteTaskRequest
	err := cfg.UnpackWithJson([]byte(info), &tr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param: invalid batch index delete request")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(tr)
	log.Info("[%s]start batch index delete task with request: %#v", id, string(requestJson))
	return b.RunDeleteTask(&tr, id)
}
