package index

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/process/download/index"
	"dip-agent/pkg/process/download/model"
	"dip-agent/pkg/process/download/model/config"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/oss"
	"dip-agent/pkg/util/retry"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
)

func init() {
	web.RegisterHttpHandler[*ResultMessage](NewInitIndexDbHttpHandler())
}

type InitIndexDbRequest struct {
	Force                       *bool    `json:"force" default:"true"`                                                    // 是否强制下载覆盖本地索引
	DgraphEngineVersionCmdPath  string   `json:"dgraphEngineVersionCmdPath,omitempty" default:"/app/rel/bin/doe-console"` // 推荐engine version的二进制文件路径
	DgraphEngineVersionCmdArgs  []string `json:"dgraphEngineVersionCmdArgs,omitempty" default:"[\"binary-version\"]"`     // 推荐engine version的命令参数
	DSearchEngineVersionCmdPath string   `json:"dsearchEngineVersionCmdPath,omitempty" default:"/app/rel/bin/doe-server"` // 搜索engine version的二进制文件路径
	DSearchEngineVersionCmdArgs []string `json:"dsearchEngineVersionCmdArgs,omitempty" default:"[\"-v\"]"`                // 搜索engine version的命令参数
	DbPath                      string   `json:"dbPath,omitempty"`
	GzPath                      string   `json:"gzPath,omitempty"`
	CheckServerStatus           *bool    `json:"checkServerStatus" default:"true"`                     // 是否检查引擎服务进程是否在线
	CheckBuildResult            *bool    `json:"checkBuildResult,omitempty" default:"true"`            // 是否进行索引构建结果校验
	MemoryUsageThreshold        *int     `json:"memoryUsageThreshold,omitempty"`                       // 内存使用阈值
	InitModel                   *bool    `json:"initModel,omitempty" default:"true"`                   // 是否初始化模型
	ModelDbPath                 string   `json:"modelDbPath,omitempty" default:"/app/rel/data/model/"` // 模型存放目录
}

type ClusterIndexRequest struct {
	ClusterCode    string         `json:"clusterCode"`
	ClusterGroup   string         `json:"clusterGroup"`
	Version        string         `json:"version"`
	Ip             string         `json:"ip"`
	EngineVersions map[string]int `json:"engineVersions"` // Dgraph的不同索引类型对应的引擎版本
	PartitionNum   *int           `json:"partitionNum"`   // 分片数
	PartitionNo    *int           `json:"partitionNo"`    // 分片号
}

type ClusterIndexDbInfo struct {
	IsDeleteAll           *bool         `json:"isDeleteAll" default:"true"` // 是否删除所有索引
	AgentClusterIndexDtos []*LoadDbInfo `json:"agentClusterIndexDtos"`
	DbPath                string        `json:"dbPath,omitempty" default:"/app/rel/data/cdb"`
	GzPath                string        `json:"gzPath,omitempty" default:"/app/rel/data/gz"`
	MemoryUsageThreshold  *int          `json:"memoryUsageThreshold,omitempty"` // 内存使用阈值
}

type LoadDbInfo struct {
	IndexName       string              `json:"indexName" validate:"required"`
	VersionOssPaths map[string]*dto.Oss `json:"versionOssPaths"`
}

type OssKey struct {
	OssKey    string `json:"ossKey"`
	KeySecret string `json:"keySecret"`
}

type ResultMessage struct {
	InitResult            string         `json:"initResult,omitempty"`       // 初始化结果
	CurrentEngineVersions map[string]int `json:"currentEngineVersions"`      // 当前引擎的版本
	IsDeleteAll           *bool          `json:"isDeleteAll" default:"true"` // 是否删除所有索引
	IndexDbOssPath        []string       `json:"indexDbOssPath"`
	ModelDbOssPath        []string       `json:"modelDbOssPath"`
	ErrMsg                []string       `json:"errMsg,omitempty"` // 错误信息
}

// InitIndexDbHttpHandler 初始化索引db
type InitIndexDbHttpHandler struct {
}

func NewInitIndexDbHttpHandler() *InitIndexDbHttpHandler {
	return &InitIndexDbHttpHandler{}
}

func (h *InitIndexDbHttpHandler) Path() string {
	return "/api/v1/index/init-db"
}

func (h *InitIndexDbHttpHandler) Method() string {
	return http.MethodPost
}

func (h *InitIndexDbHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[*ResultMessage] {
	requestId, r := util.GetRequestId(r)
	request := &InitIndexDbRequest{}
	err := cfg.UnpackReaderWithJson(r.Body, request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[*ResultMessage](err)
	}
	requestJson, _ := json.MarshalIndentDefault(request)
	log.Info("[%s]init db request: %s", requestId, string(requestJson))
	start := time.Now()
	defer func() {
		log.Info("[%s]init db cost: %s", requestId, time.Since(start))
	}()

	rm, err := initDb(requestId, request)
	if err != nil {
		initIndexErrMsg := ""
		if rm != nil {
			if len(rm.ErrMsg) > 0 {
				initIndexErrMsg = strings.Join(rm.ErrMsg, ", \n")
			}
		}

		log.Error("[%s]init db fail: %s. err: %+v", requestId, initIndexErrMsg, err)

		// 发送告警
		engineBinaryVersion := api.EngineBinaryVersion
		var engineVersionInfo string
		if engineBinaryVersion != nil {
			engineVersionContent, _ := json.Marshal(engineBinaryVersion)
			if len(engineBinaryVersion) > 0 {
				engineVersionInfo = string(engineVersionContent)
			}
		}
		errMsg := fmt.Sprintf("agent init db fail: %s. \nengine version: %s. \ndetail: %s", err.Error(), engineVersionInfo, initIndexErrMsg)
		sendErr := sendWarnMsg(requestId, errMsg)
		if sendErr != nil {
			log.Error("[%s]send warn msg fail: %s", requestId, sendErr)
		}

		var message strings.Builder
		message.WriteString(err.Error())
		if initIndexErrMsg != "" {
			message.WriteString(": ")
			message.WriteString(initIndexErrMsg)
		}
		response := api.OfFailWithData[*ResultMessage](api.SystemException, err, message.String(), rm)
		// 对于初始化的场景, 不需要详细的堆栈错误信息
		response.ErrorDetails = &api.ErrorDetails{
			ErrorType: api.SystemException,
		}
		return response
	}

	return api.OfSuccess[*ResultMessage](rm)
}

// TODO 发布初始化完成事件, 以便开始监控引擎进程

// 初始化节点
func initDb(requestId string, r *InitIndexDbRequest) (*ResultMessage, error) {
	var (
		err           error
		versions      map[string]int
		resultMessage = &ResultMessage{
			IndexDbOssPath: make([]string, 0),
			ModelDbOssPath: make([]string, 0),
			ErrMsg:         make([]string, 0),
		}
	)

	// 获取当前引擎的版本
	versions, err = GetEngineBinaryVersions(requestId, r)
	if err != nil {
		return resultMessage, errors.WithMessage(err, "get engine binary versions fail")
	}
	resultMessage.CurrentEngineVersions = versions
	log.Info("[%s]current engine versions: %+v", requestId, versions)

	// 检测当前引擎服务是否在线
	if checkEngineOnline(requestId) {
		return resultMessage, errors.New("engine service is online, cannot init db")
	}

	// 初始化索引
	err = initIndexDb(requestId, r, resultMessage)
	if err != nil {
		return resultMessage, err
	}

	// 初始化模型
	needInitModel := r.InitModel != nil && *r.InitModel
	if needInitModel {
		err = initModel(requestId, r, resultMessage)
		if err != nil {
			return resultMessage, err
		}
	}

	return resultMessage, nil
}

func initIndexDb(requestId string, r *InitIndexDbRequest, resultMessage *ResultMessage) error {
	var (
		err error
	)

	// 从平台获取需要加载的索引信息
	resp, err := getTaskInfo(requestId, resultMessage.CurrentEngineVersions)
	if err != nil {
		return err
	}
	resultMessage.IsDeleteAll = resp.IsDeleteAll
	indexDbInfoContent, _ := json.MarshalIndentDefault(resp)
	log.Info("[%s]get index db info: %+v", requestId, string(indexDbInfoContent))

	// cpu core
	concurrency := runtime.GOMAXPROCS(-1) + 1

	// 内存阈值
	var memoryUsageThreshold int
	// 接口直接指定的优先最高
	if r.MemoryUsageThreshold != nil {
		memoryUsageThreshold = *r.MemoryUsageThreshold
		log.Info("[%s]use http request memoryUsageThreshold: %d", requestId, memoryUsageThreshold)
	} else if resp.MemoryUsageThreshold != nil {
		memoryUsageThreshold = *resp.MemoryUsageThreshold
		log.Info("[%s]use platform memoryUsageThreshold: %d", requestId, memoryUsageThreshold)
	}

	if memoryUsageThreshold <= 0 {
		log.Info("[%s]use default memoryUsageThreshold: %d", requestId, 93)
		memoryUsageThreshold = 93
	}
	log.Info("[%s]memoryUsageThreshold: %d", requestId, memoryUsageThreshold)

	// 默认配置
	isForce := r.Force != nil && *r.Force
	checkBuildResult := r.CheckBuildResult != nil && *r.CheckBuildResult
	dbPath := resp.DbPath
	if r.DbPath != "" {
		dbPath = r.DbPath
	}
	log.Info("[%s]index db path: %s", requestId, dbPath)
	gzPath := resp.GzPath
	if r.GzPath != "" {
		gzPath = r.GzPath
	}
	log.Info("[%s]index gz path: %s", requestId, gzPath)
	// 重建gzPath
	err = os.RemoveAll(gzPath)
	if err != nil {
		log.Warn("[%s]remove gz path fail: %s", requestId, gzPath)
	}
	err = os.MkdirAll(gzPath, 0755)
	if err != nil {
		log.Warn("[%s]create gz path fail: %s", requestId, gzPath)
	}

	isDeleteAll := resp.IsDeleteAll != nil && *resp.IsDeleteAll
	if isDeleteAll {
		// 删除所有索引
		err = util.RemoveAllExcept(dbPath, nil)
		if err != nil {
			return err
		}
		log.Info("[%s]remove all index db success: %s", requestId, dbPath)
	}

	indexInfos := resp.AgentClusterIndexDtos
	if len(indexInfos) == 0 {
		log.Info("[%s]no index db need to init", requestId)
		resultMessage.InitResult = "no index db need to init"
		return nil
	}

	// 是否全部老链路的索引
	if isAllOldIndex(indexInfos) {
		log.Warn("[%s]all old script index version", requestId)
		//return resultMessage, errors.New("all old index, fallback to old script version")
	}

	// 获取需要下载的ossPath数量
	downloadSize := 0
	for _, info := range indexInfos {
		versionOssPaths := info.VersionOssPaths
		downloadSize += len(versionOssPaths)

		for _, ossDef := range versionOssPaths {
			resultMessage.IndexDbOssPath = append(resultMessage.IndexDbOssPath, ossDef.Dir)
		}
	}

	// 需要下载的索引任务
	downloadTasks, err := initDownloadIndexTask(requestId, dbPath, gzPath, isForce, checkBuildResult, memoryUsageThreshold, indexInfos)
	if err != nil {
		return err
	}
	if len(downloadTasks) == 0 {
		log.Info("[%s]no index db need to download", requestId)
		return nil
	}

	// 异步下载
	productor := func(ctx context.Context, dataCh chan<- *index.ProcessRequest) error {
		for _, task := range downloadTasks {
			select {
			case <-ctx.Done():
				return nil
			case dataCh <- task:
			}
		}
		return nil
	}
	consumer := func(item *index.ProcessRequest) error {
		rmi, err := downloadIndex(requestId, item)
		if err != nil {
			return err
		}
		content, _ := json.Marshal(rmi)
		log.Info("[%s]download index result: %s", requestId, string(content))
		return nil
	}
	errs := util.AsyncProcess(requestId, productor, consumer, concurrency, false)
	if len(errs) > 0 {
		for _, err := range errs {
			if err != nil {
				resultMessage.ErrMsg = append(resultMessage.ErrMsg, err.Error())
			}
		}
		return errors.New("download index db fail")
	}

	return err
}

func initDownloadIndexTask(requestId, dbPath, gzPath string, isForce, checkBuildResult bool, memoryUsageThreshold int, indexInfos []*LoadDbInfo) ([]*index.ProcessRequest, error) {
	// 默认配置
	isCompress := true
	isChecksum := false // 为了兼容老版本, 默认不校验
	isReboot := true
	moveWithRsync := false // 默认不使用rsync, mv命令更快

	// 内存使用率阈值
	if !api.CurrentEnv.IsPrd() {
		if memoryUsageThreshold < 150 {
			memoryUsageThreshold = 150
		}
	}

	// 初始化的时候, 并行度使用当前cpu核数, 全力下载
	concurrency := runtime.GOMAXPROCS(-1)

	result := make([]*index.ProcessRequest, 0)
	var err error

	for _, indexLoadInfo := range indexInfos {
		versionOssPaths := indexLoadInfo.VersionOssPaths
		if len(versionOssPaths) == 0 {
			log.Warn("[%s]no index version: %s", requestId, indexLoadInfo.IndexName)
			continue
		}
		indexName := indexLoadInfo.IndexName
		indexDbPath := filepath.Join(dbPath, indexName)

		// 获取本地索引的版本
		localVersions, err := getLocalVersions(indexDbPath)
		if err != nil {
			log.Warn("[%s]get local index versions fail: %s. err: %s", requestId, indexDbPath, err)
			continue
		}
		localVersionLen := len(localVersions)
		// 删除过期版本
		if localVersionLen > 0 {
			for _, localVersion := range localVersions {
				if _, ok := versionOssPaths[localVersion]; !ok {
					// 删除本地索引
					localIndexDbPath := filepath.Join(indexDbPath, localVersion)
					err = os.RemoveAll(localIndexDbPath)
					if err != nil {
						log.Warn("[%s]remove outdated index version fail: %s", requestId, localIndexDbPath)
					} else {
						log.Info("[%s]remove outdated index version success: %s", requestId, localIndexDbPath)
					}
				}
			}
		}

		// 下载最新版本
		for indexVersion, ossDef := range versionOssPaths {
			ossDef.Concurrency = concurrency
			// 如果本地存在对应的版本, 则不重复下载
			if !isForce && localVersionLen > 0 {
				if util.ContainsString(localVersions, indexVersion) {
					// 进一步判断目录内容是否有文件
					localIndexDbPath := filepath.Join(indexDbPath, indexVersion)
					isEmptyDir, err := util.IsEmptyDir(localIndexDbPath)
					if err != nil {
						log.Warn("[%s]check local index version fail: %s", requestId, localIndexDbPath)
					} else if !isEmptyDir {
						log.Info("[%s]local index version exist, no need to download: %s(%s)", requestId, indexName, indexVersion)
						continue
					}
				}
			}

			// 检查对应版本在oss中是否存在
			c, err := oss.NewClient(ossDef.Endpoint, ossDef.Bucket, ossDef.AccessKey, ossDef.SecretKey)
			if err != nil {
				return nil, errors.WithMessage(err, "create oss client fail")
			}
			exist, err := c.IsDirExist(ossDef.Dir)
			if err != nil {
				return nil, errors.WithMessagef(err, "check oss dir exist fail: %s", ossDef.Dir)
			}
			if !exist {
				//log.Warn("[%s]index version not exist in oss: %s", requestId, ossDef.Dir)
				return nil, errors.Errorf("index version not exist in oss: %s", ossDef.Dir)
			}

			log.Info("[%s]start init index db: %s(%s)", requestId, indexName, indexVersion)
			pr := &index.ProcessRequest{
				IndexName:                 indexName,
				IndexVersion:              indexVersion,
				DbPath:                    dbPath,
				GzPath:                    gzPath,
				Compress:                  &isCompress,
				CheckBuildResult:          &checkBuildResult,
				Checksum:                  &isChecksum,
				ChecksumFileName:          "checksum.json",
				Oss:                       ossDef,
				EngineBuildResultFileName: "check.json",
				AgentConfigDirName:        "agent_config",
				IsReboot:                  &isReboot,
				Force:                     &isForce,
				MoveWithRsync:             &moveWithRsync,
				MemoryUsageThreshold:      memoryUsageThreshold,
			}
			err = cfg.SetDefaultAndValidate(pr)
			if err != nil {
				return nil, errors.WithMessage(err, "download index request validate fail")
			}
			result = append(result, pr)
		}
	}
	return result, err
}

func downloadIndex(requestId string, pr *index.ProcessRequest) (*index.ResultMessageInfo, error) {
	content, err := json.Marshal(pr)
	if err != nil {
		log.Warn("[%s]marshal process request fail: %s", requestId, err)
	} else {
		log.Info("[%s]init index db process request: %s", requestId, string(content))
	}
	// 下载索引
	return index.RunTask(requestId, pr)
}

// 从平台获取需要加载的索引信息
func getTaskInfo(requestId string, versions map[string]int) (*ClusterIndexDbInfo, error) {
	url := api.BackendDomain + "/agent/version/cluster/index/oss/path"
	req := &ClusterIndexRequest{
		ClusterCode:    api.ClusterCode,
		ClusterGroup:   api.ClusterGroup,
		Version:        api.VERSION,
		Ip:             api.NodeIp,
		EngineVersions: versions,
		PartitionNum:   getPartitionNum(),
		PartitionNo:    getPartitionNo(),
	}
	hc := util.GetDefaultHttpClient()
	hc.SetClue(requestId)

	var (
		err  error
		resp = &ClusterIndexDbInfo{}
	)

	err = retry.DoRetryWithMaxRetries(func() error {
		return hc.PostWithResponse(url, req, nil, resp)
	}, 3, 5*time.Second, "getIndexDbTaskInfo")

	if err != nil {
		return nil, err
	}

	err = cfg.SetDefaultAndValidate(resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// 查询oss ak&sk
func getOssAkSk() (*OssKey, error) {
	url := api.BackendDomain + "/tool/getOssConfig"
	hc := util.NewHttpClientWithSuccessCode(30*time.Second, []string{"0"})

	var (
		err  error
		resp *OssKey
	)

	err = retry.DoRetry(func() error {
		return hc.GetWithResponse(url, nil, resp)
	})
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// 获取本地索引的版本
func getLocalVersions(indexDbPath string) ([]string, error) {
	exist := util.IsPathExist(indexDbPath)
	if !exist {
		return nil, nil
	}
	return util.GetSubdirectories(indexDbPath)
}

// GetEngineBinaryVersions 获取引擎二进制版本
func GetEngineBinaryVersions(requestId string, r *InitIndexDbRequest) (map[string]int, error) {
	versions, err := engine.GetEngineBinaryVersions(requestId, r.DgraphEngineVersionCmdPath, r.DgraphEngineVersionCmdArgs, r.DSearchEngineVersionCmdPath, r.DSearchEngineVersionCmdArgs)
	if err != nil {
		return versions, err
	}
	api.EngineBinaryVersion = versions
	return versions, err
}

// 是否全部老链路的索引
func isAllOldIndex(indexInfos []*LoadDbInfo) bool {
	for _, indexInfo := range indexInfos {
		if indexInfo.VersionOssPaths == nil {
			continue
		}
		for _, ossDef := range indexInfo.VersionOssPaths {
			ossPath := ossDef.Dir
			if strings.Contains(ossPath, "dip_new") {
				return false
			}
		}
	}
	return true
}

// 发送告警
func sendWarnMsg(requestId string, eventMsg string) error {
	wr := platform.NewWarnRequestWithMsg(requestId, eventMsg)
	_, err := wr.Send()
	if err != nil {
		return errors.WithMessage(err, "send warn msg fail")
	}
	return nil
}

// 从环境变量获取分区数
func getPartitionNum() *int {
	pn := 1
	partitionNum := os.Getenv("TOTAL_PARTITION")
	if partitionNum == "" {
		return &pn
	}
	// 转为int
	pn, err := strconv.Atoi(partitionNum)
	if err != nil {
		log.Warn("parse partition num fail: %s", err)
		return nil
	}
	return &pn
}

// 从环境变量获取分区号
func getPartitionNo() *int {
	pn := 0
	partitionNo := os.Getenv("PARTITION_NUMBER")
	if partitionNo == "" {
		return &pn
	}
	// 转为int
	pn, err := strconv.Atoi(partitionNo)
	if err != nil {
		log.Warn("parse partition no fail: %s", err)
		return nil
	}
	return &pn
}

// init model
func initModel(requestId string, r *InitIndexDbRequest, resultMessage *ResultMessage) error {
	// 先尝试获取模型的接口是否存在
	_, err := platform.NewGetAllModelRequest(requestId).Send()
	if err != nil {
		log.Warn("[%s]get all model fail. model api no exist: %s", requestId, err)
		return nil
	}

	isForce := r.Force != nil && *r.Force
	isDeleteAll := resultMessage.IsDeleteAll != nil && *resultMessage.IsDeleteAll
	if isDeleteAll {
		// 删除所有模型
		err := util.RemoveAllExcept(r.ModelDbPath, nil)
		if err != nil {
			return err
		}
		log.Info("[%s]remove all model db success: %s", requestId, r.ModelDbPath)
	}

	// 创建模型 db 目录
	if !util.IsPathExist(r.ModelDbPath) {
		err := os.MkdirAll(r.ModelDbPath, 0755)
		if err != nil {
			return errors.WithMessagef(err, "create model db dir fail: %s", r.ModelDbPath)
		}
		log.Info("[%s]create model db dir success: %s", requestId, r.ModelDbPath)
	}

	// 调用平台接口获取需要加载的模型信息
	modelInfos, err := getAllModels(requestId)
	if err != nil {
		return err
	}
	if len(modelInfos) == 0 {
		log.Info("[%s]no model need to load", requestId)
		return nil
	}

	// 记录模型oss路径
	for _, modelInfo := range modelInfos {
		versionOssPaths := modelInfo.VersionOssPaths
		for _, ossDef := range versionOssPaths {
			resultMessage.ModelDbOssPath = append(resultMessage.ModelDbOssPath, ossDef.Dir)
		}
	}

	// 需要下载的模型信息
	downloadModelTasks, err := initDownloadModelTask(requestId, r.ModelDbPath, isForce, modelInfos)
	if err != nil {
		return err
	}
	if len(downloadModelTasks) == 0 {
		log.Info("[%s]no model need to download", requestId)
		return nil
	}

	// 下载模型的并发数
	concurrency := runtime.GOMAXPROCS(-1)

	// 并发下载模型
	productor := func(ctx context.Context, dataCh chan<- *modelProcessRequest) error {
		for _, task := range downloadModelTasks {
			select {
			case <-ctx.Done():
				return nil
			case dataCh <- task:
			}
		}
		return nil
	}
	consumer := func(item *modelProcessRequest) error {
		// 下载模型
		err := downloadModel(requestId, item.toModelDownloadProcessRequest())
		if err != nil {
			return err
		}
		// 下载模型配置文件
		err = downloadModelConfig(requestId, item.toModelConfigDownloadProcessRequest())
		if err != nil {
			return err
		}
		return nil
	}
	errs := util.AsyncProcess(requestId, productor, consumer, concurrency, false)
	if len(errs) > 0 {
		for _, err := range errs {
			if err != nil {
				resultMessage.ErrMsg = append(resultMessage.ErrMsg, err.Error())
			}
		}
		return errors.New("download model db fail")
	}

	return nil
}

func getAllModels(requestId string) ([]*dto.ModelInfo, error) {
	var (
		err                error
		modelInfos         []*dto.ModelInfo
		getAllModelRequest = platform.NewGetAllModelRequest(requestId)
	)
	err = retry.DoRetryWithClue(func() error {
		modelInfos, err = getAllModelRequest.Send()
		if err != nil {
			return err
		}
		return nil
	}, requestId)
	if err != nil {
		return nil, err
	}
	return modelInfos, nil
}

func initDownloadModelTask(requestId, modelDbPath string, isForce bool, modelInfos []*dto.ModelInfo) ([]*modelProcessRequest, error) {
	// 默认配置
	isReboot := true
	onlySingleFile := false
	concurrency := runtime.GOMAXPROCS(-1) // 初始化的时候, 并行度使用当前cpu核数, 全力下载

	result := make([]*modelProcessRequest, 0)
	var err error

	for _, modelLoadInfo := range modelInfos {
		versionOssPaths := modelLoadInfo.VersionOssPaths
		if len(versionOssPaths) == 0 {
			log.Warn("[%s]no model version: %s", requestId, modelLoadInfo.ModelName)
			continue
		}
		modelName := modelLoadInfo.ModelName
		modelLocalDbPath := filepath.Join(modelDbPath, modelName)

		// 获取本地模型的版本
		localVersions, err := getLocalVersions(modelLocalDbPath)
		if err != nil {
			log.Warn("[%s]get local model versions fail: %s. err: %s", requestId, modelLocalDbPath, err)
			continue
		}
		localVersionLen := len(localVersions)
		// 删除过期版本
		if localVersionLen > 0 {
			for _, localVersion := range localVersions {
				if _, ok := versionOssPaths[localVersion]; !ok {
					// 删除本地模型
					localModelDbPath := filepath.Join(modelLocalDbPath, localVersion)
					err = os.RemoveAll(localModelDbPath)
					if err != nil {
						log.Warn("[%s]remove outdated model version fail: %s", requestId, localModelDbPath)
					} else {
						log.Info("[%s]remove outdated model version success: %s", requestId, localModelDbPath)
					}
				}
			}
		}

		// 下载最新版本
		for modelVersion, ossDef := range versionOssPaths {
			ossDef.Concurrency = concurrency
			// 如果本地存在对应的版本, 则不重复下载
			if !isForce && localVersionLen > 0 {
				if util.ContainsString(localVersions, modelVersion) {
					// 进一步判断目录内容是否有文件
					localModelDbPath := filepath.Join(modelLocalDbPath, modelVersion)
					isEmptyDir, err := util.IsEmptyDir(localModelDbPath)
					if err != nil {
						log.Warn("[%s]check local model version fail: %s", requestId, localModelDbPath)
					} else if !isEmptyDir {
						log.Info("[%s]local model version exist, no need to download: %s(%s)", requestId, modelName, modelVersion)
						continue
					}
				}
			}

			// 检查对应版本在oss中是否存在
			c, err := oss.NewClient(ossDef.Endpoint, ossDef.Bucket, ossDef.AccessKey, ossDef.SecretKey)
			if err != nil {
				return nil, errors.WithMessage(err, "create oss client fail")
			}
			exist, err := c.IsDirExist(ossDef.Dir)
			if err != nil {
				return nil, errors.WithMessagef(err, "check oss dir exist fail: %s", ossDef.Dir)
			}
			if !exist {
				return nil, errors.Errorf("model version not exist in oss: %s", ossDef.Dir)
			}

			log.Info("[%s]start init model db: %s(%s)", requestId, modelName, modelVersion)
			pr := &modelProcessRequest{
				ModelName:      modelName,
				ModelVersion:   modelVersion,
				ModelDbPath:    modelDbPath,
				Oss:            ossDef,
				OnlySingleFile: &onlySingleFile,
				IsReboot:       &isReboot,
				ModelConfigs:   modelLoadInfo.ModelConfigs,
			}
			err = cfg.SetDefaultAndValidate(pr)
			if err != nil {
				return nil, errors.WithMessage(err, "download model request validate fail")
			}
			result = append(result, pr)
		}
	}
	return result, err
}

// 下载模型
func downloadModel(requestId string, pr *model.ProcessRequest) error {
	rmi, err := model.RunTask(requestId, pr)
	if err != nil {
		return err
	}
	content, _ := json.Marshal(rmi)
	log.Info("[%s]download model result: %s", requestId, string(content))
	return nil
}

// 下载模型配置文件
func downloadModelConfig(requestId string, pr *config.ProcessRequest) error {
	rmi, err := config.RunTask(requestId, pr)
	if err != nil {
		return err
	}
	content, _ := json.Marshal(rmi)
	log.Info("[%s]download model config result: %s", requestId, string(content))
	return nil
}

type modelProcessRequest struct {
	ModelName      string             `json:"modelName,omitempty" validate:"required"`
	ModelVersion   string             `json:"modelVersion,omitempty" validate:"required"`
	ModelDbPath    string             `json:"modelDbPath,omitempty" default:"/app/rel/data/model/" validate:"required"` // 模型存放目录
	Oss            *dto.Oss           `json:"oss,omitempty" validate:"required,dive"`                                   // oss相关配置
	OnlySingleFile *bool              `json:"onlySingleFile,omitempty" default:"false"`                                 // 是否下载的模型只允许单个文件
	IsReboot       *bool              `json:"isReboot,omitempty" default:"false"`                                       // 是否重启更新
	ModelConfigs   []*dto.ModelConfig `json:"modelConfigs,omitempty" validate:"dive"`                                   // 模型配置文件信息
}

func (m *modelProcessRequest) toModelDownloadProcessRequest() *model.ProcessRequest {
	return &model.ProcessRequest{
		ModelName:      m.ModelName,
		ModelVersion:   m.ModelVersion,
		ModelDbPath:    m.ModelDbPath,
		Oss:            m.Oss,
		OnlySingleFile: m.OnlySingleFile,
		IsReboot:       m.IsReboot,
	}
}

func (m *modelProcessRequest) toModelConfigDownloadProcessRequest() *config.ProcessRequest {
	return &config.ProcessRequest{
		ModelName:    m.ModelName,
		ModelVersion: m.ModelVersion,
		Oss:          m.Oss,
		ModelConfigs: m.ModelConfigs,
	}
}

// 检测当前引擎服务是否在线
func checkEngineOnline(requestId string) bool {
	engineServiceStatusResp, err := engine.NewServiceStatusRequestWithTimeout(requestId, 10*time.Second).Send()
	if err != nil {
		return false
	}
	return engineServiceStatusResp != nil &&
		engineServiceStatusResp.Header.IsSuccess() &&
		engineServiceStatusResp.ServiceStatus != nil &&
		engineServiceStatusResp.ServiceStatus.Online != nil &&
		*engineServiceStatusResp.ServiceStatus.Online
}
