package health

import (
	"context"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/sysconfig"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"fmt"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"sync"
	"testing"
	"time"
)

func init() {
	// 0. init log
	log.InitDefaultLogger()
	// 1. init env
	syscfg := sysconfig.Config{}
	engine.InitFormEnv(syscfg.EngineEnvKey)
	// 2. init json
	json.Register("default", &jsoniter.Jsoniter{})
}

// mockEngineServer 模拟引擎服务
type mockEngineServer struct {
	server     *http.Server
	port       string
	handler    http.HandlerFunc
	started    bool
	startError error
}

// 创建新的模拟引擎服务器
func newMockEngineServer(port string, handler http.HandlerFunc) *mockEngineServer {
	mux := http.NewServeMux()
	mux.HandleFunc("/ApiService/ServiceStatus", handler)

	return &mockEngineServer{
		server: &http.Server{
			Addr:    ":" + port,
			Handler: mux,
		},
		port:    port,
		handler: handler,
	}
}

// 启动模拟引擎服务器
func (m *mockEngineServer) start() error {
	// 检查端口是否可用
	if !isPortAvailable(m.port) {
		m.startError = fmt.Errorf("port %s is not available, skipping test", m.port)
		return m.startError
	}

	// 启动服务器
	var startError error
	var wg sync.WaitGroup
	wg.Add(1)

	go func() {
		defer wg.Done()
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			startError = err
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)
	m.started = startError == nil
	m.startError = startError

	return startError
}

// 停止模拟引擎服务器
func (m *mockEngineServer) stop() error {
	if !m.started {
		return nil
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return m.server.Shutdown(ctx)
}

// 检查端口是否可用
func isPortAvailable(port string) bool {
	ln, err := net.Listen("tcp", ":"+port)
	if err != nil {
		return false
	}
	ln.Close()
	return true
}

func TestMain(m *testing.M) {
	// 将健康检查器设置为 nil
	localHealthChecker = nil
	// 运行测试
	code := m.Run()

	os.Exit(code)
}

// 测试正常情况
func TestReadinessHttpHandler_ProcessRequest_Success(t *testing.T) {
	// 创建并启动模拟引擎服务器
	server := newMockEngineServer("9109", func(w http.ResponseWriter, r *http.Request) {
		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		successResponse := `{
			"header": {
				"code": 200,
				"message": "successful"
			},
			"service_status": {
				"online": true,
				"engineBinaryVersion": "1.0.0",
				"engineBuildVersion": "BUILD_TIMESTAMP **********"
			}
		}`
		w.Write([]byte(successResponse))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 准备测试环境
	handler := NewReadinessHttpHandler()
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/readiness", nil)
	req.Header.Set("X-Request-ID", "test-request-id")

	// 执行请求
	receivedResp := handler.ProcessRequest(context.Background(), req)

	// 验证响应内容
	if !strings.Contains(receivedResp.ResponseData, "Service is ready") {
		t.Errorf("期望响应包含 'Service is ready'，但收到了: %s", receivedResp.ResponseData)
	}

}

// 测试引擎异常情况
func TestReadinessHttpHandler_ProcessRequest_EngineError(t *testing.T) {
	// 创建并启动模拟引擎服务器
	server := newMockEngineServer("9109", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusServiceUnavailable)
		errorResponse := `{
			"header": {
				"code": 500,
				"message": "internal server error"
			}
		}`
		w.Write([]byte(errorResponse))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 准备测试环境
	handler := NewReadinessHttpHandler()
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/readiness", nil)
	req.Header.Set("X-Request-ID", "test-request-id")

	// 执行请求
	receivedResp := handler.ProcessRequest(context.Background(), req)

	// 添加：检验HTTP状态码应为503
	if receivedResp.HttpStatusCode != http.StatusServiceUnavailable {
		t.Errorf("期望HTTP状态码503，但收到了%d", receivedResp.HttpStatusCode)
	}

}

// 测试告警功能
func TestReadinessHttpHandler_FailureAlerts(t *testing.T) {
	// 创建处理器并清除原有状态
	handler := NewReadinessHttpHandler()
	handler.failureCountMux.Lock()
	handler.failureCount = 0
	handler.alerted = false
	handler.failureCountMux.Unlock()

	// 创建模拟引擎服务器，始终返回不可用状态
	server := newMockEngineServer("9109", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"header":{"code":200},"service_status":{"online":false}}`))
	})

	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 执行足够次数的请求以触发告警
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/readiness", nil)
	for i := 0; i < maxReadinessFailureCount; i++ {
		req.Header.Set("X-Request-ID", fmt.Sprintf("test-alert-%d", i))
		handler.ProcessRequest(context.Background(), req)
	}

	// 验证告警状态
	handler.failureCountMux.Lock()
	alerted := handler.alerted
	failCount := handler.failureCount
	handler.failureCountMux.Unlock()

	if !alerted {
		t.Error("连续失败后应该发送告警")
	}

	if failCount != maxReadinessFailureCount {
		t.Errorf("连续失败计数应为%d，但为%d", maxReadinessFailureCount, failCount)
	}

	// 测试恢复
	// 改变服务器响应为正常
	server.server.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/ApiService/ServiceStatus" {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"header":{"code":200},"service_status":{"online":true}}`))
		}
	})

	// 执行新请求，应该重置状态
	req.Header.Set("X-Request-ID", "test-recovery")
	handler.ProcessRequest(context.Background(), req)

	// 验证恢复后的状态
	handler.failureCountMux.Lock()
	alertedAfter := handler.alerted
	countAfter := handler.failureCount
	handler.failureCountMux.Unlock()

	if alertedAfter {
		t.Error("恢复后告警状态应重置")
	}

	if countAfter != 0 {
		t.Errorf("恢复后失败计数应重置为0，但为%d", countAfter)
	}
}
