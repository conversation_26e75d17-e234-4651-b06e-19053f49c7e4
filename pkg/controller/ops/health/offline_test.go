package health

import (
	"context"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/sysconfig"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/exec"
	_ "dip-agent/pkg/process/offline"
	_ "dip-agent/pkg/process/online"
	_ "dip-agent/pkg/process/qps"
	"dip-agent/pkg/task"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func init() {
	// 0. init log
	log.InitDefaultLogger()
	// 1. init env
	syscfg := sysconfig.Config{}
	syscfg.Persistence.Properties = map[string]interface{}{
		"filePath": "/tmp/task_status.json",
	}

	cfg.UnpackTypeDefaultsAndValidate(strings.ToLower("file"), "agent.yml", &syscfg)
	var tasks []task.Config
	for _, tc := range syscfg.Tasks {
		if tc.Name == "engine-offline" {
			tasks = append(tasks, tc)
		}
	}
	syscfg.Tasks = tasks

	engine.InitFormEnv(syscfg.EngineEnvKey)
	// 2. init json
	json.Register("default", &jsoniter.Jsoniter{})
	// init exec manage
	exec.Init()
	task.Start(&task.TasksConfig{Tasks: syscfg.Tasks})
}

// 创建新的支持多端点的模拟引擎服务器
func newMockEngineServerWithEndpoints(port string, endpoints map[string]http.HandlerFunc) *mockEngineServer {
	mux := http.NewServeMux()

	// 注册所有端点
	for path, handler := range endpoints {
		mux.HandleFunc(path, handler)
	}

	return &mockEngineServer{
		server: &http.Server{
			Addr:    ":" + port,
			Handler: mux,
		},
		port: port,
	}
}

func TestOfflineHttpHandler_ProcessRequest_Success(t *testing.T) {
	// 创建并启动模拟引擎服务器 - 支持多个端点
	endpoints := map[string]http.HandlerFunc{
		"/ApiService/Offline": func(w http.ResponseWriter, r *http.Request) {
			// 返回成功响应
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			successResponse := `{"header":{"code":200,"message":"ok","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}}}`
			w.Write([]byte(successResponse))
		},
		"/ApiService/Qps": func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			jsonResponse := `{"header":{"code":200,"message":"Get qps succsess","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}},"qps":0}`
			w.Write([]byte(jsonResponse))
		},
	}

	server := newMockEngineServerWithEndpoints("9109", endpoints)

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建处理器
	handler := NewOfflineHttpHandler()

	// 创建模拟请求 (使用本地IP)
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/offline", nil)
	req.Header.Set("X-Forwarded-For", "127.0.0.1")

	// 执行请求
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应
	if resp.IsSuccess() != true {
		t.Errorf("期望成功响应，但收到了失败响应")
	}
	if resp.Code.IsSuccess() != true {
		t.Errorf("期望收到失败 Code 200, 但是收到 Code 500")
	}
}
func TestOfflineHttpHandler_ProcessRequest_NonLocalIP(t *testing.T) {
	// 创建并启动模拟引擎服务器 - 支持多个端点
	endpoints := map[string]http.HandlerFunc{
		"/ApiService/Offline": func(w http.ResponseWriter, r *http.Request) {
			// 返回成功响应
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			successResponse := `{"header":{"code":200,"message":"ok","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}}}`
			w.Write([]byte(successResponse))
		},
		"/ApiService/Qps": func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			jsonResponse := `{"header":{"code":200,"message":"Get qps succsess","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}},"qps":0}`
			w.Write([]byte(jsonResponse))
		},
	}

	server := newMockEngineServerWithEndpoints("9109", endpoints)

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建处理器
	handler := NewOfflineHttpHandler()

	// 创建模拟请求 (使用非本地IP)
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/offline", nil)
	req.Header.Set("X-Forwarded-For", "********") // 使用非本地IP

	// 执行请求
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应
	if resp.IsSuccess() != true {
		t.Errorf("期望成功响应，但收到了失败响应")
	}
	if resp.Code.IsSuccess() != true {
		t.Errorf("期望收到失败 Code 200, 但是收到 Code 500")
	}
}

// C 引擎无法访问的情况（endpoints 无法访问）
func TestOfflineHttpHandler_ProcessRequest_ServerNotStarted(t *testing.T) {
	// 创建处理器
	handler := NewOfflineHttpHandler()

	// 创建模拟请求 (使用本地IP)
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/offline", nil)
	req.Header.Set("X-Forwarded-For", "127.0.0.1")

	// 执行请求 - 注意这里我们没有启动任何服务器
	// 这将导致连接被拒绝或超时
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应 - 应该返回失败
	if resp.IsSuccess() == true {
		t.Errorf("期望失败响应（服务器未启动），但收到了成功响应")
	}
	if resp.Code.IsSuccess() == true {
		t.Errorf("期望收到失败 Code，但是收到了成功 Code")
	}
}

// Qps 接口返回500错误的情况
func TestOfflineHttpHandler_ProcessRequest_QpsErrorResponse(t *testing.T) {
	// 创建并启动模拟引擎服务器 - 支持多个端点
	endpoints := map[string]http.HandlerFunc{
		"/ApiService/Offline": func(w http.ResponseWriter, r *http.Request) {
			// 返回成功响应
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			successResponse := `{"header":{"code":200,"message":"ok","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}}}`
			w.Write([]byte(successResponse))
		},
		"/ApiService/Qps": func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			jsonResponse := `{"header":{"code":500,"message":"Get qps succsess","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}},"qps":0}`
			w.Write([]byte(jsonResponse))
		},
	}

	server := newMockEngineServerWithEndpoints("9109", endpoints)

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建处理器
	handler := NewOfflineHttpHandler()

	// 创建模拟请求 (使用本地IP)
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/offline", nil)
	req.Header.Set("X-Forwarded-For", "127.0.0.1")

	// 执行请求
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应
	if resp.IsSuccess() == true {
		t.Errorf("期望成功响应，但收到了失败响应")
	}
	if resp.Code.IsSuccess() == true {
		t.Errorf("期望收到失败 Code 200, 但是收到 Code 500")
	}
}
