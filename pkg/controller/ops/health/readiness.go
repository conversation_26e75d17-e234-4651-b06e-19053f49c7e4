package health

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/exec"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util"
	"fmt"
	"github.com/pkg/errors"
	"net/http"
	"strings"
	"sync"
	"time"
)

const (
	maxReadinessFailureCount = 3                         // 连续失败告警阈值
	readinessAlertCooldown   = 30 * time.Minute          // 告警冷却时间
	readinessCheckConfigKey  = "readiness.check.enabled" // Key: 用于开启/关闭 readiness 探针检查
)

var localHealthChecker *LocalHealthChecker
var readinessHandler *ReadinessHttpHandler

func init() {
	web.RegisterHttpHandler[string](NewReadinessHttpHandler())
	localHealthChecker = NewLocalHealthChecker() // 初始化本地健康检查器
}

// ReadinessHttpHandler Kubernetes Readiness 探针处理器
type ReadinessHttpHandler struct {
	failureCount     int        // 连续失败次数
	failureCountMux  sync.Mutex // 保护计数器的互斥锁
	alerted          bool       // 当前是否已发送告警
	lastAlertTime    time.Time  // 上次告警时间
	configOnce       sync.Once  // 确保只加载一次集群配置
	readinessEnabled bool       // 存储是否开启探针逻辑
}

// NewReadinessHttpHandler 创建处理器实例
func NewReadinessHttpHandler() *ReadinessHttpHandler {
	if readinessHandler == nil {
		readinessHandler = &ReadinessHttpHandler{}
	}
	return readinessHandler
}

// Path 返回处理的路径
func (h *ReadinessHttpHandler) Path() string {
	return "/api/v1/ops/health/readiness"
}

// Method 返回处理的HTTP方法
func (h *ReadinessHttpHandler) Method() string {
	return http.MethodGet
}

// ProcessRequest 处理Kubernetes Readiness探针请求
func (h *ReadinessHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) (result *api.Response[string]) {
	// 初始化检查时间
	checkedTime := time.Now().Format(time.RFC3339)

	requestId, r := util.GetRequestId(r)

	// 只执行一次
	h.configOnce.Do(func() {
		resp, err := platform.NewGetClusterConfigRequest(requestId).SendWithRetry()
		if err != nil {
			// 拉不到配置，就当“不开启”处理
			log.Warn("Failed to fetch cluster config, skipping readiness checks: %v", err)
			h.readinessEnabled = false
			return
		}
		commonCfg := cfg.CommonCfg(*resp)
		// GetBoolWithDefault(key, def) 在 key 不存在时返回 default
		h.readinessEnabled = commonCfg.GetBoolWithDefault(readinessCheckConfigKey, false)
	})

	// 如果未打开 readiness 检查，则直接返回健康
	log.Debug("readinessEnabled value %t", h.readinessEnabled)
	if !h.readinessEnabled {
		return api.OfSuccess[string]("ok")
	}

	// 跳过重启更新任务
	allTaskMap := exec.GetAllTaskStatus()
	for _, task := range allTaskMap {
		taskName := task.TaskName()
		if strings.Contains(taskName, "reboot") {
			status := task.Status().ToStatusUpdate()
			if !IsFinalStatusString(status.Status) {
				log.Info("skip readiness: taskName=%s, taskId=%s, status=%s", taskName, task.TaskId(), status.Status)
				return api.OfSuccess[string]("ok")
			}
		}
	}

	// 触发本地健康检查（异步），不影响主流程
	if localHealthChecker != nil { // 增加空指针检查
		localHealthChecker.CheckReadiness(requestId)
	}

	engineServiceStatusResp, err := engine.NewServiceStatusRequest(requestId).Send()
	if err != nil {
		errMsg := fmt.Sprintf("query engine service status failed: %v", err)
		h.handleFailure(requestId, errMsg)
		return api.NotReadyError[string](errors.WithMessage(err, "query engine service status failed"))
	}

	// 判断是否 online ready
	isOnline := engineServiceStatusResp != nil &&
		engineServiceStatusResp.Header.IsSuccess() &&
		engineServiceStatusResp.ServiceStatus != nil &&
		engineServiceStatusResp.ServiceStatus.Online != nil &&
		*engineServiceStatusResp.ServiceStatus.Online

	if !isOnline {
		h.handleFailure(requestId, "engine service not ready")
		return api.NotReadyError[string](errors.New("engine service not ready"))
	}

	// 服务可用，重置失败计数
	h.resetFailureCount(requestId)

	// 组装成功响应信息
	responseStr := fmt.Sprintf("Service is ready. Checked at: %s", checkedTime)
	return api.OfSuccess[string](responseStr)
}

func IsFinalStatusString(status string) bool {
	switch status {
	case "SUCCESS", "FAIL", "STOP":
		return true
	default:
		return false
	}
}

// handleFailure 处理检查失败情况
func (h *ReadinessHttpHandler) handleFailure(requestId string, reason string) {
	h.failureCountMux.Lock()
	defer h.failureCountMux.Unlock()

	h.failureCount++
	log.Warn("Readiness check failed (%d/%d): %s, requestId: %s",
		h.failureCount, maxReadinessFailureCount, reason, requestId)

	// 检查是否需要发送告警
	shouldAlert := h.failureCount >= maxReadinessFailureCount &&
		(!h.alerted || time.Since(h.lastAlertTime) > readinessAlertCooldown)

	if shouldAlert {
		h.sendAlert(requestId, reason)
		h.alerted = true
		h.lastAlertTime = time.Now()
	}
}

// resetFailureCount 重置失败计数器
func (h *ReadinessHttpHandler) resetFailureCount(requestId string) {
	h.failureCountMux.Lock()
	defer h.failureCountMux.Unlock()

	if h.failureCount > 0 {
		log.Info("Readiness check recovered after %d failures, requestId: %s", h.failureCount, requestId)

		// 如果之前处于告警状态，记录恢复日志
		if h.alerted {
			log.Info("Alert status reset, readiness service returned to normal, requestId: %s", requestId)
			h.alerted = false
		}
	}

	h.failureCount = 0
}

// sendAlert 发送告警
func (h *ReadinessHttpHandler) sendAlert(requestId string, reason string) {
	log.Error("Alert: Readiness check failed %d consecutive times. Latest failure reason: %s, requestId: %s",
		maxReadinessFailureCount, reason, requestId)

	// 记录告警冷却信息
	log.Info("Readiness service is abnormal, next alert will be sent after %v, requestId: %s",
		readinessAlertCooldown, requestId)

	// 组装错误信息
	errMsg := fmt.Sprintf(
		"【重要】引擎服务 Not Ready 告警\n"+
			" 端点: %s\n"+
			" 状态: 连续探测失败 %d 次检查 (阈值: %d)\n"+
			" 原因: %s\n"+
			" 时间: %s\n"+
			" 静默: 接下来的 %d 分钟内该POD，将不再发送此告警\n"+
			"这可能表明该服务无法正常响应，请立即调查处理。",
		h.Path(),
		maxReadinessFailureCount,
		maxReadinessFailureCount,
		reason,
		time.Now().Format(time.RFC3339),
		readinessAlertCooldown/time.Minute,
	)
	// 发送报警
	wr := platform.NewWarnRequestWithMsg(requestId, errMsg)
	_, err := wr.Send()
	if err != nil {
		log.Error("send warn failed: %s", err)
	}
}
