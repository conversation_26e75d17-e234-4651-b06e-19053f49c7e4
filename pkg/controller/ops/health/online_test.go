package health

import (
	"context"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/sysconfig"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"net/http"
	"net/http/httptest"
	"testing"
)

func init() {
	// 0. init log
	log.InitDefaultLogger()
	// 1. init env
	syscfg := sysconfig.Config{}
	engine.InitFormEnv(syscfg.EngineEnvKey)
	// 2. init json
	json.Register("default", &jsoniter.Jsoniter{})

}

// 正常情况
func TestOnlineHttpHandler_ProcessRequest_Success(t *testing.T) {
	// 创建并启动模拟引擎服务器 - 支持多个端点
	endpoints := map[string]http.HandlerFunc{
		"/ApiService/Online": func(w http.ResponseWriter, r *http.Request) {
			// 返回成功响应
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			successResponse := `{"header":{"code":200,"message":"ok","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}}}`
			w.Write([]byte(successResponse))
		},
		"/ApiService/ServiceStatus": func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			jsonResponse := `{"header":{"code":200,"message":"successful"},"service_status":{"online":true,"engineBinaryVersion":"1.0.0","engineBuildVersion":"BUILD_TIMESTAMP **********"}}`
			w.Write([]byte(jsonResponse))
		},
	}

	server := newMockEngineServerWithEndpoints("9109", endpoints)

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建处理器
	handler := NewOnlineHttpHandler()

	// 创建模拟请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/online", nil)
	req.Header.Set("X-Request-ID", "test-online-success")
	req.Header.Set("X-Forwarded-For", "127.0.0.1")
	// 执行请求
	resp := handler.ProcessRequest(context.Background(), req)

	if resp.ResponseData != "engine online success" {
		t.Errorf("期望响应数据为 'engine online success'，但收到了 '%s'", resp.ResponseData)
	}
}

// 异常情况
func TestOnlineHttpHandler_ProcessRequest_ServerNotStarted(t *testing.T) {
	// 创建处理器
	handler := NewOnlineHttpHandler()

	// 创建模拟请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/online", nil)
	req.Header.Set("X-Request-ID", "test-online-server-not-started")
	req.Header.Set("X-Forwarded-For", "127.0.0.1")
	// 执行请求 - 注意这里我们没有启动任何服务器
	// 这将导致连接被拒绝或超时
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应 - 应该返回失败
	if resp.IsSuccess() {
		t.Errorf("期望失败响应（服务器未启动），但收到了成功响应")
	}
	if resp.Code.IsSuccess() {
		t.Errorf("期望收到失败 Code，但是收到了成功 Code")
	}
}

func TestOnlineHttpHandler_ProcessRequest_StatusOffline(t *testing.T) {
	// 创建并启动模拟引擎服务器 - 上线成功但状态为离线
	endpoints := map[string]http.HandlerFunc{
		"/ApiService/Online": func(w http.ResponseWriter, r *http.Request) {
			// 返回成功响应
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			successResponse := `{"header":{"code":200,"message":"ok","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}}}`
			w.Write([]byte(successResponse))
		},
		"/ApiService/ServiceStatus": func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			// 返回状态为离线
			jsonResponse := `{"header":{"code":200,"message":"successful"},"service_status":{"online":false,"engineBinaryVersion":"1.0.0","engineBuildVersion":"BUILD_TIMESTAMP **********"}}`
			w.Write([]byte(jsonResponse))
		},
	}

	server := newMockEngineServerWithEndpoints("9109", endpoints)

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建处理器
	handler := NewOnlineHttpHandler()

	// 创建模拟请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/online", nil)
	req.Header.Set("X-Request-ID", "test-online-status-offline")
	req.Header.Set("X-Forwarded-For", "127.0.0.1")
	// 执行请求
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应 - 应该返回失败
	if resp.IsSuccess() {
		t.Errorf("期望失败响应（服务状态为离线），但收到了成功响应")
	}
	if resp.Code.IsSuccess() {
		t.Errorf("期望收到失败 Code，但是收到了成功 Code")
	}

}

func TestOnlineHttpHandler_ProcessRequest_ServiceStatusError(t *testing.T) {
	// 创建并启动模拟引擎服务器 - 上线成功但状态检查失败
	endpoints := map[string]http.HandlerFunc{
		"/ApiService/Online": func(w http.ResponseWriter, r *http.Request) {
			// 返回成功响应
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			successResponse := `{"header":{"code":200,"message":"ok","error_details":{"error_type":"UNKNOWN_EXCEPTION","detailed_message":"","trace":""}}}`
			w.Write([]byte(successResponse))
		},
		"/ApiService/ServiceStatus": func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			// 返回服务器错误
			jsonResponse := `{"header":{"code":500,"message":"internal server error"}}`
			w.Write([]byte(jsonResponse))
		},
	}

	server := newMockEngineServerWithEndpoints("9109", endpoints)

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建处理器
	handler := NewOnlineHttpHandler()

	// 创建模拟请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/online", nil)
	req.Header.Set("X-Request-ID", "test-online-status-error")
	req.Header.Set("X-Forwarded-For", "127.0.0.1")
	// 执行请求
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应 - 应该返回失败
	if resp.IsSuccess() {
		t.Errorf("期望失败响应（服务状态检查失败），但收到了成功响应")
	}
	if resp.Code.IsSuccess() {
		t.Errorf("期望收到失败 Code，但是收到了成功 Code")
	}
}

func TestOnlineHttpHandler_ProcessRequest_OnlineError(t *testing.T) {
	// 创建并启动模拟引擎服务器 - 上线失败
	endpoints := map[string]http.HandlerFunc{
		"/ApiService/Online": func(w http.ResponseWriter, r *http.Request) {
			// 返回错误响应
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			errorResponse := `{"header":{"code":500,"message":"internal server error","error_details":{"error_type":"INTERNAL_SERVER_ERROR","detailed_message":"failed to online","trace":""}}}`
			w.Write([]byte(errorResponse))
		},
	}

	server := newMockEngineServerWithEndpoints("9109", endpoints)

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建处理器
	handler := NewOnlineHttpHandler()

	// 创建模拟请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/ops/health/online", nil)
	req.Header.Set("X-Request-ID", "test-online-error")
	req.Header.Set("X-Forwarded-For", "127.0.0.1")
	// 执行请求
	resp := handler.ProcessRequest(context.Background(), req)

	// 验证响应 - 应该返回失败
	if resp.IsSuccess() {
		t.Errorf("期望失败响应（上线请求失败），但收到了成功响应")
	}
	if resp.Code.IsSuccess() {
		t.Errorf("期望收到失败 Code，但是收到了成功 Code")
	}
}
