package health

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/exec"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util"
	"fmt"
	"github.com/pkg/errors"
	"net/http"
	"time"
)

func init() {
	web.RegisterHttpHandler[string](NewOfflineHttpHandler())
}

// OfflineHttpHandler 服务下线处理器
type OfflineHttpHandler struct {
}

// NewOfflineHttpHandler 创建处理器实例
func NewOfflineHttpHandler() *OfflineHttpHandler {
	return &OfflineHttpHandler{}
}

// Path 返回处理的路径
func (h *OfflineHttpHandler) Path() string {
	return "/api/v1/ops/health/offline"
}

// Method 返回处理的HTTP方法
func (h *OfflineHttpHandler) Method() string {
	return http.MethodGet
}

// ProcessRequest 处理服务下线请求
func (h *OfflineHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	requestId, _ := util.GetRequestId(r)
	log.Info("[%s]Received engine offline request", requestId)

	// 调用 引擎摘流 pipeline
	return h.runHotEngineOfflineTask(requestId)

}

func (h *OfflineHttpHandler) runHotEngineOfflineTask(requestId string) *api.Response[string] {
	taskId := util.GenerateUUID()
	carrier := make(map[string]interface{})
	taskName := "engine-offline"

	// 执行任务
	t, err := exec.SubmitTaskWith(requestId, taskId, taskName, carrier)
	if err != nil {
		err = errors.WithMessagef(err, "submit task fail: %s", taskId)
		return api.SystemFail[string](err)
	}
	tr := <-t.Result()

	if tr.Status().IsSuccess() == false {
		err = errors.New(fmt.Sprintf("engine offline fail: %s", tr.Message()))
		// send alert msg
		h.sendAlert(requestId, tr.Message())
		return api.SystemFail[string](err)
	}
	return api.OfSuccess("engine offline success")
}
func (h *OfflineHttpHandler) sendAlert(requestId string, reason string) {
	// 组装错误信息
	errMsg := fmt.Sprintf(
		"【重要】引擎服务 摘流 异常告警\n"+
			"  原因:  %s\n"+
			"  requestId:  %s\n"+
			"  时间:  %s\n"+
			"这可能表明该服务无法正常响应，请立即调查处理。",
		reason,
		requestId,
		time.Now().Format(time.RFC3339),
	)
	// 发送报警
	wr := platform.NewWarnRequestWithMsg(requestId, errMsg)
	_, err := wr.Send()
	if err != nil {
		log.Error("send warn failed: %s", err)
	}
}
