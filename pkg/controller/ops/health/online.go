package health

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/retry"
	"fmt"
	"github.com/pkg/errors"
	"net/http"
	"time"
)

func init() {
	web.RegisterHttpHandler[string](NewOnlineHttpHandler())
}

// OnlineHttpHandler 服务下线处理器
type OnlineHttpHandler struct {
}

// NewOnlineHttpHandler 创建处理器实例
func NewOnlineHttpHandler() *OnlineHttpHandler {
	return &OnlineHttpHandler{}
}

// Path 返回处理的路径
func (h *OnlineHttpHandler) Path() string {
	return "/api/v1/ops/health/online"
}

// Method 返回处理的HTTP方法
func (h *OnlineHttpHandler) Method() string {
	return http.MethodGet
}

func (h *OnlineHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	requestId, r := util.GetRequestId(r)

	// 调用online接口
	err := retry.DoRetry(func() error {
		_, err := engine.NewOnlineRequest(requestId).Send()
		return err
	})
	if err != nil {
		h.sendAlert(requestId, err.Error())
		return api.UpdateFail[string](err)
	}

	// 调用sever status接口检查是否上线成功
	statusResponse, err := engine.NewServiceStatusRequest(requestId).Send()
	if err != nil {
		return api.UpdateFail[string](err)
	}
	if statusResponse.ServiceStatus == nil || statusResponse.ServiceStatus.Online == nil || !*statusResponse.ServiceStatus.Online {
		err = errors.New("engine online fail: service status is offline")
		h.sendAlert(requestId, err.Error())
		return api.UpdateFail[string](err)
	}

	return api.OfSuccess("engine online success")
}

// sendAlert 发送告警
func (h *OnlineHttpHandler) sendAlert(requestId string, reason string) {
	log.Error("Alert: Engine online failed. requestId: %s reason: %s", requestId, reason)
	// 组装错误信息
	errMsg := fmt.Sprintf(
		"【重要】引擎服务 online 上线失败告警\n"+
			" 原因: %s\n"+
			" 时间: %s\n"+
			"这可能表明该服务无法正常响应，请立即调查处理。",
		reason,
		time.Now().Format(time.RFC3339),
	)
	// 发送报警
	wr := platform.NewWarnRequestWithMsg(requestId, errMsg)
	_, err := wr.Send()
	if err != nil {
		log.Error("send warn failed: %s", err)
	}
}
