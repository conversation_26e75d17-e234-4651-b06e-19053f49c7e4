package health

import (
	"context"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/platform"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

const (
	localReadinessURL = "http://localhost:9981/readness"
	maxFailureCount   = 3                // 连续失败告警阈值
	checkTimeout      = 1 * time.Second  // 检查超时时间
	alertCooldown     = 30 * time.Minute // 告警冷却时间
)

// LocalHealthCheckResponse 9981端口的健康检查响应结构
type LocalHealthCheckResponse struct {
	Status string `json:"status"` // 状态信息
}

// LocalHealthChecker 本地健康检查器
type LocalHealthChecker struct {
	failureCount    int        // 连续失败次数
	failureCountMux sync.Mutex // 保护计数器的互斥锁
	alerted         bool       // 当前是否已发送告警
	lastAlertTime   time.Time  // 上次告警时间
	isChecking      bool       // 是否有检查正在进行
	checkMux        sync.Mutex // 保护isChecking的互斥锁
}

// NewLocalHealthChecker 创建本地健康检查器实例
func NewLocalHealthChecker() *LocalHealthChecker {
	return &LocalHealthChecker{}
}

// CheckReadiness 检查本地9981端口的readness
// 如果已有检查在进行，则返回false
func (c *LocalHealthChecker) CheckReadiness(requestId string) bool {
	// 检查是否已有检查在进行
	c.checkMux.Lock()
	if c.isChecking {
		c.checkMux.Unlock()
		log.Debug("Local health check already in progress, skipping this check, requestId: %s", requestId)
		return false
	}

	// 标记检查开始
	c.isChecking = true
	c.checkMux.Unlock()

	// 在协程中执行检查
	go func() {
		defer func() {
			// 标记检查结束
			c.checkMux.Lock()
			c.isChecking = false
			c.checkMux.Unlock()

			// 捕获可能的panic
			if r := recover(); r != nil {
				log.Error("Panic in local health check goroutine: %v, requestId: %s", r, requestId)
			}
		}()

		c.performCheck(requestId)
	}()

	return true
}

// GetStatus 获取当前健康状态
func (c *LocalHealthChecker) GetStatus() (bool, int) {
	c.failureCountMux.Lock()
	defer c.failureCountMux.Unlock()

	// 如果失败次数小于阈值，表示健康
	return c.failureCount < maxFailureCount, c.failureCount
}

// performCheck 执行实际的健康检查
func (c *LocalHealthChecker) performCheck(requestId string) {
	ctx, cancel := context.WithTimeout(context.Background(), checkTimeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, localReadinessURL, nil)
	if err != nil {
		log.Warn("创建本地readiness检查请求失败: %v, requestId: %s", err, requestId)
		c.handleFailure(requestId, fmt.Sprintf("创建请求失败: %v", err))
		return
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Warn("Failed to create local readiness check request: %v, requestId: %s", err, requestId)
		c.handleFailure(requestId, fmt.Sprintf("Failed to create request: %v", err))
		return
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warn("Failed to read local readiness endpoint response: %v, requestId: %s", err, requestId)
		c.handleFailure(requestId, fmt.Sprintf("Failed to read response: %v", err))
		return
	}

	// 解析JSON响应
	var healthResp LocalHealthCheckResponse
	if err := json.Unmarshal(body, &healthResp); err != nil {
		log.Warn("Failed to parse local readiness endpoint response: %v, requestId: %s", err, requestId)
		c.handleFailure(requestId, fmt.Sprintf("Failed to parse response: %v", err))
		return
	}

	// 检查状态是否为"Ready"
	if healthResp.Status != "Ready" {
		log.Warn("Local readiness endpoint reported non-ready status: %s, requestId: %s", healthResp.Status, requestId)
		c.handleFailure(requestId, fmt.Sprintf("Non-ready status: %s", healthResp.Status))
		return
	}

	// 如果一切正常，重置失败计数器
	c.resetFailureCount(requestId)
}

// handleFailure 处理检查失败情况
func (c *LocalHealthChecker) handleFailure(requestId string, reason string) {
	c.failureCountMux.Lock()
	defer c.failureCountMux.Unlock()

	c.failureCount++
	log.Warn("Local readiness check failed (%d/%d): %s, requestId: %s",
		c.failureCount, maxFailureCount, reason, requestId)

	// 检查是否需要发送告警
	shouldAlert := c.failureCount >= maxFailureCount &&
		(!c.alerted || time.Since(c.lastAlertTime) > alertCooldown)

	if shouldAlert {
		c.sendAlert(requestId, reason)
		c.alerted = true
		c.lastAlertTime = time.Now()
	}
}

// resetFailureCount 重置失败计数器
func (c *LocalHealthChecker) resetFailureCount(requestId string) {
	c.failureCountMux.Lock()
	defer c.failureCountMux.Unlock()

	if c.failureCount > 0 {
		log.Info("Local readiness check recovered after %d failures, requestId: %s", c.failureCount, requestId)

		// 如果之前处于告警状态，记录恢复日志
		if c.alerted {
			log.Info("Alert status reset, local readiness service returned to normal, requestId: %s", requestId)
			c.alerted = false
		}
	}

	c.failureCount = 0
}

// sendAlert 发送告警
func (c *LocalHealthChecker) sendAlert(requestId string, reason string) {
	log.Error("Alert: Local readiness check failed %d consecutive times. Latest failure reason: %s, requestId: %s",
		maxFailureCount, reason, requestId)

	// 记录告警冷却信息 - 无论是首次告警还是后续告警
	log.Info("Local readiness service is abnormal, next alert will be sent after %v, requestId: %s",
		alertCooldown, requestId)

	// 组装错误信息
	errMsg := fmt.Sprintf(
		"【重要】POD 内 csearch-control 服务异常\n"+
			"  服务:  csearch-control\n"+
			"  端点:  %s\n"+
			"  状态:  连续探测失败 %d 次检查 (阈值: %d)\n"+
			"  原因:  %s\n"+
			"  时间:  %s\n"+
			"  静默:  接下来的 %d 分钟内该POD，将不再发送此告警\n"+
			"这可能表明该服务无法正常响应，请立即调查处理。",
		localReadinessURL,
		maxFailureCount,
		maxFailureCount,
		reason,
		time.Now().Format(time.RFC3339),
		alertCooldown/time.Minute, // 将时间转换为分钟
	)
	// 发送报警
	wr := platform.NewWarnRequestWithMsg(requestId, errMsg)
	_, err := wr.Send()
	if err != nil {
		log.Error("send warn failed: %s", err)
	}
}
