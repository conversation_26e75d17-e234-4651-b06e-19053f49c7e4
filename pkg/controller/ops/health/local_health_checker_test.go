package health

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/sysconfig"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"
)

func init() {
	// 0. init log
	log.InitDefaultLogger()
	// 1. init env
	syscfg := sysconfig.Config{}
	engine.InitFormEnv(syscfg.EngineEnvKey)
	// 2. init json
	json.Register("default", &jsoniter.Jsoniter{})
	// 3. 配置坐标信息
	api.BackendDomain = "http://pre-dip-backend.shizhuang-inc.com"
	api.ClusterCode = "dpp-rec-deal"
	api.ClusterGroup = "group3"
	api.NodeIp = "************"
}

// mockLocalServer 模拟本地健康检查服务器
type mockLocalServer struct {
	server     *http.Server
	port       string
	handler    http.HandlerFunc
	started    bool
	startError error
}

// 创建新的模拟本地服务器
func newMockLocalServer(port string, handler http.HandlerFunc) *mockLocalServer {
	mux := http.NewServeMux()
	mux.HandleFunc("/readness", handler)

	return &mockLocalServer{
		server: &http.Server{
			Addr:    ":" + port,
			Handler: mux,
		},
		port:    port,
		handler: handler,
	}
}

// 启动模拟本地服务器
func (m *mockLocalServer) start() error {
	// 检查端口是否可用
	if !isPortAvailable(m.port) {
		m.startError = fmt.Errorf("port %s is not available, skipping test", m.port)
		return m.startError
	}

	// 启动服务器
	var startError error
	var wg sync.WaitGroup
	wg.Add(1)

	go func() {
		defer wg.Done()
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			startError = err
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)
	m.started = startError == nil
	m.startError = startError

	return startError
}

// 停止模拟本地服务器
func (m *mockLocalServer) stop() error {
	if !m.started {
		return nil
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return m.server.Shutdown(ctx)
}

// 测试健康检查成功流程
func TestLocalHealthChecker_AsyncHealthCheck_Success(t *testing.T) {
	// 创建并启动模拟本地服务器
	server := newMockLocalServer("9981", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"Ready"}`))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建健康检查器
	checker := NewLocalHealthChecker()

	// 确认初始状态
	initialHealthy, initialCount := checker.GetStatus()
	if !initialHealthy {
		t.Fatal("初始状态应该为健康")
	}
	if initialCount != 0 {
		t.Fatalf("初始失败计数应该为0，但获得了%d", initialCount)
	}

	// 执行检查 - 这会启动异步检查流程
	started := checker.CheckReadiness("test-request-id")

	// 验证检查是否成功启动
	if !started {
		t.Fatal("健康检查应该已成功启动")
	}

	// 验证isChecking状态已设置
	checker.checkMux.Lock()
	isCheckingNow := checker.isChecking
	checker.checkMux.Unlock()
	if !isCheckingNow {
		t.Error("启动检查后isChecking应该为true")
	}

	// 等待异步检查流程完成
	// 注意：在实际场景中，可能需要调整等待时间或使用更可靠的同步机制
	time.Sleep(200 * time.Millisecond)

	// 验证检查后的状态
	finalHealthy, finalCount := checker.GetStatus()
	if !finalHealthy {
		t.Errorf("成功检查后状态应该为健康，但获得了不健康")
	}
	if finalCount != 0 {
		t.Errorf("成功检查后失败计数应该为0，但获得了%d", finalCount)
	}

	// 验证isChecking状态已重置
	checker.checkMux.Lock()
	isCheckingAfter := checker.isChecking
	checker.checkMux.Unlock()
	if isCheckingAfter {
		t.Error("检查完成后isChecking应该为false")
	}
}

// 测试健康检查失败流程
func TestLocalHealthChecker_AsyncHealthCheck_Failure(t *testing.T) {
	// 创建并启动模拟本地服务器，返回非就绪状态
	server := newMockLocalServer("9981", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"Not Ready"}`))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建健康检查器
	checker := NewLocalHealthChecker()

	// 确认初始状态
	initialHealthy, initialCount := checker.GetStatus()
	if !initialHealthy || initialCount != 0 {
		t.Fatalf("初始状态应该为健康且失败计数为0，但获得了healthy=%v, count=%d",
			initialHealthy, initialCount)
	}

	// 执行检查 - 这会启动异步检查流程
	started := checker.CheckReadiness("test-failure-id")
	if !started {
		t.Fatal("健康检查应该已成功启动")
	}

	// 等待异步检查流程完成
	time.Sleep(200 * time.Millisecond)

	// 验证检查后的状态
	_, finalCount := checker.GetStatus()
	if finalCount != 1 {
		t.Errorf("失败检查后计数应该为1，但获得了%d", finalCount)
	}
}

// 测试连续失败达到告警阈值
func TestLocalHealthChecker_CheckReadiness_AlertThreshold(t *testing.T) {
	// 创建并启动模拟本地服务器
	server := newMockLocalServer("9981", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte(`{"status":"Not Ready"}`))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建健康检查器
	checker := NewLocalHealthChecker()

	// 确认初始状态
	initialHealthy, initialCount := checker.GetStatus()
	if !initialHealthy || initialCount != 0 {
		t.Fatalf("初始状态应该为健康且失败计数为0，但获得了healthy=%v, count=%d",
			initialHealthy, initialCount)
	}

	// 执行多次检查以达到告警阈值
	for i := 0; i < maxFailureCount; i++ {
		started := checker.CheckReadiness(fmt.Sprintf("test-alert-%d", i))
		if !started {
			t.Fatalf("第%d次检查应该成功启动", i+1)
		}
		// 等待异步检查完成再启动下一次
		time.Sleep(100 * time.Millisecond)
	}

	// 等待最后一次检查完成（可能包含告警操作）
	time.Sleep(200 * time.Millisecond)

	// 验证最终状态
	healthy, count := checker.GetStatus()
	if healthy {
		t.Errorf("连续失败后状态应该为不健康，但获得了健康")
	}
	if count < maxFailureCount {
		t.Errorf("期望失败计数至少为%d，但获得了%d", maxFailureCount, count)
	}

	// 验证告警状态
	checker.failureCountMux.Lock()
	alerted := checker.alerted
	checker.failureCountMux.Unlock()
	if !alerted {
		t.Error("期望发送了告警，但未发送")
	}
}

// 测试从失败恢复
func TestLocalHealthChecker_Recovery(t *testing.T) {
	// 创建并启动模拟服务器，初始设置为失败响应
	server := newMockLocalServer("9981", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte(`{"status":"Not Ready"}`))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建健康检查器
	checker := NewLocalHealthChecker()

	// 手动设置失败计数和告警状态
	checker.failureCountMux.Lock()
	checker.failureCount = maxFailureCount
	checker.alerted = true
	checker.lastAlertTime = time.Now().Add(-time.Hour) // 设置为过去时间，以确保冷却期已过
	checker.failureCountMux.Unlock()

	// 确认初始状态
	initialHealthy, initialCount := checker.GetStatus()
	if initialHealthy {
		t.Fatalf("手动设置后状态应该为不健康，但获得了健康状态")
	}
	if initialCount != maxFailureCount {
		t.Fatalf("手动设置后失败计数应该为%d，但获得了%d", maxFailureCount, initialCount)
	}

	// 更改服务器响应为成功
	server.server.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/readness" {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"status":"Ready"}`))
		}
	})

	// 执行检查
	started := checker.CheckReadiness("recovery-test")
	if !started {
		t.Fatal("恢复检查应该成功启动")
	}

	// 等待异步检查完成
	time.Sleep(200 * time.Millisecond)

	// 验证状态已恢复
	healthy, count := checker.GetStatus()
	if !healthy {
		t.Errorf("恢复后状态应该为健康，但获得了不健康")
	}
	if count != 0 {
		t.Errorf("恢复后失败计数应该为0，但为%d", count)
	}

	// 验证告警状态已重置
	checker.failureCountMux.Lock()
	alerted := checker.alerted
	checker.failureCountMux.Unlock()
	if alerted {
		t.Error("恢复后告警状态应重置，但未重置")
	}
}

// 测试并发检查
func TestLocalHealthChecker_ConcurrentChecks(t *testing.T) {
	// 设置模拟服务器，添加延迟以测试并发行为
	server := newMockLocalServer("9981", func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(300 * time.Millisecond) // 添加延迟
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"Ready"}`))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建健康检查器
	checker := NewLocalHealthChecker()

	// 并发执行多次检查
	var wg sync.WaitGroup
	checkResults := make([]bool, 5) // 存储每个并发检查的结果

	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			// 尝试启动检查并保存结果
			checkResults[index] = checker.CheckReadiness(fmt.Sprintf("concurrent-test-%d", index))
		}(i)
		// 添加小延迟以确保goroutine能启动
		time.Sleep(10 * time.Millisecond)
	}
	wg.Wait() // 等待所有goroutine完成

	// 验证只有一个检查返回true
	trueCount := 0
	for i, result := range checkResults {
		if result {
			trueCount++
			t.Logf("检查 %d 成功启动", i)
		} else {
			t.Logf("检查 %d 被拒绝启动", i)
		}
	}

	if trueCount != 1 {
		t.Errorf("应该只有一个检查返回true（成功启动），但有%d个", trueCount)
	}

	// 验证检查进行中状态
	checker.checkMux.Lock()
	checking := checker.isChecking
	checker.checkMux.Unlock()
	if !checking {
		t.Error("此时应有检查正在进行中")
	}

	// 等待检查完成
	time.Sleep(500 * time.Millisecond)

	// 验证检查完成后状态
	checker.checkMux.Lock()
	checking = checker.isChecking
	checker.checkMux.Unlock()
	if checking {
		t.Error("检查应该已完成")
	}

	// 验证健康状态
	healthy, count := checker.GetStatus()
	if !healthy {
		t.Error("检查成功后状态应为健康")
	}
	if count != 0 {
		t.Errorf("检查成功后失败计数应为0，但为%d", count)
	}
}

// 测试各种错误情况
func TestLocalHealthChecker_ErrorCases(t *testing.T) {
	testCases := []struct {
		name       string
		handler    http.HandlerFunc
		expectFail bool
	}{
		{
			name: "无效的JSON响应",
			handler: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{invalid json`))
			},
			expectFail: true,
		},
		{
			name: "非就绪状态",
			handler: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"status":"NotReady"}`))
			},
			expectFail: true,
		},
		{
			name: "服务器错误",
			handler: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
				w.Write([]byte(`{"error":"internal server error"}`))
			},
			expectFail: true,
		},
		{
			name: "正常就绪状态",
			handler: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"status":"Ready"}`))
			},
			expectFail: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建并启动模拟本地服务器
			server := newMockLocalServer("9981", tc.handler)

			// 启动服务器，如果端口不可用则跳过测试
			if err := server.start(); err != nil {
				t.Skipf("跳过测试: %v", err)
				return
			}
			defer server.stop()

			// 创建新的健康检查器
			checker := NewLocalHealthChecker()

			// 确保初始状态正常
			initialHealthy, _ := checker.GetStatus()
			if !initialHealthy {
				t.Fatal("初始状态应为健康")
			}

			// 执行检查
			started := checker.CheckReadiness(fmt.Sprintf("error-test-%s", tc.name))
			if !started {
				t.Fatal("检查应成功启动")
			}

			// 等待异步检查完成
			time.Sleep(200 * time.Millisecond)

			// 验证最终状态
			_, count := checker.GetStatus()

			if tc.expectFail && count == 0 {
				t.Errorf("期望处理失败（计数 > 0），但计数为0")
			} else if !tc.expectFail && count > 0 {
				t.Errorf("期望处理成功（计数 = 0），但计数为%d", count)
			}

			// 检查 isChecking 状态
			checker.checkMux.Lock()
			isCheckingNow := checker.isChecking
			checker.checkMux.Unlock()
			if isCheckingNow {
				t.Error("检查完成后，isChecking应为false")
			}
		})
	}
}

// 测试警报冷却机制
func TestLocalHealthChecker_AlertCooldown(t *testing.T) {
	// 创建并启动模拟本地服务器，返回错误状态
	server := newMockLocalServer("9981", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte(`{"status":"Error"}`))
	})

	// 启动服务器，如果端口不可用则跳过测试
	if err := server.start(); err != nil {
		t.Skipf("跳过测试: %v", err)
		return
	}
	defer server.stop()

	// 创建健康检查器，手动设置接近阈值
	checker := NewLocalHealthChecker()
	checker.failureCountMux.Lock()
	checker.failureCount = maxFailureCount - 1
	checker.failureCountMux.Unlock()

	// 验证初始状态
	healthy, count := checker.GetStatus()
	if !healthy {
		t.Fatalf("初始状态虽接近阈值但应仍为健康，但获得了不健康")
	}
	if count != maxFailureCount-1 {
		t.Fatalf("初始失败计数应为%d，但为%d", maxFailureCount-1, count)
	}

	// 执行检查触发第一次告警
	started := checker.CheckReadiness("cooldown-test-1")
	if !started {
		t.Fatal("检查应成功启动")
	}

	// 等待异步检查和告警完成
	time.Sleep(200 * time.Millisecond)

	// 验证告警已发送
	checker.failureCountMux.Lock()
	alerted := checker.alerted
	firstAlertTime := checker.lastAlertTime
	checker.failureCountMux.Unlock()

	if !alerted {
		t.Fatal("第一次应该已发送告警")
	}

	// 再次执行检查，由于冷却期应不发送新告警
	checker.CheckReadiness("cooldown-test-2")
	time.Sleep(200 * time.Millisecond)

	// 验证告警时间未更新
	checker.failureCountMux.Lock()
	currentAlertTime := checker.lastAlertTime
	checker.failureCountMux.Unlock()

	if currentAlertTime != firstAlertTime {
		t.Errorf("冷却期内不应发送新告警，但告警时间已从%v更新为%v",
			firstAlertTime, currentAlertTime)
	}

	// 手动修改上次告警时间为足够早以绕过冷却期
	checker.failureCountMux.Lock()
	checker.lastAlertTime = time.Now().Add(-2 * alertCooldown)
	previousTime := checker.lastAlertTime
	checker.failureCountMux.Unlock()

	// 再次执行检查，应发送新告警
	checker.CheckReadiness("cooldown-test-3")
	time.Sleep(200 * time.Millisecond)

	// 验证已发送新告警
	checker.failureCountMux.Lock()
	newAlertTime := checker.lastAlertTime
	checker.failureCountMux.Unlock()

	if newAlertTime.Equal(previousTime) {
		t.Errorf("冷却期过后应发送新告警，但告警时间未更新，仍为%v", newAlertTime)
	}
}
