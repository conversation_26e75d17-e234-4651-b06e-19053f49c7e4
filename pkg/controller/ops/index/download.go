package index

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/web"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/process/download/index"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
	"net/http"
)

func init() {
	web.RegisterHttpHandler[string](NewIndexDownloadHttpHandler())
}

type OssKey struct {
	OssKey    string `json:"ossKey"`
	KeySecret string `json:"keySecret"`
}

type IndexDownloadRequest struct {
	IndexName    string `json:"indexName,omitempty" validate:"required"`
	IndexVersion string `json:"indexVersion,omitempty" validate:"required"`
	DbPath       string `json:"dbPath,omitempty" default:"/app/rel/data/cdb"`
	GzPath       string `json:"gzPath,omitempty" default:"/app/rel/data/gz"`
	OssDir       string `json:"ossDir" validate:"required"` // oss目录
}

type IndexDownloadHttpHandler struct {
}

func NewIndexDownloadHttpHandler() *IndexDownloadHttpHandler {
	return &IndexDownloadHttpHandler{}
}

func (h *IndexDownloadHttpHandler) Path() string {
	return "/api/v1/ops/index/download"
}

func (h *IndexDownloadHttpHandler) Method() string {
	return http.MethodPost
}

// ProcessRequest 获取引擎服务状态(包含索引状态)
func (h *IndexDownloadHttpHandler) ProcessRequest(ctx context.Context, r *http.Request) *api.Response[string] {
	var request IndexDownloadRequest
	requestId, r := util.GetRequestId(r)
	err := cfg.UnpackReaderWithJson(r.Body, &request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	requestJson, _ := json.Marshal(request)
	log.Info("[%s]index download request: %s", requestId, string(requestJson))

	ossConfig, err := platform.GetOssConfig()
	if err != nil {
		return api.SystemFail[string](err)
	}

	// 默认配置
	isCompress := true
	//isChecksum := false // 为了兼容老版本, 默认不校验
	isChecksum := true
	isReboot := true
	moveWithRsync := false // 默认不使用rsync, mv命令更快
	checkBuildResult := true
	isForce := true
	// 内存使用率阈值
	memoryUsageThreshold := 93
	if !api.CurrentEnv.IsPrd() {
		memoryUsageThreshold = 150
	}
	ossDef := &dto.Oss{
		AccessKey:   ossConfig.OssKey,
		SecretKey:   ossConfig.KeySecret,
		Endpoint:    api.OssEndPoint,
		Bucket:      api.OssBucket,
		Dir:         request.OssDir,
		Concurrency: 8,
		LimitSpeed:  false,
	}

	pr := &index.ProcessRequest{
		IndexName:                 request.IndexName,
		IndexVersion:              request.IndexVersion,
		DbPath:                    request.DbPath,
		GzPath:                    request.GzPath,
		Compress:                  &isCompress,
		CheckBuildResult:          &checkBuildResult,
		Checksum:                  &isChecksum,
		ChecksumFileName:          "checksum.json",
		Oss:                       ossDef,
		EngineBuildResultFileName: "check.json",
		AgentConfigDirName:        "agent_config",
		IsReboot:                  &isReboot,
		Force:                     &isForce,
		MoveWithRsync:             &moveWithRsync,
		MemoryUsageThreshold:      memoryUsageThreshold,
	}
	err = cfg.SetDefaultAndValidate(pr)
	if err != nil {
		return api.SystemFail[string](err)
	}
	rmi, err := index.RunTask(requestId, pr)
	if err != nil {
		return api.SystemFail[string](err)
	}
	content, _ := json.Marshal(rmi)
	log.Info("[%s]download index result: %s", requestId, string(content))

	return api.OfSuccessWithMessage("ok", string(content))
}
