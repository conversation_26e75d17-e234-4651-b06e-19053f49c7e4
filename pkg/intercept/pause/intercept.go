package stop

import (
	"dip-agent/pkg/consts"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/intercept"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/report"
	ut "dip-agent/pkg/util/time"
	"fmt"
	"time"
)

const (
	TypeName = "pause"
)

func init() {
	pipeline.Register(api.INTERCEPTOR, TypeName, makeIntercept)
}

func makeIntercept(info pipeline.Info) api.Component {
	return NewInterceptor(info)
}

type Interceptor struct {
	intercept.Abstract
}

func NewInterceptor(info pipeline.Info) *Interceptor {
	return &Interceptor{
		Abstract: intercept.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (i *Interceptor) Config() interface{} {
	return nil
}

func (i *Interceptor) Start() error {
	return nil
}

func (i *Interceptor) Intercept(invoker api.Invoker, invocation api.Invocation) api.Result {
	// 如果任务没有被暂停, 正常执行
	if !invocation.IsPause() {
		return invoker.Invoke(invocation)
	}

	start := time.Now()
	// 如果任务是暂停状态，将等待调用恢复，并返回调用结果
	msg := fmt.Sprintf("[%s]invoke processor is paused: %s", invocation.RequestId, invocation.ProcessorName)
	log.Info(msg)

	// 获取当前执行的总体进度
	var totalProgress int
	tp, exist := invocation.Carrier.Get(consts.TotalProgress)
	if !exist {
		totalProgress = 0
		invocation.Carrier.Put(consts.TotalProgress, totalProgress)
	} else {
		totalProgress = tp.(int)
	}

	// 上报被暂停成功的步骤信息
	// 上报地址
	reportUrl := invocation.Carrier.GetString("reportUrl")
	// 上报开始的步骤信息
	reportReq := report.EngineTaskStatusReportReq{
		TaskId:        invocation.TaskId,
		StepName:      invocation.ProcessorName + "-pause",
		StepStartTime: ut.FormatNowDefault(),
		StepEndTime:   ut.FormatNowDefault(),
		StepStatus:    report.StepStatusRunning,
		StepResult:    invocation.ProcessorName + "被暂停",
		StepProgress:  0,
		TotalProgress: totalProgress,
	}
	reportReq.Send(invocation.RequestId, reportUrl)

	// 响应暂停
	invocation.Wait()

	// 从暂停中恢复
	log.Info("[%s]invoke processor is resumed: %s", invocation.RequestId, invocation.ProcessorName)

	// 暂停恢复后，上报恢复成功的步骤信息
	reportReq.StepStatus = report.StetStatusResume
	reportReq.StepEndTime = ut.FormatNowDefault()
	reportReq.StepProgress = 100
	reportReq.StepCost = time.Since(start).Milliseconds()
	reportReq.StepResult = invocation.ProcessorName + "暂停已恢复"
	reportReq.Send(invocation.RequestId, reportUrl)

	// 继续执行
	return invoker.Invoke(invocation)
}
