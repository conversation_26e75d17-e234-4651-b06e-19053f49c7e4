package report

import (
	"dip-agent/pkg/consts"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/intercept"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/report"
	"dip-agent/pkg/util"
	ut "dip-agent/pkg/util/time"
	"time"
)

const (
	TypeName = "report"

	StepStatusNotStart StepStatus = 0 // 未开始
	StepStatusRunning  StepStatus = 1 // 执行中
	StepStatusSuccess  StepStatus = 2 // 执行成功
	StepStatusFailed   StepStatus = 3 // 执行失败
)

func init() {
	pipeline.Register(api.INTERCEPTOR, TypeName, makeIntercept)
}

func makeIntercept(info pipeline.Info) api.Component {
	return NewInterceptor(info)
}

type StepStatus int

type EngineTaskStatusReportReq struct {
	TaskId        string     `json:"taskId" validate:"required"`                // 任务id: 子任务id
	StepName      string     `json:"stepName" validate:"required"`              // 执行步骤名称
	StepStartTime string     `json:"stepStartTime" validate:"datetime"`         // 执行步骤开始时间: 格式为yyyy-MM-dd HH:mm:ss
	StepEndTime   string     `json:"stepEndTime,omitempty" validate:"datetime"` // 执行步骤结束时间: 格式为yyyy-MM-dd HH:mm:ss
	StepStatus    StepStatus `json:"stepStatus" validate:"required"`            // 执行步骤状态: 0-未开始, 1-执行中, 2-执行成功, 3-执行失败
	StepCost      int64      `json:"stepCost" validate:"min=0"`                 // 执行步骤耗时: 单位为毫秒
	StepResult    string     `json:"stepResult" validate:"max=2048"`            // 执行结果: 例如执行成功后的数据处理数量; 执行失败的错误原因
	StepProgress  int        `json:"stepProgress" validate:"min=0,max=100"`     // 执行步骤进度: 0~100, 表示执行进度百分比
	TotalProgress int        `json:"totalProgress" validate:"min=0,max=100"`    // 整体执行进度: 0~100, 表示整体执行进度百分比
	//DumpCount     int64     `json:"dumpCount" validate:"min=0"`             // dump数据量
	//IndexSize     int64     `json:"indexSize" validate:"min=0"`             // 构建的索引大小: 单位为byte
}

type Interceptor struct {
	intercept.Abstract
	config *Config
	hc     *util.HttpClient
}

func NewInterceptor(info pipeline.Info) *Interceptor {
	return &Interceptor{
		config: &Config{},
		Abstract: intercept.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (i *Interceptor) Config() interface{} {
	return i.config
}

func (i *Interceptor) Start() error {
	// 目前平台的响应code为0表示成功
	i.hc = util.NewHttpClientWithSuccessCode(30*time.Second, []string{"0"})
	return nil
}

func (i *Interceptor) Intercept(invoker api.Invoker, invocation api.Invocation) api.Result {
	start := time.Now()
	requestId := invocation.RequestId
	// 获取当前执行的总体进度
	var totalProgress int
	tp, exist := invocation.Carrier.Get(consts.TotalProgress)
	if !exist {
		totalProgress = 0
		invocation.Carrier.Put(consts.TotalProgress, totalProgress)
	} else {
		totalProgress = tp.(int)
	}

	// 上报地址
	reportUrl := invocation.Carrier.GetString("reportUrl")
	if reportUrl == "" {
		reportUrl = api.BrainDomain + i.config.ReportPath
	}

	// 上报开始的步骤信息
	reportReq := report.EngineTaskStatusReportReq{
		TaskId:        invocation.TaskId,
		StepName:      invocation.ProcessorName,
		StepStartTime: ut.FormatNowDefault(),
		StepEndTime:   ut.FormatNowDefault(),
		StepStatus:    report.StepStatusRunning,
		StepProgress:  0,
		TotalProgress: totalProgress,
	}
	reportReq.Send(requestId, reportUrl)

	// 执行步骤
	r := invoker.Invoke(invocation)

	// 上报结束的步骤信息
	if r.Status().IsSuccess() {
		reportReq.StepStatus = report.StepStatusSuccess
		reportReq.StepProgress = 100
	} else {
		reportReq.StepStatus = report.StepStatusFailed
		reportReq.StepProgress = 0
	}
	reportReq.StepEndTime = ut.FormatNowDefault()
	reportReq.StepCost = time.Since(start).Milliseconds()

	// 附加上报信息
	if dc, exist := invocation.Carrier.Get(consts.ReportDumpCount); exist {
		reportReq.DumpCount = dc.(int64)
	}
	if is, exist := invocation.Carrier.Get(consts.ReportIndexSize); exist {
		reportReq.IndexSize = is.(int64)
	}

	// 消息长度限制
	msg := r.Message()
	if len(msg) > 2048 {
		msg = msg[:2040] + "..."
	}
	reportReq.StepResult = msg

	totalProgress = totalProgress + invocation.ProcessorStepProgress
	invocation.Carrier.Put(consts.TotalProgress, totalProgress)
	reportReq.TotalProgress = totalProgress
	reportReq.Send(requestId, reportUrl)
	return r
}
