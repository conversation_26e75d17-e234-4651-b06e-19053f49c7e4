package exception

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/intercept"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/notify"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/platform"
	"fmt"
	"strings"
)

const (
	TypeName = "exception"
)

func init() {
	pipeline.Register(api.INTERCEPTOR, TypeName, makeIntercept)
}

func makeIntercept(info pipeline.Info) api.Component {
	return NewInterceptor(info)
}

type Interceptor struct {
	intercept.Abstract
	notify.Notifier
}

func NewInterceptor(info pipeline.Info) *Interceptor {
	return &Interceptor{
		Abstract: intercept.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
		Notifier: platform.NewWarnRequestWithMsg("", ""),
	}
}

func (i *Interceptor) Config() interface{} {
	return nil
}

func (i *Interceptor) Start() error {
	return nil
}

func (i *Interceptor) Intercept(invoker api.Invoker, invocation api.Invocation) api.Result {
	// 执行步骤
	r := invoker.Invoke(invocation)

	if !r.Status().IsFail() {
		return r
	}

	return i.exceptionHandle(invocation.RequestId, r)
}

// 异常信息处理
func (i *Interceptor) exceptionHandle(requestId string, r api.Result) api.Result {
	errMsg := r.Message()
	errorDetails := r.Error()

	// errorDetails处理
	if errorDetails != nil {
		detailedMessage := errorDetails.DetailedMessage
		// 引擎没有部署agent标准接口
		if strings.Contains(detailedMessage, "Fail to find method") {
			errorDetails.DetailedMessage = "[engine has not deployed the agent api]" + detailedMessage
			errMsg = "[失败原因: 引擎没有部署agent接口代码]" + errMsg
			r.SetMessage(errMsg)
			return r
		}

		// 构建引擎被kill, 通常是由于构建容器资源不足
		if strings.Contains(detailedMessage, "killed") {
			errorDetails.ErrorType = api.ErrorType(api.BuildException_InsufficientResources)
			errorDetails.DetailedMessage = "[engine process killed, container resources not enough!]" + detailedMessage
			errMsg = "[失败原因: 引擎进程被kill, 容器资源不足. 请至索引平台->索引管理->设置额外参数: containerMemorySize(GB)/containerDiskSize(GB)/containerCoreSize]" + errMsg
			r.SetMessage(errMsg)
			return r
		}

		// 追增量超时: wait inc timeout
		if strings.Contains(detailedMessage, "wait inc timeout") {
			//errorDetails.DetailedMessage = "[wait inc timeout]" + detailedMessage
			errorDetails.ErrorType = api.ErrorType(api.UpdateException_WaitIncTimeout)
			errMsg = "[失败原因: 追增量超时]" + errMsg
			r.SetMessage(errMsg)
			return r
		}
	}

	// error message 处理
	// 优先在次处理!!!
	if errMsg != "" {

		// TODO: 参数不合法. 区分dump\build\update阶段

		// 主键重复: duplicated primary key
		if strings.Contains(errMsg, "duplicated primary key") {
			r.SetMessage("[失败原因: 源表数据异常, 主键冲突]" + errMsg)
			return r
		}

		// key重复超过阈值: key has been existed, no need to insert
		if strings.Contains(errMsg, "key has been existed, no need to insert") {
			r.SetMessage("[失败原因: 源表数据异常, key重复超过阈值]" + errMsg)
			return r
		}

		// 内存不足: memory space not enough
		if strings.Contains(errMsg, "memory space not enough") {
			r.SetMessage(fmt.Sprintf("[失败原因: 集群节点(%s)内存不足]%s", api.NodeIp, errMsg))
			return r
		}

		// 磁盘空间不足: disk space not enough
		if strings.Contains(errMsg, "disk space not enough") {
			r.SetMessage(fmt.Sprintf("[失败原因: 集群节点(%s)磁盘空间不足]%s", api.NodeIp, errMsg))
			return r
		}

		// 索引构建的引擎二进制版本与引擎服务的二进制版本不一致: 例如: index binary_version 2 != srv binary_version 1
		if strings.Contains(errMsg, "index binary_version") && strings.Contains(errMsg, "srv binary_version") && strings.Contains(errMsg, "!=") {
			r.SetMessage("[失败原因: 索引构建二进制版本与引擎服务的二进制版本不一致]" + errMsg)
			return r
		}

		// 调用引擎reload接口返回key id冲突: mmap
		if strings.Contains(errMsg, "/ApiService/HotReload") && strings.Contains(errMsg, "mmap") {
			r.SetMessage("[失败原因: 调用引擎热更新接口失败, key id冲突]" + errMsg)
			return r
		}

		// 创建dump service失败: create dump service fail
		if strings.Contains(errMsg, "create dump service fail") {
			if strings.Contains(errMsg, "odps client timeout") {
				r.SetMessage("[失败原因: 创建odps-client超时]" + errMsg)
			} else {
				r.SetMessage("[失败原因: 创建odps-client失败]" + errMsg)
			}
			return r
		}

		// 调用平台接口失败: call dip-backend api fail
		if strings.Contains(errMsg, "call dip-backend api fail") {
			r.SetMessage("[失败原因: 调用平台接口失败]" + errMsg)
			return r
		}

		// 没有数据可dump: no data to dump
		if strings.Contains(errMsg, "no data to dump") {
			r.SetMessage("[失败原因: odps分区无数据或者数据为空条目]" + errMsg)
			return r
		}

		// 磁盘空间不足: no space left on device
		if strings.Contains(errMsg, "no space left on device") {
			r.SetMessage("[失败原因: 磁盘空间不足]" + errMsg)
			return r
		}

		// 引擎索引构建失败: engine build index fail
		if strings.Contains(errMsg, "engine build index fail") {
			// each row of data must contain key_field
			if strings.Contains(errMsg, "each row of data must contain key_field") {
				r.SetMessage("[失败原因: 引擎(build-engine)索引构建失败, 表数据不正确]" + errMsg)
			} else {
				r.SetMessage("[失败原因: 引擎(build-engine)索引构建失败]" + errMsg)
			}
			return r
		}

		// 引擎卸载索引失败: index unload check fail
		if strings.Contains(errMsg, "index unload check fail") {
			r.SetMessage("[失败原因: 引擎卸载索引失败]" + errMsg)
			return r
		}

		// 热更新双版本卸载异常: outdated version of normal task can not greater than 1
		if strings.Contains(errMsg, "outdated version of normal task can not greater than 1") {
			newErrMsg := "[失败原因: 热更新双版本卸载异常, 本地过期版本超过1]" + errMsg
			r.SetMessage(newErrMsg)
			// 会影响 pipeline 中是否继续执行的判断逻辑
			// @see pipeline.go # 237
			//r.SetStatus(api.STOP_AND_ROLLBACK)
			// TODO 平台支持 code 自动化处理后，无法发送报警
			// 发送报警
			err := i.SendNotification(requestId, newErrMsg)
			if err != nil {
				log.Warn("send warn notification fail: %s", err)
			}
			return r
		}

		// 数据问题: consume record error
		if strings.Contains(errMsg, "consume record error") {
			r.SetMessage("[失败原因: odps源表数据存在问题]" + errMsg)
			return r
		}

		// 索引正在追增量，无法卸载: index inc is not EOF, cannot unload index
		if strings.Contains(errMsg, "index inc is not EOF, cannot unload index") {
			r.SetMessage("[失败原因: 索引正在追增量, 无法卸载]" + errMsg)
			return r
		}

		// check dir read/write fail：磁盘不可读写，容器挂载磁盘异常
		if strings.Contains(errMsg, "check dir read/write fail") {
			r.SetMessage("[失败原因: 磁盘不可读写，容器挂载磁盘异常]" + errMsg)
			// 会影响 pipeline 中是否继续执行的判断逻辑
			// @see pipeline.go # 237
			//r.SetStatus(api.STOP_AND_ALERT)
			return r
		}

		// 任务调度校验失败, 不属于当前集群: check task in cluster fail
		if strings.Contains(errMsg, "check task in cluster fail") {
			r.SetMessage(fmt.Sprintf("[失败原因: 任务不属于当前集群(%s-%s)]%s", api.ClusterCode, api.ClusterGroup, errMsg))
			return r
		}

		// 保持此项在最后, 以便更详细的错误信息展示
		// 调用引擎接口报错: call engine api fail
		if strings.Contains(errMsg, "call engine api fail") {
			// 是否调用超时
			if strings.Contains(errMsg, "context deadline exceeded") {
				r.SetMessage("[失败原因: 调用引擎接口超时]" + errMsg)
			} else if strings.Contains(errMsg, "connection refused") {
				r.SetMessage("[失败原因: 调用引擎接口失败, 引擎服务未启动]" + errMsg)
			} else if strings.Contains(errMsg, "connection reset by peer") {
				r.SetMessage("[失败原因: 调用引擎接口失败, 引擎服务连接被重置]" + errMsg)
			} else if strings.Contains(errMsg, "no such host") {
				r.SetMessage("[失败原因: 调用引擎接口失败, 引擎服务地址不存在]" + errMsg)
			} else if strings.Contains(errMsg, "connection timed out") {
				r.SetMessage("[失败原因: 调用引擎接口超时]" + errMsg)
			} else {
				r.SetMessage("[失败原因: 调用引擎接口失败]" + errMsg)
			}
			return r
		}

	}

	return r
}
