package stop

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/intercept"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/report"
	ut "dip-agent/pkg/util/time"
	"fmt"
)

const (
	TypeName = "stop"
)

func init() {
	pipeline.Register(api.INTERCEPTOR, TypeName, makeIntercept)
}

func makeIntercept(info pipeline.Info) api.Component {
	return NewInterceptor(info)
}

type Interceptor struct {
	intercept.Abstract
}

func NewInterceptor(info pipeline.Info) *Interceptor {
	return &Interceptor{
		Abstract: intercept.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (i *Interceptor) Config() interface{} {
	return nil
}

func (i *Interceptor) Start() error {
	return nil
}

func (i *Interceptor) Intercept(invoker api.Invoker, invocation api.Invocation) api.Result {
	// 如果任务没有被终止, 正常执行
	if !invocation.IsStop() {
		return invoker.Invoke(invocation)
	}

	// 如果任务步骤被终止, 判断当前步骤是否可以终止
	if !invocation.CanStop {
		log.Warn(fmt.Sprintf("[任务当前步骤不可终止][%s]task invoke processor can not stop: %s", invocation.RequestId, invocation.ProcessorName))
		return invoker.Invoke(invocation)
	}

	// 如果任务被终止, 上报终止成功的步骤信息
	msg := fmt.Sprintf("[任务被终止][%s]task invoke processor stopped: %s", invocation.RequestId, invocation.ProcessorName)
	log.Info(msg)

	// 上报被终止成功的步骤信息
	// 上报地址
	reportUrl := invocation.Carrier.GetString("reportUrl")
	// 上报开始的步骤信息
	reportReq := report.EngineTaskStatusReportReq{
		TaskId:        invocation.TaskId,
		StepName:      invocation.ProcessorName + "-stop",
		StepStartTime: ut.FormatNowDefault(),
		StepEndTime:   ut.FormatNowDefault(),
		StepStatus:    report.StepStatusRunning,
		StepProgress:  0,
		TotalProgress: 0,
	}
	reportReq.Send(invocation.RequestId, reportUrl)

	return api.OfStopWith(msg)
}
