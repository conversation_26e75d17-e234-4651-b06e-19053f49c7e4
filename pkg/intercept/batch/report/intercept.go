package report

import (
	"dip-agent/pkg/consts"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/intercept"
	"dip-agent/pkg/dto"
	ri "dip-agent/pkg/intercept/report"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/report"
	"dip-agent/pkg/util"
	ut "dip-agent/pkg/util/time"
	"fmt"
	"time"
)

const (
	TypeName = "batch-report" // TODO 批量执行的上报拦截器
)

func init() {
	pipeline.Register(api.INTERCEPTOR, TypeName, makeIntercept)
}

func makeIntercept(info pipeline.Info) api.Component {
	return NewInterceptor(info)
}

type Interceptor struct {
	intercept.Abstract
	config *ri.Config
	hc     *util.HttpClient
}

func NewInterceptor(info pipeline.Info) *Interceptor {
	return &Interceptor{
		config: &ri.Config{},
		Abstract: intercept.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (i *Interceptor) Config() interface{} {
	return i.config
}

func (i *Interceptor) Start() error {
	// 目前平台的响应code为0表示成功
	i.hc = util.NewHttpClientWithSuccessCode(30*time.Second, []string{"0"})
	return nil
}

func (i *Interceptor) Intercept(invoker api.Invoker, invocation api.Invocation) api.Result {
	start := time.Now()
	requestId := invocation.RequestId
	// 获取当前执行的总体进度
	var totalProgress int
	tp, exist := invocation.Carrier.Get(consts.TotalProgress)
	if !exist {
		totalProgress = 0
		invocation.Carrier.Put(consts.TotalProgress, totalProgress)
	} else {
		totalProgress = tp.(int)
	}

	var (
		reportReqMap = make(map[string]dto.UpdateIndexInfo)
	)

	// 上报地址
	reportUrl := invocation.Carrier.GetString("reportUrl")
	if reportUrl == "" {
		reportUrl = api.BrainDomain + i.config.ReportPath
	}

	// 获取需要上报的索引信息
	if reportIndexInfoObj, exist := invocation.Carrier.Get(consts.ReportIndexInfo); exist {
		reqs := make([]*report.EngineTaskStatusReportReq, 0)
		if reportIndexInfo, ok := reportIndexInfoObj.([]*dto.UpdateIndexInfo); ok {
			for _, indexInfo := range reportIndexInfo {
				if !indexInfo.CanUpdate {
					continue
				}
				// 上报索引信息
				reportReq := &report.EngineTaskStatusReportReq{
					TaskId:        invocation.TaskId,
					IndexName:     indexInfo.IndexName,
					IndexVersion:  indexInfo.IndexVersion,
					StepName:      invocation.ProcessorName,
					StepStartTime: ut.FormatDefault(start),
					StepEndTime:   ut.FormatNowDefault(),
					StepStatus:    report.StepStatusRunning,
					StepProgress:  0,
					TotalProgress: totalProgress,
				}
				reqs = append(reqs, reportReq)
				//reportReq.Send(requestId, reportUrl)
				reportReqMap[indexInfo.IndexName] = *indexInfo
			}
		}
		if len(reqs) > 0 {
			report.SendBatchStatus(requestId, reqs)
		}
	}

	// 执行步骤
	r := invoker.Invoke(invocation)

	// 更新总体进度
	totalProgress = totalProgress + invocation.ProcessorStepProgress
	invocation.Carrier.Put(consts.TotalProgress, totalProgress)

	// 执行结束后, 获取需要上报的任务
	if reportIndexInfoObj, exist := invocation.Carrier.Get(consts.ReportIndexInfo); exist {
		reqs := make([]*report.EngineTaskStatusReportReq, 0)
		if reportIndexInfo, ok := reportIndexInfoObj.([]*dto.UpdateIndexInfo); ok {
			for _, indexInfo := range reportIndexInfo {
				reportReq := report.EngineTaskStatusReportReq{
					TaskId:        invocation.TaskId,
					IndexName:     indexInfo.IndexName,
					IndexVersion:  indexInfo.IndexVersion,
					StepName:      invocation.ProcessorName,
					StepStartTime: ut.FormatDefault(start),
					StepEndTime:   ut.FormatNowDefault(),
					StepCost:      time.Since(start).Milliseconds(),
					StepStatus:    report.StepStatusSuccess,
					StepProgress:  100,
					TotalProgress: totalProgress,
				}

				// 执行状态
				if r.Status().IsSuccess() {
					reportReq.StepStatus = report.StepStatusSuccess
					reportReq.StepProgress = 100
				} else {
					reportReq.StepStatus = report.StepStatusFailed
					reportReq.StepProgress = 0
				}

				ext := indexInfo.ReportExt
				if ext != nil {
					// 重置任务开始&结束时间
					if st, exist := ext[consts.ReportStartTime]; exist {
						if t, ok := st.(time.Time); ok && !t.IsZero() {
							reportReq.StepStartTime = ut.FormatDefault(t)
						}
					}
					if et, exist := ext[consts.ReportEndTime]; exist {
						if t, ok := et.(time.Time); ok && !t.IsZero() {
							reportReq.StepEndTime = ut.FormatDefault(t)
						}
					}

					// 附加上报信息
					if dc, exist := ext[consts.ReportDumpCount]; exist {
						reportReq.DumpCount = dc.(int64)
					}
					if is, exist := ext[consts.ReportIndexSize]; exist {
						reportReq.IndexSize = is.(int64)
					}
				}

				// 消息长度限制
				msg := r.Message()
				if len(msg) > 2048 {
					msg = msg[:2040] + "..."
				}
				reportReq.StepResult = msg

				if !indexInfo.CanUpdate {
					reportReq.StepStatus = report.StepStatusFailed
					if beforeReportIndexInfo, reqExist := reportReqMap[indexInfo.IndexName]; reqExist && beforeReportIndexInfo.CanUpdate {
						reportReq.StepResult = fmt.Sprintf("[%s]%s", indexInfo.Reason, reportReq.StepResult)
					} else {
						continue
					}
				}
				//reportReq.Send(requestId, reportUrl)
				reqs = append(reqs, &reportReq)
			}
		}
		if len(reqs) > 0 {
			report.SendBatchStatus(requestId, reqs)
		}
	}

	return r
}

// TODO 处理异步上报: 消费事件中心事件
