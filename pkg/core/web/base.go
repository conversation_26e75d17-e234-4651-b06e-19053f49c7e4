package web

import (
	"context"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"fmt"
	"net/http"
	"runtime/debug"
	"time"
)

// HttpHandler is a generic interface for handling HTTP requests.
type HttpHandler[T any] interface {
	Path() string
	Method() string
	ProcessRequest(ctx context.Context, r *http.Request) *api.Response[T]
}

func RegisterHttpHandler[T any](handler HttpHandler[T]) {
	Register(handler.Path(), httpHandlerWrapper(handler))
}

func httpHandlerWrapper[T any](h HttpHandler[T]) Handler {
	path := h.Path()
	method := h.Method()

	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		if method != "" && method != "*" && r.Method != method {
			msg := fmt.Sprintf("Method not allowed: [%s], expect method [%s]", r.Method, method)
			http.Error(w, msg, http.StatusMethodNotAllowed)
			return
		}

		// 获取requestId
		requestId, r := util.GetRequestId(r)

		// 获取请求体
		//if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		//	http.Error(w, "Bad request: "+err.Error(), http.StatusBadRequest)
		//	return
		//}

		// 处理请求
		response := h.ProcessRequest(r.Context(), r)

		// 设置通用属性
		response.RequestId = requestId
		response.Cost = time.Since(start).Milliseconds()
		response.Ip = api.NodeIp

		// 响应是否成功
		if !response.IsSuccess() {
			log.Error("[%s - %s]response error: %s. parent stack: \n%s", requestId, path, response.Error().Error(), debug.Stack())
		}

		// 返回响应
		responseJson, err := json.Marshal(response)
		if err != nil {
			http.Error(w, fmt.Sprintf("[%s]Error encoding response", requestId), http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json;charset=UTF-8")
		_, err = w.Write(responseJson)
		if err != nil {
			log.Error("[%s]write response error: %v", requestId, err)
			return
		}
	}
}
