package api

import (
	"strings"
)

const (
	CodeSuccess = Code(200)
	CodeFail    = Code(500)
)

type Code int

func (c Code) IsSuccess() bool {
	return c == CodeSuccess
}

func NewCodeFromStatus(status Status) Code {
	switch status {
	case SUCCESS:
		return CodeSuccess
	default:
		return Code(status)
	}
}

func NewStatusFromCode(code Code) Status {
	switch code {
	case CodeSuccess:
		return SUCCESS
	case CodeFail:
		return FAIL
	default:
		return Status(code)
	}
}

type Response[T any] struct {
	Code            Code          `json:"code"`                   // 响应编码. 200表示成功，其他表示错误/异常
	ResponseMessage string        `json:"message,omitempty"`      // 用户友好展示信息
	ResponseData    T             `json:"data,omitempty"`         // 响应数据，为泛型类型
	ErrorDetails    *ErrorDetails `json:"errorDetails,omitempty"` // 错误详细信息

	// 通用属性, 父类自动添加
	Cost      int64  `json:"cost"`      // 耗时, 单位ms
	RequestId string `json:"requestId"` // 请求id
	Ip        string `json:"ip"`        // 本机ip

	status   Status // code是status的最终状态表示
	extraMap map[string]interface{}
}

func (as *Response[T]) Status() Status {
	return as.status
}

func (as *Response[T]) SetStatus(s Status) {
	as.status = s
}

func (as *Response[T]) Error() *ErrorDetails {
	return as.ErrorDetails
}

func (as *Response[T]) Data() interface{} {
	return as.ResponseData
}

func (as *Response[T]) Message() string {
	return as.ResponseMessage
}

func (as *Response[T]) DetailMessage() string {
	var r strings.Builder
	if as.ResponseMessage != "" {
		r.WriteString("response message: ")
		r.WriteString(as.ResponseMessage)
	}
	if as.ErrorDetails != nil {
		if r.Len() > 0 {
			r.WriteByte('\n')
		}
		r.WriteString(as.ErrorDetails.Error())
	}
	return r.String()
}

func (as *Response[T]) SetMessage(message string) {
	as.ResponseMessage = message
}

func (as *Response[T]) Extra() map[string]interface{} {
	return as.extraMap
}

// IsSuccess 方法判断是否成功响应
func (as *Response[T]) IsSuccess() bool {
	return as.Code == CodeSuccess
}

// OfSuccess 创建一个成功的ApiResponse
func OfSuccess[T any](data T) *Response[T] {
	return OfSuccessWithMessage(data, "Success")
}

func OfSuccessWithMessage[T any](data T, message string) *Response[T] {
	return &Response[T]{
		Code:            CodeSuccess,
		ResponseData:    data,
		ResponseMessage: message,
		extraMap:        make(map[string]interface{}),

		status: SUCCESS,
	}
}

// OfStop 创建一个终止的ApiResponse
func OfStop() *Response[string] {
	return &Response[string]{
		Code:            CodeSuccess,
		ResponseMessage: "任务终止",
		extraMap:        make(map[string]interface{}),

		status: STOP,
	}
}

// OfStopWith 创建一个终止的ApiResponse
func OfStopWith(msg string) *Response[string] {
	return &Response[string]{
		Code:            CodeSuccess,
		ResponseMessage: msg,
		extraMap:        make(map[string]interface{}),

		status: STOP,
	}
}

func ParamFail[T any](err error) *Response[T] {
	return FailWithErr[T](ParameterException, err)
}

func SystemFail[T any](err error) *Response[T] {
	return FailWithErr[T](SystemException, err)
}

func DumpFail[T any](err error) *Response[T] {
	ed := NewDumpError(err)
	return &Response[T]{
		Code:            CodeFail,
		ResponseMessage: err.Error(),
		ErrorDetails:    ed,
		extraMap:        make(map[string]interface{}),

		status: FAIL,
	}
}

func BuildFail[T any](err error) *Response[T] {
	return BuildFailWithMessage[T](err, err.Error())
}

func BuildFailWithMessage[T any](err error, message string) *Response[T] {
	return FailWithMessage[T](BuildException, err, message)
}

func UpdateFail[T any](err error) *Response[T] {
	return FailWithErr[T](UpdateException, err)
}

func UpdateFailWithMessage[T any](err error, message string) *Response[T] {
	return FailWithMessage[T](UpdateException, err, message)
}

func FailWithCode[T any](code int, err error) *Response[T] {
	return FailWithErr[T](ErrorType(code), err)
}

func FailWithErr[T any](et ErrorType, err error) *Response[T] {
	return FailWithMessage[T](et, err, err.Error())
}

func FailWithMessage[T any](et ErrorType, err error, message string) *Response[T] {
	return OfFail[T](et, err, message)
}

func FailWithData[T any](err error, data T) *Response[T] {
	return OfFailWithData[T](SystemException, err, err.Error(), data)
}

func OfFail[T any](et ErrorType, err error, message string) *Response[T] {
	ed := FromErrorWithType(et, err)
	r := &Response[T]{
		Code:            CodeFail,
		ResponseMessage: message,
		ErrorDetails:    ed,
		extraMap:        make(map[string]interface{}),

		status: FAIL,
	}
	return r
}

func OfFailWithData[T any](et ErrorType, err error, message string, data T) *Response[T] {
	ed := FromErrorWithType(et, err)
	r := &Response[T]{
		Code:            CodeFail,
		ResponseData:    data,
		ResponseMessage: message,
		ErrorDetails:    ed,
		extraMap:        make(map[string]interface{}),

		status: FAIL,
	}
	return r
}

func FromResult[T any](r Result) *Response[T] {
	// 如果r是*Response[T]类型, 直接返回
	if ar, ok := r.(*Response[T]); ok {
		return ar
	}
	ar := &Response[T]{
		Code:            NewCodeFromStatus(r.Status()),
		ResponseMessage: r.Message(),
		ErrorDetails:    r.Error(),
		extraMap:        r.Extra(),
	}
	// 设置data
	data := r.Data()
	if data != nil {
		if d, ok := data.(T); ok {
			ar.ResponseData = d
		}
	}
	if r.Extra() != nil {
		c := r.Extra()[Cost]
		if c != nil {
			if cost, ok := c.(int64); ok {
				ar.Cost = cost
			}
		}
		rid := r.Extra()[RequestId]
		if rid != nil {
			if requestId, ok := rid.(string); ok {
				ar.RequestId = requestId
			}
		}
	}
	ar.Ip = NodeIp
	return ar
}
