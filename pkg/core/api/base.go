package api

import (
	"dip-agent/pkg/core/cfg"
	"strings"
)

const (
	AgentName = "dip-agent"
	VERSION   = "1.1.8"

	TASK        = Category("task")
	PROCESSOR   = Category("processor")
	INTERCEPTOR = Category("interceptor")

	// BUILD 索引构建任务
	BUILD = Type("build")
	// UPDATE 索引更新任务
	UPDATE = Type("update")

	RequestId = "requestId"
	Cost      = "cost"
)

var (
	NodeName            string
	NodeIp              string
	Pid                 int
	BackendDomain       string
	BrainDomain         string
	ClusterCode         string
	ClusterGroup        string
	CurrentEnv          Env
	BuildTime           string
	CommitId            string
	EngineBinaryVersion map[string]int
)

type Category string
type Type string
type Env string

// IsPrd 是否线上环境
func (e Env) IsPrd() bool {
	return e == "prd" || e == "csprd" || strings.Contains(string(e), "prd")
}

// IsTest 是否为测试环境
func (e Env) IsTest() bool {
	return e == "test" || e == "d1" || e == "d2" || strings.Contains(string(e), "test")
}

// IsEmpty 环境是否为空
func (e Env) IsEmpty() bool {
	return e == ""
}

type Result interface {
	Status() Status // 这里一定是终态
	SetStatus(status Status)
	Error() *ErrorDetails
	Data() interface{}
	Message() string
	SetMessage(message string)
	Extra() map[string]interface{}
}

type Context interface {
	Name() string
	Category() Category
	Type() Type
	Properties() cfg.CommonCfg
}

type Config interface {
	Config() interface{}
}

type Describable interface {
	Category() Category
	Type() Type
	String() string
	Name() string
}

type Lifecycle interface {
	// Init initializes the component with the given context
	Init(context Context) error
	// Start starts the component, non-blocking
	Start() error
	// Stop stops the component
	Stop()
}

type Component interface {
	Config
	Describable
	Lifecycle
}

type Processor interface {
	Component
	Process(ctx *ProcessContext) Result
}

// Interceptor 拦截器, 用于每个Processor的执行环绕处理
type Interceptor interface {
	Component
	Intercept(invoker Invoker, invocation Invocation) Result
}

type Invocation struct {
	*ProcessContext

	// 以下为执行器特有属性
	CanStop bool // 是否可以终止
	CanSkip bool // 是否可以跳过
}

func (i Invocation) ShouldSkip() bool {
	// 优先判断当前步骤是否应该跳过
	shouldSkip := i.ProcessContext.ShouldSkip()
	if !shouldSkip {
		return false
	}
	// 当前processor是否允许跳过
	return i.CanSkip
}

type Invoker interface {
	Invoke(invocation Invocation) Result
}
