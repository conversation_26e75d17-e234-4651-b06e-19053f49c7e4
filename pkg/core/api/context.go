package api

import (
	"context"
	"dip-agent/pkg/core/cfg"
	"sync"
	"sync/atomic"
)

type ProcessContext struct {
	RequestId string
	TaskId    string
	Carrier   cfg.CommonCfg
	Ctx       context.Context

	// 以下为流程控制属性
	cancel    context.CancelFunc
	pauseSign *atomic.Value // 暂停标记
	lock      *sync.Mutex
	cond      *sync.Cond

	// 运行时执行属性
	ProcessorName         string // 当前执行器的名称
	ProcessorIndex        int    // 当前执行器的索引, 即第几步执行
	ProcessorStepProgress int    // 当前执行器在pipeline的执行时间占比

	// 用于控制从指定processor开始执行
	StartProcessName       string // 开始执行的processor名称
	hasMatchStartProcessor bool   // 是否匹配到了开始执行的processor
}

func NewProcessContext(requestId string, taskId string, carrier cfg.CommonCfg) *ProcessContext {
	ctx, cancel := context.WithCancel(context.Background())
	if carrier == nil {
		carrier = make(map[string]interface{})
	}
	pCtx := &ProcessContext{
		RequestId: requestId,
		TaskId:    taskId,
		Carrier:   carrier,
		Ctx:       ctx,
		cancel:    cancel,
		pauseSign: &atomic.Value{},
		lock:      &sync.Mutex{},
	}
	pCtx.pauseSign.Store(false)
	pCtx.cond = sync.NewCond(pCtx.lock)
	return pCtx
}

// IsStop 是否停止
func (pc *ProcessContext) IsStop() bool {
	return pc.Ctx.Err() != nil
}

// IsDebug 是否调试
func (pc *ProcessContext) IsDebug() bool {
	return pc.Carrier.GetBool("debug")
}

// IsPause 是否暂停
func (pc *ProcessContext) IsPause() bool {
	if pc.pauseSign == nil {
		return false
	}
	ps := pc.pauseSign.Load()
	if ps == nil {
		return false
	}
	return ps.(bool)
}

// Cancel 取消
func (pc *ProcessContext) Cancel() {
	// 可能处于暂停状态, 先恢复
	pc.Resume()
	// 取消
	pc.cancel()
}

// Pause 暂停
func (pc *ProcessContext) Pause() {
	pc.pauseSign.Store(true)
}

// Resume 恢复
func (pc *ProcessContext) Resume() {
	pc.pauseSign.Store(false)
	pc.cond.Broadcast()
}

// Wait 等待
func (pc *ProcessContext) Wait() {
	pc.lock.Lock()
	pc.cond.Wait()
	pc.lock.Unlock()
}

// ShouldSkip 是否跳过
func (pc *ProcessContext) ShouldSkip() bool {
	// 未指定开始执行的processor, 则不需要跳过
	if pc.StartProcessName == "" {
		return false
	}
	// 如果已经匹配到了开始执行的processor, 则后续的processor都不需要跳过
	if pc.hasMatchStartProcessor {
		return false
	}
	// 如果当前processor是需要开始执行的processor, 则不需要跳过. 同时标记已经匹配到了开始执行的processor
	if pc.StartProcessName == pc.ProcessorName {
		pc.hasMatchStartProcessor = true
		return false
	}
	return true
}
