package process

import (
	"dip-agent/pkg/core/api"
	"fmt"
)

type Abstract struct {
	name          string
	IPipelineName string
	TypeName      string
}

func (a *Abstract) Name() string {
	return a.name
}

func (a *Abstract) Category() api.Category {
	return api.PROCESSOR
}

func (a *Abstract) Type() api.Type {
	return api.Type(a.TypeName)
}

func (a *Abstract) String() string {
	return fmt.Sprintf("%s:%s(%s)", a.name, a.TypeName, api.PROCESSOR)
}

func (a *Abstract) Init(context api.Context) error {
	a.name = context.Name()
	return nil
}

func (a *Abstract) Start() error {
	return nil
}

func (a *Abstract) Stop() {

}

func (a *Abstract) PipelineName() string {
	return a.IPipelineName
}
