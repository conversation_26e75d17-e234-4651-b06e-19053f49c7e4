package process

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/jsoniter"
	"fmt"
	"testing"
)

// 测试用的请求和响应结构
type TestRequest struct {
	Name  string `json:"name" validate:"required"`
	Count int    `json:"count" validate:"min=1"`
}

type TestResponse struct {
	Message string `json:"message"`
	Result  int    `json:"result"`
}

// 测试实现
type TestProcessorImpl struct{}

func (impl *TestProcessorImpl) NewRequest() TestRequest {
	return TestRequest{}
}

func (impl *TestProcessorImpl) DoProcess(requestId string, request TestRequest) (TestResponse, error) {
	if request.Name == "error" {
		return TestResponse{}, fmt.Errorf("test error")
	}

	return TestResponse{
		Message: fmt.Sprintf("Hello %s", request.Name),
		Result:  request.Count * 2,
	}, nil
}

// 简化版测试实现
type TestSimpleImpl struct{}

func (impl *TestSimpleImpl) DoProcess(requestId string, request TestRequest) (TestResponse, error) {
	return TestResponse{
		Message: fmt.Sprintf("Simple: %s", request.Name),
		Result:  request.Count + 10,
	}, nil
}

// 测试函数
func testProcessFunc(requestId string, request TestRequest) (TestResponse, error) {
	return TestResponse{
		Message: fmt.Sprintf("Func: %s", request.Name),
		Result:  request.Count * 3,
	}, nil
}

func TestGenericProcessor(t *testing.T) {
	// 初始化
	log.InitDefaultLogger()
	json.SetDefaultEngine(jsoniter.Name)

	// 测试GenericProcessor
	t.Run("GenericProcessor", func(t *testing.T) {
		impl := &TestProcessorImpl{}
		processor := NewGenericProcessor[TestRequest, TestResponse](
			"test-pipeline",
			"test-generic",
			impl,
		)

		// 准备测试数据
		request := &TestRequest{
			Name:  "Alice",
			Count: 5,
		}

		carrier, err := cfg.NewCommonCfgWithJson(request)
		if err != nil {
			t.Fatalf("create carrier failed: %v", err)
		}

		ctx := &api.ProcessContext{
			RequestId: "test-request-123",
			Carrier:   carrier,
		}

		// 执行处理
		result := processor.Process(ctx)

		// 验证结果
		if !result.Status().IsSuccess() {
			t.Fatalf("process failed: %v", result.Error())
		}

		response := result.Data().(TestResponse)
		if response.Message != "Hello Alice" {
			t.Errorf("expected message 'Hello Alice', got '%s'", response.Message)
		}
		if response.Result != 10 {
			t.Errorf("expected result 10, got %d", response.Result)
		}
	})

	// 测试SimpleProcessor
	t.Run("SimpleProcessor", func(t *testing.T) {
		impl := &TestSimpleImpl{}
		processor := NewSimpleProcessor[TestRequest, TestResponse](
			"test-pipeline",
			"test-simple",
			impl,
		)

		request := &TestRequest{
			Name:  "Bob",
			Count: 3,
		}

		carrier, err := cfg.NewCommonCfgWithJson(request)
		if err != nil {
			t.Fatalf("create carrier failed: %v", err)
		}

		ctx := &api.ProcessContext{
			RequestId: "test-request-456",
			Carrier:   carrier,
		}

		result := processor.Process(ctx)

		if !result.Status().IsSuccess() {
			t.Fatalf("process failed: %v", result.Error())
		}

		response := result.Data().(TestResponse)
		if response.Message != "Simple: Bob" {
			t.Errorf("expected message 'Simple: Bob', got '%s'", response.Message)
		}
		if response.Result != 13 {
			t.Errorf("expected result 13, got %d", response.Result)
		}
	})

	// 测试FuncProcessor
	t.Run("FuncProcessor", func(t *testing.T) {
		processor := NewFuncProcessor[TestRequest, TestResponse](
			"test-pipeline",
			"test-func",
			testProcessFunc,
		)

		request := &TestRequest{
			Name:  "Charlie",
			Count: 4,
		}

		carrier, err := cfg.NewCommonCfgWithJson(request)
		if err != nil {
			t.Fatalf("create carrier failed: %v", err)
		}

		ctx := &api.ProcessContext{
			RequestId: "test-request-789",
			Carrier:   carrier,
		}

		result := processor.Process(ctx)

		if !result.Status().IsSuccess() {
			t.Fatalf("process failed: %v", result.Error())
		}

		response := result.Data().(TestResponse)
		if response.Message != "Func: Charlie" {
			t.Errorf("expected message 'Func: Charlie', got '%s'", response.Message)
		}
		if response.Result != 12 {
			t.Errorf("expected result 12, got %d", response.Result)
		}
	})

	// 测试错误处理
	t.Run("ErrorHandling", func(t *testing.T) {
		impl := &TestProcessorImpl{}
		processor := NewGenericProcessor[TestRequest, TestResponse](
			"test-pipeline",
			"test-error",
			impl,
		)

		// 测试参数错误
		invalidCarrier := cfg.NewCommonCfg()
		invalidCarrier.Put("invalid", "data")

		ctx := &api.ProcessContext{
			RequestId: "test-error-1",
			Carrier:   invalidCarrier,
		}

		result := processor.Process(ctx)
		if result.Status().IsSuccess() {
			t.Error("expected param error, but got success")
		}

		// 测试业务逻辑错误
		errorRequest := &TestRequest{
			Name:  "error",
			Count: 1,
		}

		carrier, _ := cfg.NewCommonCfgWithJson(errorRequest)
		ctx.Carrier = carrier

		result = processor.Process(ctx)
		if result.Status().IsSuccess() {
			t.Error("expected business error, but got success")
		}
	})
}

func TestProcessorInterface(t *testing.T) {
	// 验证processor实现了正确的接口
	var processor api.Processor

	impl := &TestProcessorImpl{}
	processor = NewGenericProcessor[TestRequest, TestResponse](
		"test",
		"test",
		impl,
	)

	if processor.Category() != api.PROCESSOR {
		t.Errorf("expected category PROCESSOR, got %s", processor.Category())
	}

	if processor.Type() != "test" {
		t.Errorf("expected type 'test', got %s", processor.Type())
	}
}
