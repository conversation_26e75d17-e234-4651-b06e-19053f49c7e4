package process

import (
	"dip-agent/pkg/core/cfg"
	"time"
)

type Config struct {
	Name         string        `yaml:"name,omitempty" validate:"required"`
	Type         string        `yaml:"type,omitempty" validate:"required"`
	Timeout      time.Duration `yaml:"timeout,omitempty" default:"0s"`
	Order        int           `yaml:"order,omitempty" default:"900"`
	Enable       bool          `yaml:"enable,omitempty" default:"true"`
	StepProgress int           `yaml:"stepProgress,omitempty" validate:"gte=0,lte=100"` // 执行步骤进度: 0~100, 表示执行进度百分比. 预估值
	CanStop      bool          `yaml:"canStop,omitempty" default:"true"`                // 是否可以终止. 有些步骤终止后会使得服务处于异常状态(例如服务停止)
	CanSkip      *bool         `yaml:"canSkip,omitempty" default:"true"`                // 是否可以跳过. 有些步骤跳过后会使得更新处于异常状态(例如内存校验)
	Properties   cfg.CommonCfg `yaml:",inline"`
}
