package process

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
)

// ProcessorInterface 定义processor需要实现的接口
type ProcessorInterface[TReq any, TResp any] interface {
	// DoProcess 实际的业务处理逻辑，只需要实现这一个方法
	DoProcess(requestId string, request TReq) (TResp, error)

	// NewRequest 创建请求对象的工厂方法
	NewRequest() TReq
}

// GenericProcessor 泛型processor，自动处理carrier转换和错误处理
// note: 优先使用SimpleProcessor或者FuncProcessor
type GenericProcessor[TReq any, TResp any] struct {
	Abstract
	impl ProcessorInterface[TReq, TResp]
}

// NewGenericProcessor 创建泛型processor
func NewGenericProcessor[TReq any, TResp any](
	pipelineName string,
	typeName string,
	impl ProcessorInterface[TReq, TResp],
) *GenericProcessor[TReq, TResp] {
	return &GenericProcessor[TReq, TResp]{
		Abstract: Abstract{
			IPipelineName: pipelineName,
			TypeName:      typeName,
		},
		impl: impl,
	}
}

// Config 实现api.Component接口
func (p *GenericProcessor[TReq, TResp]) Config() interface{} {
	return nil
}

// Process 实现api.Processor接口，自动处理carrier转换
func (p *GenericProcessor[TReq, TResp]) Process(ctx *api.ProcessContext) api.Result {
	// 创建请求对象
	request := p.impl.NewRequest()

	// 自动从carrier转换为具体的请求类型
	err := ctx.Carrier.UnpackToWithJson(&request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[TResp](err)
	}

	// 记录请求日志
	if requestContent, err := json.MarshalToString(request); err == nil {
		log.Info("[%s]%s request: %s", ctx.RequestId, p.TypeName, requestContent)
	}

	// 调用具体的业务处理逻辑
	response, err := p.impl.DoProcess(ctx.RequestId, request)
	if err != nil {
		return api.SystemFail[TResp](err)
	}

	// 记录响应日志
	log.Info("[%s]%s completed successfully", ctx.RequestId, p.TypeName)

	// 响应消息
	responseMessage, _ := json.MarshalToString(response)
	return api.OfSuccessWithMessage(response, responseMessage)
}

// SimpleProcessorInterface 更简单的processor接口，只需要实现DoProcess
type SimpleProcessorInterface[TReq any, TResp any] interface {
	DoProcess(requestId string, request TReq) (TResp, error)
}

// SimpleProcessor 简化版processor，自动创建请求对象
//
// 重要约束：TReq 必须为非指针类型（如 MyRequest 而不是 *MyRequest）
// 原因：内部会自动创建 var request TReq，然后调用 UnpackToWithJson(&request)
// 如果 TReq 是指针类型，会导致 &(*MyRequest) 的错误用法
//
// 正确示例：
//
//	type MyRequest struct { ... }
//	type MyResponse struct { ... }
//	func (impl *MyImpl) DoProcess(requestId string, request MyRequest) (MyResponse, error) { ... }
//
// 错误示例：
//
//	type MyRequest struct { ... }
//	type MyResponse struct { ... }
//	func (impl *MyImpl) DoProcess(requestId string, request *MyRequest) (*MyResponse, error) { ... }
type SimpleProcessor[TReq any, TResp any] struct {
	Abstract
	impl SimpleProcessorInterface[TReq, TResp]
}

// NewSimpleProcessor 创建简化版processor
func NewSimpleProcessor[TReq any, TResp any](
	pipelineName string,
	typeName string,
	impl SimpleProcessorInterface[TReq, TResp],
) *SimpleProcessor[TReq, TResp] {
	return &SimpleProcessor[TReq, TResp]{
		Abstract: Abstract{
			IPipelineName: pipelineName,
			TypeName:      typeName,
		},
		impl: impl,
	}
}

// Config 实现api.Component接口
func (p *SimpleProcessor[TReq, TResp]) Config() interface{} {
	return nil
}

// Process 实现api.Processor接口
func (p *SimpleProcessor[TReq, TResp]) Process(ctx *api.ProcessContext) api.Result {
	// 使用反射创建请求对象
	var request TReq

	// 自动从carrier转换为具体的请求类型
	err := ctx.Carrier.UnpackToWithJson(&request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[TResp](err)
	}

	// 记录请求日志
	if requestContent, err := json.MarshalToString(request); err == nil {
		log.Info("[%s]%s request: %s", ctx.RequestId, p.TypeName, requestContent)
	}

	// 调用具体的业务处理逻辑
	response, err := p.impl.DoProcess(ctx.RequestId, request)
	if err != nil {
		return api.SystemFail[TResp](err)
	}

	log.Info("[%s]%s completed successfully", ctx.RequestId, p.TypeName)

	// 响应消息
	responseMessage, _ := json.MarshalToString(response)
	return api.OfSuccessWithMessage(response, responseMessage)
}

// FuncProcessor 基于函数的processor，最简化的实现
//
// 重要约束：TReq 必须为非指针类型（如 MyRequest 而不是 *MyRequest）
// 原因：内部会自动创建 var request TReq，然后调用 UnpackToWithJson(&request)
// 如果 TReq 是指针类型，会导致 &(*MyRequest) 的错误用法
//
// 正确示例：
//
//	func processFunc(requestId string, request MyRequest) (MyResponse, error)
//
// 错误示例：
//
//	func processFunc(requestId string, request *MyRequest) (*MyResponse, error)
type FuncProcessor[TReq any, TResp any] struct {
	Abstract
	processFunc func(requestId string, request TReq) (TResp, error)
}

// NewFuncProcessor 创建基于函数的processor
//
// 重要约束：TReq 必须为非指针类型
// - 正确：NewFuncProcessor[MyRequest, MyResponse](...)
// - 错误：NewFuncProcessor[*MyRequest, *MyResponse](...)
//
// 原因：内部实现使用 var request TReq; UnpackToWithJson(&request)
// 如果 TReq 是指针，会导致类型错误
func NewFuncProcessor[TReq any, TResp any](
	pipelineName string,
	typeName string,
	processFunc func(requestId string, request TReq) (TResp, error),
) *FuncProcessor[TReq, TResp] {
	return &FuncProcessor[TReq, TResp]{
		Abstract: Abstract{
			IPipelineName: pipelineName,
			TypeName:      typeName,
		},
		processFunc: processFunc,
	}
}

// Config 实现api.Component接口
func (p *FuncProcessor[TReq, TResp]) Config() interface{} {
	return nil
}

// Process 实现api.Processor接口
func (p *FuncProcessor[TReq, TResp]) Process(ctx *api.ProcessContext) api.Result {
	var request TReq

	err := ctx.Carrier.UnpackToWithJson(&request)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[TResp](err)
	}

	log.Info("[%s]%s processing...", ctx.RequestId, p.TypeName)

	// 记录请求日志
	if requestContent, err := json.MarshalToString(request); err == nil {
		log.Info("[%s]%s request: %s", ctx.RequestId, p.TypeName, requestContent)
	}

	response, err := p.processFunc(ctx.RequestId, request)
	if err != nil {
		return api.SystemFail[TResp](err)
	}

	log.Info("[%s]%s completed successfully", ctx.RequestId, p.TypeName)

	// 响应消息
	responseMessage, _ := json.MarshalToString(response)
	return api.OfSuccessWithMessage(response, responseMessage)
}
