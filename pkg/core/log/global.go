package log

import (
	"dip-agent/pkg/core/log/spi"
	"flag"
	"fmt"
	"github.com/rs/zerolog"
	"gopkg.in/natefinch/lumberjack.v2"
	"io"
	"os"
	"path"
	"path/filepath"
	"time"
)

var (
	defaultLogger *Logger
	AfterError    spi.AfterError
	gLoggerConfig = &LoggerConfig{}
)

func init() {
	SetFlag(flag.CommandLine)
}

func SetFlag(f *flag.FlagSet) {
	f.StringVar(&gLoggerConfig.Level, "log.level", "info", "Global log output level")
	f.Bool<PERSON>ar(&gLoggerConfig.JsonFormat, "log.jsonFormat", false, "Parses the JSON log format")
	f.Bo<PERSON>ar(&gLoggerConfig.EnableStdout, "log.enableStdout", true, "EnableStdout enable the log print to stdout")
	f.<PERSON>(&gLoggerConfig.EnableFile, "log.enableFile", false, "EnableFile makes the framework log to a file")
	f.StringVar(&gLoggerConfig.Directory, "log.dir", "/logs/agent", "Directory to log to to when log.enableFile is enabled")
	f.StringVar(&gLoggerConfig.Filename, "log.filename", "dip-agent.log", "Filename is the name of the logfile which will be placed inside the directory")
	f.IntVar(&gLoggerConfig.MaxSize, "log.maxSize", 128, "Max size in MB of the logfile before it's rolled")
	f.IntVar(&gLoggerConfig.MaxBackups, "log.maxBackups", 4, "Max number of rolled files to keep")
	f.IntVar(&gLoggerConfig.MaxAge, "log.maxAge", 7, "Max age in days to keep a logfile")
	f.StringVar(&gLoggerConfig.TimeFormat, "log.timeFormat", "2006-01-02 15:04:05", "TimeFormat log time format")
	f.IntVar(&gLoggerConfig.CallerSkipCount, "log.callerSkipCount", 4, "CallerSkipCount is the number of stack frames to skip to find the caller")
	f.BoolVar(&gLoggerConfig.NoColor, "log.noColor", false, "NoColor disables the colorized output")
}

type LoggerConfig struct {
	Level           string `yaml:"level,omitempty"`
	JsonFormat      bool   `yaml:"jsonFormat,omitempty"`
	EnableStdout    bool   `yaml:"enableStdout,omitempty"`
	EnableFile      bool   `yaml:"enableFile,omitempty"`
	Directory       string `yaml:"directory,omitempty"`
	Filename        string `yaml:"filename,omitempty"`
	MaxSize         int    `yaml:"maxSize,omitempty"`
	MaxBackups      int    `yaml:"maxBackups,omitempty"`
	MaxAge          int    `yaml:"maxAge,omitempty"`
	TimeFormat      string `yaml:"timeFormat,omitempty"`
	CallerSkipCount int    `yaml:"callerSkipCount,omitempty"`
	NoColor         bool   `yaml:"noColor,omitempty"`
}

type Logger struct {
	l *zerolog.Logger
}

func InitDefaultLogger() {
	logger := NewLogger(gLoggerConfig)
	defaultLogger = logger
}

func FilePath() string {
	return filepath.Join(gLoggerConfig.Directory, gLoggerConfig.Filename)
}

func Dir() string {
	return gLoggerConfig.Directory
}

func NewLogger(config *LoggerConfig) *Logger {
	var writers []io.Writer

	if config.EnableStdout {
		writers = append(writers, os.Stderr)
	}

	if config.EnableFile {
		writers = append(writers, newRollingFile(config.Directory, config.Filename, config.MaxBackups, config.MaxSize, config.MaxAge))
	}

	if !config.JsonFormat {
		for i, w := range writers {
			writers[i] = zerolog.ConsoleWriter{
				Out:        w,
				NoColor:    config.NoColor,
				TimeFormat: config.TimeFormat,
			}
		}
	}

	mw := io.MultiWriter(writers...)

	zerolog.TimeFieldFormat = config.TimeFormat
	zerolog.CallerSkipFrameCount = config.CallerSkipCount
	level, err := zerolog.ParseLevel(config.Level)
	if err != nil {
		panic("set log level error, choose trace/debug/info/warn/error/fatal/panic")
	}
	multi := zerolog.MultiLevelWriter(mw)
	logger := zerolog.New(multi).Level(level).With().Timestamp().Caller().Logger()
	return &Logger{
		l: &logger,
	}
}

func newRollingFile(directory string, filename string, maxBackups int, maxSize int, maxAge int) io.Writer {
	if err := os.MkdirAll(directory, 0744); err != nil {
		panic(fmt.Sprintf("can't create log directory %s", directory))
	}

	return &lumberjack.Logger{
		Filename:   path.Join(directory, filename),
		MaxBackups: maxBackups, // files
		MaxSize:    maxSize,    // megabytes
		MaxAge:     maxAge,     // days
		Compress:   true,
	}
}

func (logger *Logger) Debug(format string, a ...interface{}) {
	if a == nil {
		logger.l.Debug().Msg(format)
	} else {
		logger.l.Debug().Msgf(format, a...)
	}
}

func (logger *Logger) Info(format string, a ...interface{}) {
	if a == nil {
		logger.l.Info().Msg(format)
	} else {
		logger.l.Info().Msgf(format, a...)
	}
}

func (logger *Logger) Warn(format string, a ...interface{}) {
	if a == nil {
		logger.l.Warn().Msg(format)
	} else {
		logger.l.Warn().Msgf(format, a...)
	}
}

func (logger *Logger) Error(format string, a ...interface{}) {
	if a == nil {
		logger.l.Error().Msg(format)
	} else {
		logger.l.Error().Msgf(format, a...)
	}
}

func (logger *Logger) Panic(format string, a ...interface{}) {
	if a == nil {
		logger.l.Panic().Msg(format)
	} else {
		logger.l.Panic().Msgf(format, a...)
	}
}

func (logger *Logger) Fatal(format string, a ...interface{}) {
	if a == nil {
		logger.l.Fatal().Msg(format)
	} else {
		logger.l.Fatal().Msgf(format, a...)
	}
}

func (logger *Logger) SubLogger(name string) *Logger {
	subLogger := logger.l.With().Str("component", name).CallerWithSkipFrameCount(gLoggerConfig.CallerSkipCount - 1).Logger()
	return &Logger{
		l: &subLogger,
	}
}

// Sample returns a logger with a sampler.
// max: the maximum number of events to be logged per period
func (logger *Logger) Sample(max uint32, period time.Duration) *Logger {
	s := logger.l.Sample(&zerolog.BurstSampler{
		Burst:  max,
		Period: period,
	})
	return &Logger{
		l: &s,
	}
}

func (logger *Logger) GetLevel() string {
	return logger.l.GetLevel().String()
}

func (logger *Logger) RawJson(key string, raw []byte, format string, a ...interface{}) {
	if a == nil {
		logger.l.Log().RawJSON(key, raw).Msg(format)
	} else {
		logger.l.Log().RawJSON(key, raw).Msgf(format, a...)
	}
}

func IsDebugLevel() bool {
	return defaultLogger.GetLevel() == zerolog.DebugLevel.String()
}

func Debug(format string, a ...interface{}) {
	defaultLogger.Debug(format, a...)
}

func Info(format string, a ...interface{}) {
	defaultLogger.Info(format, a...)
}

func Warn(format string, a ...interface{}) {
	defaultLogger.Warn(format, a...)
}

func Error(format string, a ...interface{}) {
	defer afterErrorOpt(format, a...)
	defaultLogger.Error(format, a...)
}

func Panic(format string, a ...interface{}) {
	defer afterErrorOpt(format, a...)
	defaultLogger.Panic(format, a...)
}

func Fatal(format string, a ...interface{}) {
	defaultLogger.Fatal(format, a...)
}

func SubLogger(name string) *Logger {
	return defaultLogger.SubLogger(name)
}

func afterErrorOpt(format string, a ...interface{}) {
	if AfterError == nil {
		return
	}
	var msg string
	if a == nil {
		msg = format
	} else {
		msg = fmt.Sprintf(format, a...)
	}
	AfterError(msg)
}

func Level() zerolog.Level {
	return defaultLogger.l.GetLevel()
}
