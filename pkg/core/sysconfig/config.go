package sysconfig

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/intercept"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/persistence"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/task"
	"time"
)

type Config struct {
	Agent        Agent                      `yaml:"agent"`
	Tasks        []task.Config              `yaml:"tasks" validate:"dive,required"`
	EngineEnvKey engine.ServiceEnvConfigKey `yaml:"engineEnvKey" validate:"dive,required"`
	Persistence  persistence.Config         `yaml:"persistence" validate:"dive,required"`
}

type Agent struct {
	Http       Http   `yaml:"http" validate:"dive"`
	JSONEngine string `yaml:"jsonEngine,omitempty" default:"sonic" validate:"oneof=jsoniter std sonic"`
}

type Http struct {
	Enabled bool   `yaml:"enabled" default:"true"`
	Host    string `yaml:"host" default:"0.0.0.0"`
	Port    int    `yaml:"port" default:"9188"`
}

func (c *Config) SetDefaults() {
	if api.CurrentEnv.IsTest() {
		if c.Persistence.Properties == nil {
			c.Persistence.Properties = map[string]interface{}{
				"filePath": "/tmp/task_status.json",
			}
		}
	}

	canSkip := false

	if len(c.Tasks) == 0 {
		c.Tasks = []task.Config{
			// 索引构建pipeline
			{
				Name: "index-build",
				Type: api.BUILD,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "build-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "build-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "build-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "build-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "build-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "build-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: dump",
							Type:         "dump",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      true,
						},
						{
							Name:         "step2: schema",
							Type:         "schema",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step3: build-index",
							Type:         "build-index",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      true,
						},
						{
							Name:         "step4: upload-index",
							Type:         "upload-index",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      true,
						},
					},
				},
			},

			// 索引热更新pipeline
			{
				Name: "hot-reload",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "hot-reload-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "hot-reload-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "hot-reload-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "hot-reload-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "hot-reload-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "hot-reload-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-index",
							Type:         "check-index",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: download-index",
							Type:         "download-index",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      true,
						},
						{
							Name:         "step3: hot-reload",
							Type:         "hot-reload",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      true,
						},
						{
							Name:         "step4: wait-inc",
							Type:         "wait-inc",
							Enable:       true,
							Order:        900,
							StepProgress: 20,
							CanStop:      false,
						},
						{
							Name:         "step5: commit",
							Type:         "commit",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
					},
				},
			},

			// 索引重启更新pipeline
			{
				Name: "reboot-reload",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "reboot-reload-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "reboot-reload-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "reboot-reload-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "reboot-reload-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "reboot-reload-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "reboot-reload-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-index",
							Type:         "check-index",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: offline",
							Type:         "offline",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      true,
						},
						{
							Name:         "step3: qps",
							Type:         "qps",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step4: stop-server",
							Type:         "stop-server",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step5: remove-index-db",
							Type:         "rmdb",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step6: download-index",
							Type:         "download-index",
							Enable:       true,
							Order:        900,
							StepProgress: 20,
							CanStop:      false,
						},
						{
							Name:         "step7: start-server",
							Type:         "start-server",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      false,
						},
						{
							Name:         "step8: wait-inc",
							Type:         "wait-inc",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step9: online",
							Type:         "online",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
					},
				},
			},

			// [推荐Dgraph]索引重启更新pipeline
			{
				Name: "dgraph-reboot-reload",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "dgraph-reboot-reload-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "dgraph-reboot-reload-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "dgraph-reboot-reload-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "dgraph-reboot-reload-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "dgraph-reboot-reload-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "dgraph-reboot-reload-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-index",
							Type:         "check-index",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: offline",
							Type:         "offline",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step3: qps",
							Type:         "qps",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step4: unload-index",
							Type:         "unload-index",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step5: download-index",
							Type:         "download-index",
							Enable:       true,
							Order:        900,
							StepProgress: 20,
							CanStop:      false,
						},
						{
							Name:         "step6: hot-reload",
							Type:         "hot-reload",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step7: wait-inc[dgraph]",
							Type:         "wait-inc",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
							Properties: map[string]interface{}{
								"isRealReboot": false,
							},
						},
						{
							Name:         "step8: commit",
							Type:         "commit",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step9: online",
							Type:         "online",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
					},
				},
			},

			// 索引批量重启更新pipeline
			{
				Name: "batch-reboot-reload",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "batch-reboot-reload-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "batch-reboot-reload-reporter",
							Type:   "batch-report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "batch-reboot-reload-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "batch-reboot-reload-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "batch-reboot-reload-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step0: begin-index-update",
							Type:         "begin-index-update",
							Enable:       true,
							Order:        900,
							StepProgress: 1,
							CanStop:      true,
							CanSkip:      &canSkip,
						},
						{
							Name:         "step1: batch-check-index",
							Type:         "batch-check-index",
							Enable:       true,
							Order:        900,
							StepProgress: 9,
							CanStop:      true,
							CanSkip:      &canSkip,
						},
						{
							Name:         "step2: offline",
							Type:         "offline",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      true,
						},
						{
							Name:         "step3: qps",
							Type:         "qps",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step4: stop-server",
							Type:         "stop-server",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step5: remove-index-db",
							Type:         "batch-rmdb",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step6: download-index",
							Type:         "batch-download-index",
							Enable:       true,
							Order:        900,
							StepProgress: 20,
							CanStop:      false,
						},
						{
							Name:         "step7: start-server",
							Type:         "start-server",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      false,
						},
						{
							Name:         "step8: wait-inc",
							Type:         "wait-inc",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step9: online",
							Type:         "online",
							Enable:       true,
							Order:        900,
							StepProgress: 4,
							CanStop:      false,
						},
						{
							Name:         "step10: end-index_update",
							Type:         "end-index-update",
							Enable:       true,
							Order:        900,
							StepProgress: 1,
							CanStop:      true,
						},
					},
				},
			},

			// 索引删除pipeline(重启删除)
			{
				Name: "delete-index",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "delete-index-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "delete-index-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "delete-index-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "delete-index-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "delete-index-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "delete-index-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-index-qps",
							Type:         "check-index-qps",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: offline",
							Type:         "offline",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step3: qps",
							Type:         "qps",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step4: stop-server",
							Type:         "stop-server",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step5: remove-index-db",
							Type:         "rmdb",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step6: start-server",
							Type:         "start-server",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      false,
						},
						{
							Name:         "step7: wait-inc",
							Type:         "wait-inc",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step8: online",
							Type:         "online",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
					},
				},
			},

			// 索引删除pipeline(热更新删除)
			{
				Name: "hotreload-delete-index",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "hotreload-delete-index-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "hotreload-delete-index-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "hotreload-delete-index-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "hotreload-delete-index-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "hotreload-delete-index-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "hotreload-delete-index-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-index-qps",
							Type:         "check-index-qps",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: delete-index",
							Type:         "delete-index",
							Enable:       true,
							Order:        900,
							StepProgress: 90,
							CanStop:      false,
						},
					},
				},
			},

			// 批量索引删除pipeline(热更新删除)
			{
				Name: "batch-hotreload-delete-index",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "batch-hotreload-delete-index-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "batch-hotreload-delete-index-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "batch-hotreload-delete-index-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "batch-hotreload-delete-index-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "batch-hotreload-delete-index-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "batch-hotreload-delete-index-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-index-qps",
							Type:         "batch-check-index-qps",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: delete-index",
							Type:         "batch-delete-index",
							Enable:       true,
							Order:        900,
							StepProgress: 90,
							CanStop:      false,
						},
					},
				},
			},

			// 模型重启更新pipeline
			{
				Name: "model-reboot-reload",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "model-reboot-reload-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "model-reboot-reload-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "model-reboot-reload-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "model-reboot-reload-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "model-reboot-reload-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "model-reboot-reload-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-model",
							Type:         "check-model",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: offline",
							Type:         "offline",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      true,
						},
						{
							Name:         "step3: qps",
							Type:         "qps",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step4: stop-server",
							Type:         "stop-server",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step5: remove-model-db",
							Type:         "rmdb-model",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step6: download-model",
							Type:         "download-model",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step7: download-model-config",
							Type:         "download-model-config",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step8: start-server",
							Type:         "start-server",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      false,
						},
						{
							Name:         "step9: wait-inc",
							Type:         "wait-inc",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step10: online",
							Type:         "online",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
					},
				},
			},

			// 模型热更新pipeline
			{
				Name: "model-hot-reload",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "model-hot-reload-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "model-hot-reload-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "model-hot-reload-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "model-hot-reload-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "model-hot-reload-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "model-hot-reload-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: check-model",
							Type:         "check-model",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      true,
						},
						{
							Name:         "step2: download-model",
							Type:         "download-model",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      true,
						},
						{
							Name:         "step3: download-model-config",
							Type:         "download-model-config",
							Enable:       true,
							Order:        900,
							StepProgress: 20,
							CanStop:      true,
						},
						{
							Name:         "step4: hot-reload",
							Type:         "model-hot-reload",
							Enable:       true,
							Order:        900,
							StepProgress: 30,
							CanStop:      true,
						},
						{
							Name:         "step5: commit",
							Type:         "commit-model",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
					},
				},
			},

			// 引擎服务重启pipeline
			{
				Name: "engine-reboot",
				Type: api.UPDATE,
				Pipeline: pipeline.Config{
					Timeout: 600 * time.Second,
					Interceptors: []*intercept.Config{
						{
							Name:   "engine-reboot-skip",
							Type:   "skip",
							Enable: true,
							Order:  90,
						},
						{
							Name:   "engine-reboot-reporter",
							Type:   "report",
							Enable: true,
							Order:  100,
						},
						{
							Name:   "engine-reboot-chaos",
							Type:   "chaos",
							Enable: true,
							Order:  110,
						},
						{
							Name:   "engine-reboot-exception",
							Type:   "exception",
							Enable: true,
							Order:  200,
						},
						{
							Name:   "engine-reboot-stop",
							Type:   "stop",
							Enable: true,
							Order:  300,
						},
						{
							Name:   "engine-reboot-pause",
							Type:   "pause",
							Enable: true,
							Order:  400,
						},
					},
					Processors: []*process.Config{
						{
							Name:         "step1: offline",
							Type:         "offline",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      true,
						},
						{
							Name:         "step2: qps",
							Type:         "qps",
							Enable:       true,
							Order:        900,
							StepProgress: 5,
							CanStop:      false,
						},
						{
							Name:         "step3: stop-server",
							Type:         "stop-server",
							Enable:       true,
							Order:        900,
							StepProgress: 20,
							CanStop:      false,
						},
						{
							Name:         "step4: start-server",
							Type:         "start-server",
							Enable:       true,
							Order:        900,
							StepProgress: 40,
							CanStop:      false,
						},
						{
							Name:         "step5: wait-inc",
							Type:         "wait-inc",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step6: sleep",
							Type:         "sleep",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
						{
							Name:         "step7: online",
							Type:         "online",
							Enable:       true,
							Order:        900,
							StepProgress: 10,
							CanStop:      false,
						},
					},
				},
			},

			// 本地测试pipeline
			//{
			//	Name: "test",
			//	Type: api.BUILD,
			//	Pipeline: pipeline.Config{
			//		Timeout:      600 * time.Second,
			//		Interceptors: []*intercept.Config{
			//		},
			//		Processors: []*process.Config{
			//			{
			//				Name:         "step1: echo",
			//				Type:         "echo",
			//				Enable:       true,
			//				Order:        900,
			//				StepProgress: 30,
			//				Properties: map[string]interface{}{
			//					"echo": "hello world1",
			//				},
			//			},
			//			{
			//				Name:         "step2: echo",
			//				Type:         "echo",
			//				Enable:       true,
			//				Order:        900,
			//				StepProgress: 30,
			//				Properties: map[string]interface{}{
			//					"echo": "hello world2",
			//				},
			//			},
			//			{
			//				Name:         "step3: echo",
			//				Type:         "echo",
			//				Enable:       true,
			//				Order:        900,
			//				StepProgress: 40,
			//				Properties: map[string]interface{}{
			//					"echo": "hello world3",
			//				},
			//			},
			//		},
			//	},
			//},
		}
	}
}
