package report

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/retry"
	"time"
)

const (
	StepStatusNotStart StepStatus = 0 // 未开始
	StepStatusRunning  StepStatus = 1 // 执行中
	StepStatusSuccess  StepStatus = 2 // 执行成功
	StepStatusFailed   StepStatus = 3 // 执行失败
	StepStatusStop     StepStatus = 4 // 执行终止
	StepStatusPause    StepStatus = 5 // 执行暂停
	StetStatusResume   StepStatus = 6 // 执行恢复
)

var (
	// stepStatusToChineseMap 执行步骤状态中文映射
	stepStatusToCnMap = map[StepStatus]string{
		StepStatusNotStart: "未开始",
		StepStatusRunning:  "执行中",
		StepStatusSuccess:  "执行成功",
		StepStatusFailed:   "执行失败",
		StepStatusStop:     "执行终止",
		StepStatusPause:    "执行暂停",
		StetStatusResume:   "执行恢复",
	}

	// defaultReportStatusUrl 默认上报状态的url
	defaultReportStatusPath = "/engine/task/status/report"
	// 批量上报状态的url
	batchReportStatusPath = "/engine/task/status/batchReport"

	// 目前平台的响应code为0表示成功
	hc = util.NewHttpClientWithSuccessCode(30*time.Second, []string{"0"})
)

type StepStatus int

type EngineTaskStatusReportReq struct {
	TaskId        string     `json:"taskId" validate:"required"`                // 任务id: 子任务id
	StepName      string     `json:"stepName" validate:"required"`              // 执行步骤名称
	StepStartTime string     `json:"stepStartTime" validate:"datetime"`         // 执行步骤开始时间: 格式为yyyy-MM-dd HH:mm:ss
	StepEndTime   string     `json:"stepEndTime,omitempty" validate:"datetime"` // 执行步骤结束时间: 格式为yyyy-MM-dd HH:mm:ss
	StepStatus    StepStatus `json:"stepStatus" validate:"required"`            // 执行步骤状态: 0-未开始, 1-执行中, 2-执行成功, 3-执行失败
	StepCost      int64      `json:"stepCost" validate:"min=0"`                 // 执行步骤耗时: 单位为毫秒
	StepResult    string     `json:"stepResult" validate:"max=2048"`            // 执行结果: 例如执行成功后的数据处理数量; 执行失败的错误原因
	StepProgress  int        `json:"stepProgress" validate:"min=0,max=100"`     // 执行步骤进度: 0~100, 表示执行进度百分比
	TotalProgress int        `json:"totalProgress" validate:"min=0,max=100"`    // 整体执行进度: 0~100, 表示整体执行进度百分比
	IndexName     string     `json:"indexName,omitempty"`                       // 索引名称
	IndexVersion  string     `json:"indexVersion,omitempty"`                    // 索引版本

	DumpCount int64 `json:"dumpCount,omitempty"` // dump数据量
	IndexSize int64 `json:"indexSize,omitempty"` // 构建的索引大小: 单位为byte
}

func (r EngineTaskStatusReportReq) Send(requestId, reportUrl string) {
	if r.StepResult == "" {
		r.StepResult = stepStatusToCnMap[r.StepStatus]
	}
	SendStatus(requestId, reportUrl, r)
}

func SendStatus(requestId, reportUrl string, r EngineTaskStatusReportReq) {
	if reportUrl == "" {
		reportUrl = api.BrainDomain + defaultReportStatusPath
	}
	// TODO 如果任务可以并发执行,需要将hc挪到函数内部
	hc.SetClue(requestId)
	// 重试上报
	err := retry.DoRetry(func() error {
		_, err := hc.PostStringWithResponse(reportUrl, r, nil)
		return err
	})
	if err != nil {
		log.Error("report failed, err: %s", err)
	}
}

func SendBatchStatus(requestId string, reqs []*EngineTaskStatusReportReq) {
	for _, r := range reqs {
		if r.StepResult == "" {
			r.StepResult = stepStatusToCnMap[r.StepStatus]
		}
	}
	reportUrl := api.BrainDomain + batchReportStatusPath
	hc.SetClue(requestId)

	// 重试上报
	err := retry.DoRetry(func() error {
		_, err := hc.PostStringWithResponse(reportUrl, reqs, nil)
		return err
	})
	if err != nil {
		log.Error("report failed, err: %s", err)
	}
}
