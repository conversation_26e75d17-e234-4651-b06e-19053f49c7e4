package exec

import (
	"bytes"
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/json/sonic"
	"github.com/stretchr/testify/mock"
	"net/http"
	"testing"
)

func TestTaskManager_PauseTask(t *testing.T) {
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	requestId := "aaa888"

	// Initialize TaskManager
	tm := NewTaskManager()
	notifier := &MockNotifier{}
	tm.Notifier = notifier

	// mock
	notifier.On("SendNotification", requestId, "too many tasks: current running tasks: [123]").Return(nil)
	// Create a test task
	taskId := "123"
	taskName := "test"
	task := NewTask(requestId, taskId, taskName, nil)
	// submit the task
	tm.SubmitTask(task)

	// Pause the task
	tm.PauseTask(taskId)

	// Verify that the task status is updated to PAUSE
	if task.status.status != api.PAUSE {
		t.Errorf("Expected task status to be PAUSE, got %s", task.status.status)
	}
}

type MockNotifier struct {
	mock.Mock
}

func (m *MockNotifier) SendNotification(clue, message string) error {
	args := m.Called(clue, message)
	return args.Error(0)
}

func TestTaskManager_SubmitTask_TooManyTasks(t *testing.T) {
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)

	requestId := "aaa888"

	// Initialize TaskManager
	tm := NewTaskManager()
	notifier := &MockNotifier{}
	tm.Notifier = notifier

	// mock
	errMsg := "too many tasks: current running tasks: [123]. pending task: 124"
	notifier.On("SendNotification", requestId, errMsg).Return(nil)

	// Create a test task
	taskId := "123"
	taskName := "test"
	task := NewTask(requestId, taskId, taskName, nil)

	// Submit the first task
	err := tm.SubmitTask(task)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	// Create another test task
	taskId2 := "124"
	task2 := NewTask(requestId, taskId2, taskName, nil)

	// Submit the second task, should trigger the too many tasks error
	err = tm.SubmitTask(task2)
	if err == nil {
		t.Fatalf("Expected error, got nil")
	}
	if err.Error() != errMsg {
		t.Errorf("Expected error message '%s', got %v", errMsg, err.Error())
	}

	// 移除首个任务,模拟任务执行完成,再重新提交任务2,是否能正常提交
	tm.releaseTask(task)

	// Submit the second task, should trigger the too many tasks error
	err = tm.SubmitTask(task2)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
}

func TestTaskManager_SkipTask(t *testing.T) {
	type args struct {
		url  string
		body string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "testSkipTask",
			args: args{
				url: "http://localhost:9188/api/v1/platform/start-task",
				body: `{
    "type": 2,
    "taskInfo": "{\"taskId\":\"10242116\",\"startStepName\":\"step3: hot-reload\",\"reportUrl\":\"http://pre-dip-brain.shizhuang-inc.com/engine/task/status/report\",\"callbackUrl\":\"http://pre-dip-brain.shizhuang-inc.com/engine/schedule/agent/reportTask\",\"templateName\":\"hot-reload\",\"indexName\":\"community_ad_ct_hot_group_pool_v4_scorea\",\"indexVersion\":\"202412161520\",\"dbPath\":\"/app/rel/data/cdb\",\"compress\":true,\"datasource\":{\"type\":2,\"info\":\"{\\\"accessKey\\\":\\\"LTAI5**************hVPhi\\\",\\\"secretKey\\\":\\\"xYEin2GGewbiTrmZlIZCo4ZaVfOAB4\\\",\\\"endpoint\\\":\\\"http://oss-cn-hangzhou-internal.aliyuncs.com\\\",\\\"bucket\\\":\\\"algo-recommend\\\",\\\"dir\\\":\\\"dip_new/community_ad_ct_hot_group_pool_v4_scorea/202412161520/cengine/pre/1/default/1/0\\\",\\\"concurrency\\\":8,\\\"limitSpeed\\\":false,\\\"speed\\\":52428800}\"},\"checkSum\":true,\"checksumFileName\":\"checksum.json\",\"incWaitTimeoutSeconds\":3600,\"incOffsetThreshold\":120,\"diskUsageThreshold\":93,\"memoryUsageThreshold\":150,\"memoryInflateFactor\":5,\"isInc\":false,\"isDoubleVersion\":false,\"needCommit\":true,\"needDumpCheck\":false,\"isVersionEqualCover\":true}"
}`,
			},
			wantErr: false,
		},
	}
	log.InitDefaultLogger()
	json.SetDefaultEngine(sonic.Name)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("POST", tt.args.url, bytes.NewBuffer([]byte(tt.args.body)))
			if err != nil {
				t.Errorf("Error creating request: %v", err)
				return
			}
			req.Header.Set("Content-Type", "application/json")

			// Send the request
			client := &http.Client{}
			resp, err := client.Do(req)
			if err != nil {
				t.Errorf("Error sending request: %v", err)
				return
			}
			defer resp.Body.Close()

			// Check response
			if resp.StatusCode != http.StatusOK {
				t.Errorf("Received non-OK response: %s", resp.Status)
				return
			} else {
				log.Info("Request sent successfully")
			}
		})
	}
}
