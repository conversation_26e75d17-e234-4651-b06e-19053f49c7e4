package exec

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/notify"
	"dip-agent/pkg/persistence"
	"dip-agent/pkg/platform"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/retry"
	ut "dip-agent/pkg/util/time"
	"fmt"
	"github.com/pkg/errors"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"time"
)

const (
	maxTasks          = 8
	defaultNumWorkers = 1
	maxFinishTasks    = 2 * maxTasks
	maxPersistTasks   = 4 * maxTasks

	//defaultCallbackUrl = "/engine/task/status/report"
	defaultCallbackUrl = "/engine/schedule/agent/reportTask"
)

var globalTaskManager *TaskManager

func Init() {
	globalTaskManager = NewTaskManager()
}

func SubmitTaskWith(requestId, taskId, taskName string, carrier cfg.CommonCfg) (*Task, error) {
	t := NewTask(requestId, taskId, taskName, carrier)
	return t, globalTaskManager.SubmitTask(t)
}

func SubmitTaskStartFrom(requestId, taskId, taskName string, carrier cfg.CommonCfg, startStepName string) (*Task, error) {
	t := NewTaskWithStartStep(requestId, taskId, taskName, carrier, startStepName)
	return t, globalTaskManager.SubmitTask(t)
}

func SubmitTask(t *Task) error {
	return globalTaskManager.SubmitTask(t)
}

func StopTask(taskId string) {
	globalTaskManager.StopTask(taskId)
}

func PauseTask(taskId string) {
	globalTaskManager.PauseTask(taskId)
}

func ResumeTask(taskId string) error {
	return globalTaskManager.ResumeTask(taskId)
}

func GetTaskStatus(taskId string) (persistence.TaskStatusUpdate, bool) {
	return globalTaskManager.GetTaskStatus(taskId)
}

func GetAllTaskStatus() map[string]*Task {
	return globalTaskManager.GetAllTaskStatus()
}

type TaskManager struct {
	done        chan struct{}
	tasks       map[string]*Task // 正在执行的任务
	mu          sync.Mutex
	queue       chan *Task
	numWorkers  int
	wg          sync.WaitGroup
	finishTasks map[string]*Task // 已经结束的任务, 保留最近的一定数量的任务

	httpClient *util.HttpClient
	notify.Notifier
}

func NewTaskManager() *TaskManager {
	tm := &TaskManager{
		done:        make(chan struct{}),
		tasks:       make(map[string]*Task),
		queue:       make(chan *Task, maxTasks),
		numWorkers:  defaultNumWorkers,
		finishTasks: make(map[string]*Task, maxFinishTasks),
		// 目前平台的响应code为0表示成功
		httpClient: util.NewHttpClientWithSuccessCode(30*time.Second, []string{"0"}),
		Notifier:   platform.NewWarnRequestWithMsg("", ""),
	}
	tm.Start()
	return tm
}

// GetTaskStatus 获取任务最新状态
func (m *TaskManager) GetTaskStatus(taskId string) (persistence.TaskStatusUpdate, bool) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if t, exists := m.tasks[taskId]; exists {
		return t.status.ToStatusUpdate(), exists
	}

	// 尝试去完成任务中查找
	for _, ft := range m.finishTasks {
		if ft.id == taskId {
			return ft.status.ToStatusUpdate(), true
		}
	}

	// 尝试查db
	if statusUpdate, err := persistence.Load(taskId); err == nil {
		return statusUpdate, true
	}
	return persistence.TaskStatusUpdate{}, false
}

// GetAllTaskStatus 获取所有任务状态
func (m *TaskManager) GetAllTaskStatus() map[string]*Task {
	m.mu.Lock()
	defer m.mu.Unlock()

	statusMap := make(map[string]*Task)
	for _, t := range m.tasks {
		statusMap[t.id] = t
	}
	for _, ft := range m.finishTasks {
		statusMap[ft.id] = ft
	}
	return statusMap
}

// UpdateTaskStatus 更新任务状态
func (m *TaskManager) UpdateTaskStatus(taskId string, update *TaskStatus) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if t, exists := m.tasks[taskId]; exists {
		t.status = update
	}
}

// SubmitTask 提交任务
func (m *TaskManager) SubmitTask(t *Task) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	return m.submitTask(t)
}

func (m *TaskManager) submitTask(t *Task) error {
	// 前置校验
	// 任务是否已经存在
	if existTask, exists := m.tasks[t.id]; exists {
		// 如果存在, 判断是否处于暂停中, 如果是, 则恢复执行
		if existTask.status.status == api.PAUSE {
			resumeFormPause(existTask)
			log.Info("[%s]submit task resumed from pause: %s", t.ctx.RequestId, t.id)
			return nil
		}
		return errors.Errorf("task already exists: %s", t.id)
	}

	// 2024-12-10: 存在运行中的任务, 报警. 目前的设定是更新任务只能串行执行, 上个更新任务没有结束, 下个更新任务不能提交到agent
	if len(m.tasks) > 0 {
		runningTaskIds := make([]string, 0, len(m.tasks))
		for id := range m.tasks {
			runningTaskIds = append(runningTaskIds, id)
		}
		errMsg := fmt.Sprintf("too many tasks: current running tasks: %v. pending task: %s", runningTaskIds, t.id)
		// 发送报警
		err := m.SendNotification(t.ctx.RequestId, errMsg)
		if err != nil {
			return errors.WithMessagef(err, "send warn msg fail: %s", errMsg)
		}
		return errors.New(errMsg)
	}

	// 任务是否超过最大限制
	if len(m.tasks) >= maxTasks {
		return errors.Errorf("too many tasks. current: %d, threshold: %d. pending task: %s", len(m.tasks), maxTasks, t.id)
	}

	// 如果存在相同taskId失败的任务, 尝试重设开始步骤
	if finishTask, exists := m.finishTasks[t.id]; exists && finishTask.status.status == api.FAIL {
		log.Info("[%s]submit task exist. try to reset start step: %s", t.ctx.RequestId, t.id)
		t.resetStartStep(finishTask)
	}

	// 更新任务状态
	t.status.status = api.PENDING
	t.status.start = time.Now()
	t.start = time.Now()
	m.tasks[t.id] = t

	// 提交任务
	select {
	case <-m.done:
		return errors.Errorf("TaskManager is stopped: %s", t.id)
	case m.queue <- t:
	}
	return nil
}

func (m *TaskManager) canExec(t *Task) (bool, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 任务是否已经存在
	if existTask, exists := m.tasks[t.id]; exists {
		// 如果存在, 判断是否处于暂停中, 如果是, 则恢复执行
		if existTask.status.status == api.PAUSE {
			resumeFormPause(existTask)
			return false, nil
		}
		return false, errors.Errorf("task already exists: %s", t.id)
	}

	// 任务是否超过最大限制
	if len(m.tasks) >= maxTasks {
		return false, errors.Errorf("too many tasks: %d, threshold: %d", len(m.tasks), maxTasks)
	}

	// 更新任务状态
	t.status.status = api.PENDING
	t.status.start = time.Now()
	m.tasks[t.id] = t

	return true, nil
}

// StopTask 终止任务. 支持任务多个步骤之间响应终止
// TODO 可能造成后续步骤无法执行而造成引擎服务异常(例如重启更新期间被终止, 导致服务迟迟没有被拉起)
func (m *TaskManager) StopTask(taskId string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if t, exists := m.tasks[taskId]; exists {
		t.status.status = api.STOP
		t.cancel()
		// 任务响应cancel后, 会调用releaseTask()释放资源
		log.Info("[%s]task stopped: %s", t.ctx.RequestId, taskId)
	}
}

// PauseTask 暂停任务
func (m *TaskManager) PauseTask(taskId string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if t, exists := m.tasks[taskId]; exists {
		// 如果任务已经停止, 则不能暂停
		if t.status.status == api.STOP {
			log.Info("[%s]task already stopped, can't pause: %s", t.ctx.RequestId, taskId)
			return
		}

		t.status.status = api.PAUSE
		t.status.pause = time.Now()
		// 触发任务暂停
		t.pause()
		log.Info("[%s]task paused: %s", t.ctx.RequestId, taskId)
	}
}

// ResumeTask 恢复任务
func (m *TaskManager) ResumeTask(taskId string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	return m.resumeTask(taskId)
}

// 由于go中的锁是不可重入的, 所以这里需要单独提取出来. 调用时需要在外层加锁
func (m *TaskManager) resumeTask(taskId string) error {
	if t, exists := m.tasks[taskId]; exists {
		// 如果任务处于运行中, 不用恢复
		if t.status.status == api.RUNNING {
			log.Info("[%s]task already running, can't resume: %s", t.ctx.RequestId, taskId)
			return nil
		}

		// 如果处于暂停状态, 则恢复
		if t.status.status == api.PAUSE {
			resumeFormPause(t)
			log.Info("[%s]task resumed from pause: %s", t.ctx.RequestId, taskId)
			return nil
		}

		// 任务处于异常状态
		return errors.Errorf("task statue is %s, can't resume: %s", t.status.status.String(), taskId)
	}

	// 从finishTasks中查找
	for _, ft := range m.finishTasks {
		if ft.id == taskId {
			// 如果任务已经停止, 则不再恢复
			if ft.status.status == api.STOP {
				log.Info("[%s]task already stopped, can't resume: %s", ft.ctx.RequestId, taskId)
				return errors.Errorf("task already stopped, can't resume: %s", taskId)
			}

			// 如果任务处于失败状态, 则从失败状态恢复
			if ft.status.status == api.FAIL {
				// 2024-12-16 已经失败的任务不能从内存中直接拉起重跑, 需要上游重新提交任务. 因为上下文\carrier可能已经变化导致不可预知的问题
				log.Info("[%s]task already failed, can't resume: %s", ft.ctx.RequestId, taskId)
				return errors.Errorf("task already failed, can't resume: %s", taskId)
				//// 重新提交任务
				//log.Info("[%s]task resumed from fail: %s", ft.ctx.RequestId, taskId)
				//// note: 这里不能直接提交老任务对象,因为里面包含的ctx\chan可能已经关闭
				//nt := NewTask(ft.ctx.RequestId, taskId, ft.taskName, ft.ctx.Carrier)
				//return m.submitTask(nt)
			}
			break
		}
	}

	return errors.Errorf("task not found: %s", taskId)
}

func resumeFormPause(t *Task) {
	t.status.status = api.RUNNING
	t.status.resume = time.Now()
	// 触发任务恢复
	t.resume()
}

func (m *TaskManager) releaseTask(t *Task) {
	m.mu.Lock()
	// 删除缓存的任务
	// 过期的完成任务
	var expiredTask *Task
	_, exists := m.tasks[t.id]
	if exists {
		delete(m.tasks, t.id)
		// 保存到finishTasks
		m.finishTasks[t.id] = t
		// 移除过期的任务
		fl := len(m.finishTasks)
		if fl > maxFinishTasks {
			// 移除较早的任务
			// 获取结束时间最早的任务移除
			// 由于map是无序的, 这里需要遍历查找
			var minTask *Task
			for _, ft := range m.finishTasks {
				if minTask == nil || ft.status.end.Before(minTask.status.end) {
					minTask = ft
				}
			}
			// 需要持久化的任务
			expiredTask = minTask
		}
	}
	m.mu.Unlock()

	if exists {
		// 确保release前结果已经写入result chan
		close(t.result)
		// 确保任务执行结束
		t.cancel()

		// 持久化到db
		if expiredTask != nil {
			taskStatus := persistence.TaskStatusUpdate{
				Status:      expiredTask.status.status.String(),
				StartTime:   ut.FormatDefault(expiredTask.status.start),
				ProcessTime: ut.FormatDefault(expiredTask.status.process),
				EndTime:     ut.FormatDefault(expiredTask.status.end),
			}
			err := persistence.Save(expiredTask.id, taskStatus)
			if err != nil {
				log.Error("save task status error: %+v", err)
			}
		}
	}
}

func (m *TaskManager) Stop() {
	// 取消所有任务
	m.mu.Lock()
	for _, t := range m.tasks {
		t.cancel()
	}
	m.mu.Unlock()

	// 给所有worker发送关闭信号
	close(m.done)
	m.wg.Wait()

	log.Info("TaskManager stopped")
}

func (m *TaskManager) Start() {
	for i := 0; i < m.numWorkers; i++ {
		m.wg.Add(1)
		go m.worker(i)
	}
}

func (m *TaskManager) worker(index int) {
	log.Info("manage worker %d started", index)
	defer func() {
		m.wg.Done()
		log.Info("manage worker %d stopped", index)
	}()
	for {
		select {
		case <-m.done:
			return
		case t := <-m.queue:
			m.runTask(t)
		}
	}
}

func (m *TaskManager) runTask(t *Task) {
	// 更新任务状态
	t.status.status = api.RUNNING
	t.status.process = time.Now()
	m.UpdateTaskStatus(t.id, t.status)

	// recover task process
	defer func() {
		if err := recover(); err != nil {
			buf := make([]byte, 2048)
			n := runtime.Stack(buf, false) // Set 'all' to false to get only the running goroutine's stack
			log.Error("[%s]task process panic: %v\n%s. stack: %s", t.id, err, buf[:n], string(debug.Stack()))
			panicErr := errors.Errorf("[%s]task process panic: %+v", t.id, err)
			tr := api.SystemFail[string](panicErr)

			// note: 这里不能直接调用finallyTask, 因为可能是finallyTask导致的panic, 避免循环panic
			go m.finallyTask(t, tr)
		}
	}()

	// 执行任务
	r := t.exec()

	// 任务执行结束处理
	m.finallyTask(t, r)
}

func (m *TaskManager) finallyTask(t *Task, r api.Result) {
	// 由于result chan容量是1，不会阻塞
	t.result <- r

	// 更新任务状态
	t.status.status = r.Status()
	t.status.end = time.Now()

	// 释放任务
	m.releaseTask(t)

	// 是否上报结果
	if t.NoCallback {
		log.Info("[%s]task no callback: %s", t.ctx.RequestId, t.id)
		return
	}
	// 回调上报执行结果
	go m.reportTaskResult(t, r)
}

func (m *TaskManager) reportTaskResult(t *Task, r api.Result) {
	callbackUrl := t.ctx.Carrier.GetString("callbackUrl")
	if callbackUrl == "" {
		callbackUrl = api.BrainDomain + defaultCallbackUrl
	}
	requestId := t.ctx.RequestId
	log.Info("[%s]report task result url: %s, param: %#v", requestId, callbackUrl, r)
	// TODO 目前只有一个任务执行协程, 后续如果增加, 这里需要做同步
	m.httpClient.SetClue(requestId)
	// 构建请求数据
	ar := api.FromResult[string](r)
	ar.ResponseData = t.id
	ar.RequestId = requestId
	ar.Cost = time.Since(t.start).Milliseconds()
	// 由于目前平台的执行结果只展示response message, 所以这里将requestId拼接到response message中
	if !(strings.Contains(ar.ResponseMessage, "requestId") && strings.Contains(ar.ResponseMessage, requestId)) {
		ar.ResponseMessage = fmt.Sprintf("%s[requestId: %s]", ar.ResponseMessage, requestId)
	}
	// 重试上报
	err := retry.DoRetry(func() error {
		success, resp, err := m.httpClient.PostWithResponseAndIsSuccess(callbackUrl, ar, nil)
		if err != nil {
			return err
		}
		if !success {
			log.Error("[%s]report task result error: %s", requestId, resp)
		}
		return nil
	})
	if err != nil {
		log.Error("[%s]report task result error: %+v", requestId, err)
	}
}
