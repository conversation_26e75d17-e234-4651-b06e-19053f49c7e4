package platform

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/retry"
	"time"

	"github.com/pkg/errors"
)

// 获取集群配置
const (
	clusterConfigPath = "/cluster/agent/config"
)

type GetClusterConfigRequest struct {
	ClusterCode  string `json:"clusterCode" validate:"required"`  // 必填. 集群编码
	ClusterGroup string `json:"clusterGroup" validate:"required"` // 必填. 集群组

	requestId  string
	httpClient *util.HttpClient
}

type GetClusterConfigResponse cfg.CommonCfg

func NewGetClusterConfigRequest(requestId string) *GetClusterConfigRequest {
	hc := util.NewHttpClient(30 * time.Second)
	hc.SetClue(requestId)
	return &GetClusterConfigRequest{
		ClusterCode:  api.ClusterCode,
		ClusterGroup: api.ClusterGroup,

		requestId:  requestId,
		httpClient: hc,
	}
}

func (r *GetClusterConfigRequest) Send() (*GetClusterConfigResponse, error) {
	url := api.BackendDomain + clusterConfigPath
	url += "?clusterCode=" + r.ClusterCode + "&clusterGroup=" + r.ClusterGroup

	var (
		err  error
		resp GetClusterConfigResponse
	)
	err = r.httpClient.GetWithResponse(url, nil, &resp)
	if err != nil {
		err = errors.WithMessagef(err, "call dip-backend api fail: %s", clusterConfigPath)
	}
	return &resp, err
}

func (r *GetClusterConfigRequest) SendWithRetry() (*GetClusterConfigResponse, error) {
	var (
		err  error
		resp *GetClusterConfigResponse
	)
	err = retry.DoRetryWithClue(func() error {
		resp, err = r.Send()
		if err != nil {
			return err
		}
		return nil
	}, r.requestId)
	return resp, err
}
