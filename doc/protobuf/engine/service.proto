syntax = "proto3";

package engine;
option cc_generic_services = true;
option go_package = "dip-agent/doc/protobuf/engine";
option java_multiple_files = true;
option java_package = "com.shizhuang.duapp.dip.agent.engine";
option java_outer_classname = "EngineApiProto";

import "brpc/options.proto";
import "engine/schema.proto";

/**
 * 接口服务的总体原则:
 * 1. 请求和响应的Content-Type为application/json
 */
service ApiService {

  /**
   * 索引热更新
   * path: /api/v1/index/hot-reload
   */
  rpc HotReload(HotReloadRequest) returns (EmptyResponse){
    option (brpc.method_timeout) = 60000;
  };

  /**
   * 索引提交
   * path: /api/v1/index/commit
   */
  rpc Commit(CommitRequest) returns (EmptyResponse);

  /**
   * 索引回滚
   * path: /api/v1/index/rollback
   */
  rpc Rollback(RollbackRequest) returns (EmptyResponse);

  /**
   * 服务下线
   * path: /api/v1/service/offline
   */
  rpc Offline(EmptyRequest) returns (EmptyResponse);

  /**
   * 服务qps
   * path: /api/v1/service/qps
   */
  rpc Qps(EmptyRequest) returns (QpsResponse);

  /**
   * 获取索引追增量状态. 用于重启服务后等待追增量完成
   * path: /api/v1/index/inc-status
   */
  rpc IncStatus(IncStatusRequest) returns (IncStatusResponse);

  /**
   * 索引预热
   * path: /api/v1/index/warmup
   */
  rpc Warmup(WarmupRequest) returns (EmptyResponse);

  /**
   * 服务上线
   * path: /api/v1/service/online
   */
  rpc Online(EmptyRequest) returns (EmptyResponse);

  /**
   * 获取索引信息
   * path: /api/v1/index/info
   */
  rpc IndexInfo(IndexInfoRequest) returns (IndexInfoResponse);

  /**
   * 获取服务状态
   * path: /api/v1/service/status
   */
  rpc ServiceStatus(EmptyRequest) returns (ServiceStatusResponse);

  /**
   * 卸载索引
   * path: /api/v1/index/unload
   */
  rpc UnloadIndex(UnloadIndexRequest) returns (EmptyResponse);

  /**
   * TODO 数据查询
   */

  /**
   * TODO inc_status\press_log?
   */

  /**
   * 获取模型信息
   */
  rpc ModelInfo(ModelInfoRequest) returns (ModelInfoResponse);

  /**
   * 热更新模型
   */
  rpc HotReloadModel(HotReloadModelRequest) returns (EmptyResponse);

  /**
   * 卸载模型
   */
  rpc UnloadModel(UnloadModelRequest) returns (EmptyResponse);

  /**
   * 查询卸载索引是否完成
   */
  rpc CheckUnload(CheckUnloadRequest) returns (CheckUnloadResponse);

}