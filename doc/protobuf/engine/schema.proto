syntax = "proto3";

import "google/protobuf/any.proto";

package engine;
option cc_generic_services = true;
option go_package = "dip-agent/doc/protobuf/engine";
option java_multiple_files = true;
option java_package = "com.shizhuang.duapp.dip.agent.engine";

// 基础响应结构
message BaseResponse {
  int32 code = 1; // 必填. 响应编码. 200表示成功，其他表示错误/异常, 需要补全message和error_details
  string message = 2; // 可选. 用户友好展示信息. 例如"执行成功","参数name不能为空"
  ErrorDetails error_details = 3; // 可选. 错误详细信息
}

// 错误详细信息
message ErrorDetails {
  ErrorType error_type = 1; // 必填. 错误类型枚举
  string detailed_message = 2; // 可选. 详细错误信息
  string trace = 3; // 可选. 错误追踪信息，例如堆栈跟踪
}

// 异常类型枚举
enum ErrorType {
  UNKNOWN_EXCEPTION = 0; // 未知异常
  DATA_EXCEPTION = 1; // 数据异常. 例如源表数据类型不匹配\源表数据缺失等
  SYSTEM_EXCEPTION = 2; // 系统异常
  NETWORK_EXCEPTION = 3; // 网络异常
  CONFIGURATION_EXCEPTION = 4; // 配置错误
  PERMISSION_EXCEPTION = 5; // 权限问题
  PARAMETER_EXCEPTION = 6; // 参数错误. 例如参数为空、参数格式错误等
}

// 响应数据定义，支持多种数据类型
message Data {
  oneof data_type {
    EmptyResponse empty_response = 2; // 空响应
  }
}

// 通用响应
message CommonResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  google.protobuf.Any data = 2; // 可选. 响应数据. 根据实际需要使用具体的消息类型
}

// 空响应
message EmptyResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
}

// 空请求
message EmptyRequest {}

// 索引基础信息
message IndexBaseInfo {
  string index_name = 1; // 必填. 索引名称
  string index_version = 2; // 必填. 索引版本
}

// 热更新请求
message HotReloadRequest {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
}

// 索引提交请求
message CommitRequest {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
}

// 索引回滚请求
message RollbackRequest {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
}

// qps响应
message QpsResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  int32 qps = 2; // 必填. qps
}

// 查询索引增量信息请求
message IncStatusRequest {
  // 索引信息. 如果为空，则查询所有索引的增量信息
  repeated IndexBaseInfo indexes = 1; // 必填. 索引基础信息
}

// 查询索引增量信息响应
message IncStatusResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  repeated IndexIncInfo index_inc_infos = 2; // 必填. 索引增量信息
}

// 索引的增量信息
message IndexIncInfo {
  string index_name = 1; // 必填. 索引名称
  string index_version = 2; // 必填. 索引版本
  bool eof = 3; // 必填. 是否已经消费到末尾
  int64 time_offset = 4; // 必填. 偏移量
  int64 total_message = 5; // 总消费消息数
  int64 last_offset = 6; // 上次消费的偏移量
  int64 last_timestamp = 7; // 上次消费的时间戳
  string topic_name = 8; // topic名称
  string last_create_time = 9; // 上次消费的创建时间
  string index_source = 10; // 索引来源. 例如dgraph中的formal、buffer
  string brokers = 11; // broker地址, 格式为ip1:port1,ip2:port2
}

// 索引预热请求
message WarmupRequest {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
}

// 获取索引信息请求
message IndexInfoRequest {
  // 索引基础信息. 如果为空，则查询所有索引的信息
  repeated IndexBaseInfo indexes = 1; // 必填. 索引基础信息
}

// 获取索引信息响应
message IndexInfoResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  repeated IndexInfo index_infos = 2; // 必填. 索引信息
}

// 索引信息
message IndexInfo {
  string index_name = 1; // 必填. 索引名称
  string index_version = 2; // 必填. 索引版本
  int64 memory_bytes = 3; // 必填. 索引占用的内存大小
  string index_source = 4; // 索引来源. 例如dgraph中的formal、buffer
  string index_type = 5; // 索引类型
  int32 index_qps = 6; // 索引qps
  string index_load_time = 7; // 索引加载完成的时间, 格式化为yyyy-MM-dd HH:mm:ss
  int64 doc_count = 8; // 索引的文档/条目数量
  bool is_latest = 9; // 是否是最新索引
  string key_id = 10; // 索引的key_id
  map<string, string> properties = 11; // 索引属性
}

// 服务状态响应
message ServiceStatusResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  ServiceStatus service_status = 2; // 必填. 服务状态
}

// 服务状态
message ServiceStatus {
  bool online = 1; // 必填. 服务是否在线
  string engineBinaryVersion = 2; // 必填. 引擎二进制版本
  string engineBuildVersion = 3; // 引擎构建版本. 例如git commit id
  ServiceExtraInfo extra_info = 4; // 额外信息
  map<string, string> properties = 5; // 服务属性
}

message ServiceExtraInfo {
  string kv_binary_version = 1; // kv二进制版本
  string kvv_binary_version = 2; // kvv二进制版本
  string invert_binary_version = 3; // invert二进制版本
  string disk_kv_binary_version = 4; // disk_kv二进制版本
  string dense_kv_binary_version = 5; // dense_kv二进制版本
}

// 卸载索引请求
message UnloadIndexRequest {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
}

// #################### 索引构建部分的定义 ####################

// 索引构建请求
message BuildIndexRequest {
  // 数据目录: 构建的源表数据路径
  string data_path = 1;
  // 日志目录: 构建的日志输出路径
  string log_path = 2;
  // 索引目录: 构建的索引输出路径
  string db_path = 3;
  // 索引定义. 必填
  IndexDef index_def = 4;
  // 分段数量
  int32 segment_num = 5;
  // 每个分段的数据文件数量
  repeated int32 segment_file_count = 6;
}

// 索引定义
message IndexDef {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
  // 索引类型. 必填
  string index_type = 3;
  // 主键字段名称. 必填
  string key_field_name = 4;
  // 主键字段类型. 必填
  string key_field_type = 5;
  // 是否双版本
  bool is_double_version = 6;
  // 是否增量索引. 必填
  bool is_inc = 7;
  // 增量配置
  IncConfig inc_config = 8;
  // 文件配置
  FileConfig file_config = 9;
  // 倒排截断最大数
  int32 max_row_num = 10;
  // 索引字段
  repeated FieldSchema fields = 11;
  // 索引额外配置
  map<string, string> extra_config = 12;
}

// 增量配置
message IncConfig {
  // topic. 必填
  string topic = 1;
  // broker地址. 必填
  string brokers = 2;
  // mq类型: kafka, datahub, rabbitmq. 必填
  string inc_type = 3;
  // 消费的开始时间戳, 必填
  int64 start_timestamp = 4;
  // 是否忽略增量表名
  bool ignore_message_table_name = 5;
}

// 文件配置
// TODO 注意设置默认值
message FileConfig {
  // 倒排大小
  int32 inverted_count = 1;
  // 正排大小
  int32 count = 2;
  // 行分隔符
  string line_flag = 3;
}

// 字段定义
message FieldSchema {
  // 字段名称
  string name = 1;
  // 字段别名
  string index_alias_name = 2;
  // 字段类型
  string type = 3;
  // is_ws -1:否,1是
  bool is_ws = 4;
  // 是否数组 -1:否,1是
  bool is_array = 5;
  bool is_default_need = 6;
  // is_index -1:否,1是
  bool is_index = 7;
  int32 array_len = 8;
  bool is_default_value_available = 9;
  string default_value = 10;
  string index_type = 11;
  // 索引参数
  FieldIndexParam index_params = 12;
  // 序列最大长度
  int32 array_max_len = 13;
  // 精密度阀值
  int32 slop = 14;
  // 词频
  bool is_tf = 15;
  // 前缀索引
  bool is_prefix = 16;
  // 后缀索引
  bool is_postfix = 17;
  // 位置
  bool is_position = 18;
  // 偏移
  bool is_offset = 19;
  // string输出
  bool is_to_string = 20;
  // 磁盘存储
  bool is_disk = 21;
  // 备注
  string remark = 22;
  // 字段额外配置
  string config = 23;
}

// 字段索引参数
message FieldIndexParam {
  int32 min_nprobe = 1;
  int32 max_nprobe = 2;
  int32 k = 3;
  string type = 4;
  int32 max_num = 5;
  bool centrol_norm = 6;
  int32 M = 7;
  int32 ef_construction = 8;
  int32 d = 9;
  bool hnsw_v2 = 10;
  bool full_space = 11;
  string sub_space_field_name = 12;
  int32 sub_space_num = 13;
  bool per_segment = 14;
}

// 索引构建响应. 对应build-result.json
message BuildIndexResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  IndexBuildResult build_result = 2; // 必填. 索引构建结果
}

// 索引构建结果
message IndexBuildResult {
  // 构建的索引大小, 必填
  int64 index_total_bytes = 1;
  // 构建的索引文档数, 必填
  int64 index_total_count = 2;
  int64 fail_doc_count = 3;
  // 构建耗时, 单位: 毫秒, 必填
  int64 build_cost_ms = 4;
  // 构建的引擎二进制版本, 必填
  string engine_binary_version = 5;
  // 额外信息
  map<string, string> extra_info = 6;
}

// 索引dump结果. 对应dump-result.json
message IndexDumpResult {
  // 拉取的数据条目数量
  int64 count = 1;
  // 拉取的数据大小
  int64 size = 2;
  // 查询的数据条目数量
  int64 query_count = 3;
  // 空条目的数量
  int64 empty_count = 4;
  // 分段数量
  int32 segment_num = 5;
  // 每个分段的数据文件数量
  repeated int32 segment_file_count = 6;
}

// 索引dump请求
message IndexDumpRequest {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
  // 索引类型. 必填
  string index_type = 3;
  // 索引字段. 必填
  repeated FieldSchema fields = 4;
  // 分段数量
  int32 segment = 5;
  // 每个分段的数据文件数量
  repeated int32 segment_file_count = 6;
  // 拉取odps的sql
  string sql = 7;
  // 数据目录
  string data_path = 8;
  // 临时数据目录
  string tmp_data_path = 9;
  // 索引目录
  string db_path = 10;
  // 拉取并行度
  int32 parallel = 11;
  // 单个数据文件的最大字节数
  int32 max_file_size = 12;

  // odps相关配置
  string odpsAccessKey = 14;
  string odpsSecretKey = 15;
  string odpsProject = 16;
  string odpsEndpoint = 17;

  // 索引额外配置
  map<string, string> extra_config = 23;
}

// 查询卸载索引是否完成
message CheckUnloadRequest {
  // 索引名称. 必填
  string index_name = 1;
  // 索引版本. 必填
  string index_version = 2;
}

message CheckUnloadResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  bool is_unloaded = 2; // 必填. 是否卸载完成
}

// ############### 模型相关的定义 ###############

// 模型基础信息
message ModelBaseInfo {
  string model_name = 1; // 必填. 模型名称
  string model_version = 2; // 必填. 模型版本
}

// 获取模型信息请求
message ModelInfoRequest {
  repeated ModelBaseInfo models = 1; // 模型基础信息. 如果为空，则查询所有模型的信息!!!
}

// 获取模型信息响应
message ModelInfoResponse {
  BaseResponse header = 1; // 必填. 基础响应结构
  repeated ModelInfo model_infos = 2; // 必填. 模型信息
}

// 模型信息
message ModelInfo {
  string model_name = 1; // 必填. 模型名称
  string model_version = 2; // 必填. 模型版本
  int64 memory_bytes = 3; // 必填. 模型占用的内存大小
  string model_source = 4; // 模型来源. 例如dgraph中的formal、buffer
  string model_type = 5; // 模型类型
  int32 model_qps = 6; // 模型qps
  string model_load_time = 7; // 模型加载完成的时间, 格式化为yyyy-MM-dd HH:mm:ss
  int64 doc_count = 8; // 模型的文档/条目数量
  bool is_latest = 9; // 是否是最新模型
  map<string, string> properties = 10; // 模型额外属性
}

// 热更新模型请求
message HotReloadModelRequest {
  // 模型名称. 必填
  string model_name = 1;
  // 模型版本. 必填
  string model_version = 2;
}

// 卸载模型请求
message UnloadModelRequest {
  // 模型名称. 必填
  string model_name = 1;
  // 模型版本. 必填
  string model_version = 2;
}
